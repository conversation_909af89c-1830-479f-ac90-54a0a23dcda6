import Sessions from "utils/Sessions";
import { createAsyncThunk } from "@reduxjs/toolkit";
import ApiService from "redux/ApiService/ApiService";
import { filterProjectStatusFunc, normalizeParamsAndAddValues } from "utils/methods/methods";

const getQhseDashboardData = createAsyncThunk("qhseDashboard/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`safety-cards/count?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res;
});

export const getRiskOfIncidents = createAsyncThunk("risk-of-inicidents/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`safety-cards/risk-of-incident-count?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res;
});

export const getTypeOfIncidents = createAsyncThunk("type-of-inicidents/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`safety-cards/type-of-incident-count?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res;
});

export default getQhseDashboardData;

import { useEffect, useState } from "react";

// Custom Components
import MDTypography from "components/MDTypography";
import MDBox from "components/MDBox";

// MUI Components
import Icon from "@mui/material/Icon";

// Constants
import { Icons } from "utils/Constants";

export default function TeamData(
  teamList,
  handleOpenNewModal,
  setModalType,
  editLists,
  setEditLists,
  handleDelete,
  handleTeamSort,
  sorted,
  permission
) {
  const [rows, setRows] = useState([]);
  const mongooseId = "_id";

  const handleEdit = (item) => {
    setModalType("Update");
    setEditLists({ ...editLists, team: item });
    handleOpenNewModal("Team");
  };

  useEffect(() => {
    if (teamList) {
      const list = teamList?.map((item) => {
        const temp = {
          teamsWfmName: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.teamsWfmName}
            </MDTypography>
          ),
          sortOrder: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.sortOrder}
            </MDTypography>
          ),
          action: (
            <MDBox>
              {permission?.update && (
                <Icon
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleEdit(item)}
                  sx={{ cursor: "pointer" }}
                >
                  {Icons.EDIT}
                </Icon>
              )}{" "}
              &nbsp;
              {permission?.delete && (
                <Icon
                  color="secondary"
                  fontSize="medium"
                  sx={{ cursor: "pointer" }}
                  onClick={() => handleDelete("Team", item[mongooseId])}
                >
                  {Icons.DELETE}
                </Icon>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [teamList, permission]);

  return {
    teamColumns: [
      {
        Header: () => (
          <div
            onClick={handleTeamSort}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Enter" && handleTeamSort()}
            style={{ cursor: "pointer" }}
          >
            Teams WFM Name
            <MDBox
              position="absolute"
              top={-3}
              left="20%"
              color={sorted === "asc" ? "text" : "secondary"}
              opacity={sorted === "asc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_up</Icon>
            </MDBox>
            <MDBox
              position="absolute"
              top={3}
              left="20%"
              color={sorted === "desc" ? "text" : "secondary"}
              opacity={sorted === "desc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_down</Icon>
            </MDBox>
          </div>
        ),
        accessor: "teamsWfmName",
        width: "90%",
        align: "left",
      },
      {
        Header: () => (
          <div
            onClick={handleTeamSort}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Enter" && handleTeamSort()}
            style={{ cursor: "pointer" }}
          >
            Sort Order
            <MDBox
              position="absolute"
              top={-3}
              left="20%"
              color={sorted === "asc" ? "text" : "secondary"}
              opacity={sorted === "asc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_up</Icon>
            </MDBox>
            <MDBox
              position="absolute"
              top={3}
              left="20%"
              color={sorted === "desc" ? "text" : "secondary"}
              opacity={sorted === "desc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_down</Icon>
            </MDBox>
          </div>
        ),
        accessor: "sortOrder",
        width: "90%",
        align: "left",
      },

      ...(permission?.update || permission?.delete
        ? [{ Header: "Action", accessor: "action", width: "10%", align: "right" }]
        : []),
    ],
    teamRows: rows,
  };
}

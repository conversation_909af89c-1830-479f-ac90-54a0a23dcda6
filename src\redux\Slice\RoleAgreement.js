import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import RoleListThunk, { roleByIdThunk } from "redux/Thunks/Role";
import Constants from "utils/Constants";

const initialState = {
  loading: Constants.IDLE,
  roleLoading: Constants.IDLE,
  list: [],
  roles: [],
};

export const roleAgreementSlice = createSlice({
  name: "roleAgreementSlice",
  initialState,
  reducers: {
    // Reducer to check all rows box row wise and all rows
    updateCheckBox(state, action) {
      const { name, target, roleData } = action.payload;
      const stateData = JSON.parse(JSON.stringify(state));
      const tempRoleCheckBox = stateData.list.map((role) => {
        // Check if role id matches
        if (
          roleData &&
          roleData[Constants.MONGOOSE_ID] &&
          role[Constants.MONGOOSE_ID] === roleData[Constants.MONGOOSE_ID]
        ) {
          if (name === "checkAllRowBox") {
            return {
              ...role,
              agreement: {
                create: target,
                read: target,
                update: target,
                delete: target,
              },
            };
          }
          return {
            ...role,
            agreement: {
              create: name === "create" ? target : role.agreement.create,
              read: name === "read" ? target : role.agreement.read,
              update: name === "update" ? target : role.agreement.update,
              delete: name === "delete" ? target : role.agreement.delete,
            },
          };
        }
        // Check All
        if (name === "checkAllRows") {
          return {
            ...role,
            agreement: {
              create: target,
              read: target,
              update: target,
              delete: target,
            },
          };
        }
        return role;
      });
      return {
        ...state,
        list: tempRoleCheckBox,
      };
    },

    reloadRoleList(state) {
      state.roleLoading = Constants.PENDING;
    },
  },
  extraReducers: {
    [roleByIdThunk.pending]: (state) => {
      if (state.list.length === 0) state.loading = Constants.PENDING;
    },
    [roleByIdThunk.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.list = payload.data.data;
    },
    [roleByIdThunk.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },

    [RoleListThunk.pending]: (state) => {
      if (state.roles.length === 0) state.roleLoading = Constants.PENDING;
    },
    [RoleListThunk.fulfilled]: (state, { payload }) => {
      state.roleLoading = Constants.FULFILLED;
      if (payload.type === "add") state.roles = payload.data.data.rolesData;
      else state.roles.push(...payload.data.data.rolesData);
    },
    [RoleListThunk.rejected]: (state) => {
      state.roleLoading = Constants.REJECTED;
    },
    [resetStateThunk.fulfilled]: (state) => {
      state.loading = Constants.IDLE;
      state.roleLoading = Constants;
      state.list = [];
      state.roles = [];
    },
  },
});

export const { updateCheckBox, reloadRoleList } = roleAgreementSlice.actions;
export default roleAgreementSlice.reducer;

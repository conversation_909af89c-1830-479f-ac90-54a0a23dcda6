/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

// 3rd party library
import PropTypes from "prop-types";
import pxToRem from "assets/theme/functions/pxToRem";

// MUI Components
import { IconButton, Popover } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import Status from "components/Table/Status";
import Author from "components/Table/Author";
import By from "components/Table/By";

// 3rd party library
import moment from "moment";

// Redux
import { useSelector } from "react-redux";

// Constants
import Constants, { Icons, defaultData } from "utils/Constants";
import FontComponent from "components/Responsive/fonts";

import notInListIcon from "assets/images/icons/Shifts/file-excel-line.png";

// Methods
import { getFormattedProjectName } from "utils/methods/methods";

export function ShiftMembers({ memberList }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  return (
    <MDBox>
      <MDBox display="flex" justifyContent="start" alignItems="center">
        <MDBox
          style={{
            flex: 1,
            width: "100%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          <Author name={memberList?.length} />
        </MDBox>
        {memberList?.find((member) => member?.notInList) && (
          <IconButton aria-label="report-info-status" color="info" onClick={handleClick}>
            {Icons.INFO}
          </IconButton>
        )}
      </MDBox>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <MDBox sx={{ p: 1, backgroundColor: "#f5f5f5", width: "300px" }}>
          {memberList
            ?.filter((item) => item?.notInList)
            .map((member) => (
              <MDBox
                key={member?.memberName}
                display="flex"
                justifyContent="start"
                alignItems="center"
              >
                <MDBox
                  style={{
                    marginTop: 1,
                    marginBottom: 1,
                    backgroundColor: "#f5f5f5",
                    fontSize: pxToRem(14),
                    padding: 10,
                  }}
                >
                  <Author name={member?.memberName} />
                </MDBox>
                {member?.notInList && (
                  <MDBox display="flex" justifyContent="center" alignItems="center">
                    <img src={notInListIcon} alt="Not In List" style={{ width: 20, height: 20 }} />
                  </MDBox>
                )}
              </MDBox>
            ))}
        </MDBox>
      </Popover>
    </MDBox>
  );
}

ShiftMembers.propTypes = {
  memberList: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default function DailyShiftData(handleOpenDeleteModal, shiftList) {
  const [rows, setRows] = useState([]);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[5]?.screensInfo?.agreement;
  const fontSize = FontComponent({ sizes: {} });

  const formateDuration = (duration) => {
    const [hours, minutes] = duration.split(":");
    const formattedHours = hours ? `${hours} hrs` : "0 hrs";
    const formattedMinutes = minutes ? `${minutes} min` : "0 min";
    return `${formattedHours} ${formattedMinutes}`;
  };

  useEffect(() => {
    if (shiftList && permission?.read) {
      const list = shiftList.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          project: (
            <Author
              name={getFormattedProjectName(item?.projects?.[0])}
              nickName={item?.defaultProject?.title}
              maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
            />
          ),
          team: <Author name={item?.teams[0]?.teamsWfmName} />,
          memberCount:
            item?.allMembers?.length > 0 ? (
              <ShiftMembers memberList={item?.allMembers} />
            ) : (
              <Author name={" "} />
            ),
          status: <Status title={`${item?.status.replace("_", " ")}`} />,
          startTime: (
            <MDTypography
              variant="caption"
              sx={{ cursor: "pointer", fontSize }}
              component={Link}
              to={`/client/shifts/${item?.[Constants.MONGOOSE_ID]}`}
            >
              {item?.startDate
                ? moment(item?.startDate).format(defaultData.WEB_24_HOURS_FORMAT_WITHOUT_SECOND)
                : "---"}
            </MDTypography>
          ),
          duration: (
            <MDTypography variant="caption" sx={{ fontSize }}>
              {item?.duration ? formateDuration(item?.duration) : "0 hrs 0 min"}
            </MDTypography>
          ),
          createdBy: (
            <By
              name={`${
                (item?.createdBy?.callingName
                  ? item?.createdBy?.callingName
                  : item?.createdBy?.firstName) ?? ""
              } ${item?.createdBy?.lastName ?? ""}`}
              isSuperAdmin={item?.createdBy?.role?.[0]?.title === defaultData.SUPER_ADMIN_ROLE}
              when={moment(item?.createdAt).format(defaultData.WEB_DATE_FORMAT)}
            />
          ),
          action: (
            <MDBox>
              {permission?.read && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  component={Link}
                  to={`/client/shifts/${item?.[Constants.MONGOOSE_ID]}`}
                >
                  {Icons.VIEW}
                </IconButton>
              )}
              &nbsp;
              {permission?.delete && (
                <IconButton
                  aria-label="fingerprint"
                  color="error"
                  onClick={() => handleOpenDeleteModal(item?.[Constants.MONGOOSE_ID])}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [shiftList, ConfigData]);

  return {
    columns: [
      { Header: "No.", accessor: "srNo", width: "2%" },
      { Header: "Project", accessor: "project", align: "left", width: "30%" },
      { Header: "Team", accessor: "team", align: "left", width: "15%" },
      { Header: "Member Count", accessor: "memberCount", align: "left", width: "5%" },
      { Header: "Start Time", accessor: "startTime", align: "left", width: "15%" },
      { Header: "Duration", accessor: "duration", align: "left", width: "13%" },
      { Header: "Status", accessor: "status", align: "left", width: "10%" },
      { Header: "Created By", accessor: "createdBy", align: "left", width: "15%" },
      { Header: "Action", accessor: "action", width: "10%", align: "left" },
    ],

    rows,
  };
}

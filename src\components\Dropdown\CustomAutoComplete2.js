import React, { useState, useCallback, useRef, useMemo } from "react";

// material-ui
import { Autocomplete, Tooltip } from "@mui/material";
import MDTypography from "components/MDTypography";
import MDInput from "components/MDInput";
import Ellipsis from "components/Table/Ellipsis";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Common components
import FontComponent from "components/Responsive/fonts";

// utils
import Constants, { Icons, Colors } from "utils/Constants";

// 3rd party
import PropTypes, { object } from "prop-types";

const ITEMS_PER_PAGE = 100; // Number of items to load per scroll

// This is the Automplete component that will be used in the dropdown which has large data sets
function CustomAutoComplete2({
  label,
  id,
  name,
  hint,
  handleChange,
  menu,
  error,
  helperText,
  getOptionLabel,
  value,
  labelStyle,
  valueStyle,
  disabled,
  mt,
  renderOption,
}) {
  const fontSize = FontComponent({ sizes: {} });
  const [inputValue, setInputValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const listboxRef = useRef(null);
  const isLoadingRef = useRef(false);

  // Filter the entire dataset based on input value
  const filteredOptions = useMemo(() => {
    if (!inputValue) return menu;

    const searchTerm = inputValue.toLowerCase().trim();

    return menu.filter((option) => {
      // Get all searchable fields from the option
      const searchableText =
        typeof option === "string"
          ? option.toLowerCase()
          : [
              getOptionLabel(option), // Include the formatted label
              option.title,
              option.name,
              option.iata,
              option.city,
              option.country,
              option[Constants.MONGOOSE_ID],
              option.id,
            ]
              .filter(Boolean)
              .join(" ")
              .toLowerCase();

      // Debug individual option when searching for Yanji
      // Split search term into words for more flexible matching
      const searchWords = searchTerm.split(/\s+/);

      // Match if any word is found in the searchable text
      const isMatch = searchWords.some((word) => searchableText.includes(word));

      return isMatch;
    });
  }, [inputValue, menu, getOptionLabel]);

  // Get paginated visible options from filtered results
  const visibleOptions = useMemo(
    () => filteredOptions.slice(0, currentPage * ITEMS_PER_PAGE),
    [filteredOptions, currentPage]
  );

  const handleInputChange = (e, newInputValue) => {
    setInputValue(newInputValue.trimStart());
    setCurrentPage(1);
  };

  const handleOptionChange = (e, selectedOption) => {
    if (!selectedOption) {
      handleChange({
        target: {
          name,
          value: "",
        },
      });
      return;
    }

    handleChange({
      target: {
        name,
        value: selectedOption,
      },
    });
  };

  const handleScroll = useCallback(
    (event) => {
      const listbox = event.target;
      const isAtBottom = listbox.scrollTop + listbox.clientHeight >= listbox.scrollHeight - 50;

      if (isAtBottom && visibleOptions.length < filteredOptions.length && !isLoadingRef.current) {
        isLoadingRef.current = true;
        const currentScrollTop = listbox.scrollTop;

        setTimeout(() => {
          setCurrentPage((prev) => prev + 1);

          requestAnimationFrame(() => {
            if (listboxRef.current) {
              listboxRef.current.scrollTop = currentScrollTop;
            }
            isLoadingRef.current = false;
          });
        }, 0);
      }
    },
    [visibleOptions.length, filteredOptions.length]
  );

  return (
    <>
      <MDTypography
        variant="caption"
        sx={{
          fontSize,
          fontWeight: labelStyle?.fontWeight || 500,
          color: "#344054",
          marginBottom: "4px",
        }}
      >
        {label}
      </MDTypography>

      <Autocomplete
        options={visibleOptions}
        getOptionLabel={getOptionLabel}
        name={name}
        id={id}
        variant="standard"
        value={value}
        disabled={disabled}
        inputValue={inputValue}
        style={{ backgroundColor: valueStyle?.backgroundColor || "transparent" }}
        onInputChange={handleInputChange}
        onChange={handleOptionChange}
        ListboxProps={{
          ref: listboxRef,
          onScroll: handleScroll,
          style: { maxHeight: "300px" },
        }}
        noOptionsText="No matching options found"
        renderOption={
          renderOption ||
          ((props, option) => {
            if (option?.hide) {
              return null;
            }
            return (
              <li {...props} key={option?.[Constants.MONGOOSE_ID]}>
                <Ellipsis
                  maxWidth="100%"
                  title={option?.title || option?.name}
                  style={{
                    textTransform: valueStyle?.textTransform || "",
                    fontSize,
                    color: disabled ? "#8c8ca8" : valueStyle?.color || "",
                  }}
                >
                  {option?.title || option?.name}
                </Ellipsis>
              </li>
            );
          })
        }
        sx={{
          "& .MuiAutocomplete-inputRoot": {
            display: "flex",
            alignItems: "center",
            height: valueStyle?.height || "auto",
            padding: valueStyle?.padding ? valueStyle?.padding : "4px",
            fontSize,
            backgroundColor: disabled
              ? Colors.DISABLED
              : valueStyle?.backgroundColor || "transparent",
          },
          mt,
        }}
        popupIcon={Icons.ARROW_DOWN}
        renderInput={(params) => {
          const selectedLabel = value ? getOptionLabel(value) : "";
          const showTooltip = selectedLabel.length > 50;

          const inputField = (
            <MDInput
              {...params}
              name={name}
              placeholder={hint}
              error={Boolean(error)}
              helperText={helperText}
              disabled={disabled}
              sx={{
                "& .MuiInputBase-input": {
                  color: disabled ? "#8c8ca8" : valueStyle?.color || "",
                  textTransform: valueStyle?.textTransform || "",
                  fontSize,
                  backgroundColor: disabled ? Colors.DISABLED : "transparent",
                  fontWeight: valueStyle?.fontWeight || 500,
                },
              }}
              FormHelperTextProps={{
                sx: {
                  marginLeft: 0,
                  color: "#FF2E2E",
                },
              }}
            />
          );

          return showTooltip ? (
            <Tooltip title={selectedLabel} arrow placement="top">
              <div>{inputField}</div>
            </Tooltip>
          ) : (
            inputField
          );
        }}
      />
    </>
  );
}

CustomAutoComplete2.propTypes = {
  label: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  hint: PropTypes.string.isRequired,
  handleChange: PropTypes.func,
  menu: PropTypes.arrayOf(PropTypes.object),
  error: PropTypes.bool,
  helperText: PropTypes.string,
  getOptionLabel: PropTypes.func.isRequired,
  value: PropTypes.string || PropTypes.objectOf(object),
  labelStyle: PropTypes.objectOf(PropTypes.any),
  valueStyle: PropTypes.objectOf(PropTypes.any),
  disabled: PropTypes.bool,
  mt: PropTypes.string || PropTypes.number,
  renderOption: PropTypes.func, // <-- Added propType for renderOption
};

CustomAutoComplete2.defaultProps = {
  handleChange: () => {},
  menu: [],
  error: false,
  helperText: "",
  value: "",
  labelStyle: {},
  valueStyle: {},
  disabled: false,
  mt: pxToRem(0),
  renderOption: null, // <-- Default to null, no custom renderOption
};

export default CustomAutoComplete2;

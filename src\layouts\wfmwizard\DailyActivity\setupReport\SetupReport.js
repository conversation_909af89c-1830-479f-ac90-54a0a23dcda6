import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { Feature } from "flagged";

// Material components
import MDBox from "components/MDBox";
import { Card, Divider, Grid, FormControl } from "@mui/material";

// Custom components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicModal from "examples/modal/BasicModal/BasicModal";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import FDropdown from "components/Dropdown/FDropdown";
import FDropdown2 from "components/Dropdown/fDropdown2";
import DataTable from "examples/Tables/DataTable";
import FTextField from "components/Form/FTextField";
import CustomRadio from "components/CustomRadio/CustomRadio";
import pxToRem from "assets/theme/functions/pxToRem";
import ResetFilterButton from "components/Buttons/ResetButton";
import MultiSelectAutoComplete from "components/Dropdown/MultipleAutocomplete";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";

// Table Data
import SetupReportData from "layouts/wfmwizard/DailyActivity/setupReport/data/SetupReportData";

// Redux
import { useDispatch, useSelector } from "react-redux";
import {
  projectListThunk,
  scopesThunk,
  locationListByIdThunk,
  AssetLinkedLocationsThunk,
  assetListThunk,
} from "redux/Thunks/FieldsData";
import createReportType, {
  getAllReportTypes,
  updateReportType,
  deleteReportType,
} from "redux/Thunks/Report";
import { openSnackbar } from "redux/Slice/Notification";
import {
  reloadReportTypeData,
  updateReportTypeReducer,
  removeReportType,
} from "redux/Slice/Report";
import { setStoreFilters, resetFilters } from "redux/Slice/Filter";

// Utils
import Constants, {
  Icons,
  PageTitles,
  ButtonTitles,
  Colors,
  ModalContent,
  FeatureTags,
  defaultData,
  Common,
} from "utils/Constants";
import { paramCreater } from "utils/methods/methods";
import Validator from "utils/Validations";

const initialReportState = {
  type: "new",
  openModal: false,
  list: [],
  body: {
    title: "",
    project: "",
    type: "",
    isProgressable: false,
    sortOrder: 0,
    isPublish: false,
    reportId: "",
    scope: "",
    locations: [],
    assets: [],
  },
  editBody: {},
  errors: {},
  openDeleteModal: false,
  editDeleteId: "",
  editIndex: null,
  loading: false,
};

const initialProjectAssets = {
  allAssets: [],
  locationsLinkedAssets: [],
};

function SetupReport() {
  const [projectList, setProjectList] = useState([]);
  const [projectReportList, setProjectReportList] = useState([]);
  const [projectScopeList, setProjectScopeList] = useState([]);
  const [projectLocations, setProjectLocations] = useState([]);
  const [projectAssets, setProjectAssets] = useState(initialProjectAssets);
  const [reportTypeData, setReportTypeData] = useState(initialReportState);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[14]?.screensInfo?.agreement;
  const location = useLocation();

  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState([
    {
      inputLabel: "Project",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      textTransform: "none",
    },
    {
      inputLabel: "Types",
      list: [
        { [Constants.MONGOOSE_ID]: "all", title: "All" },
        { [Constants.MONGOOSE_ID]: "location", title: Common.LOCATION },
        { [Constants.MONGOOSE_ID]: "asset_per_location", title: "Asset per Location" },
        { [Constants.MONGOOSE_ID]: "multiple_assets", title: "Multiple Assets" },
      ],
      selectedValue: "all",
      textTransform: "capitalize",
    },
  ]);
  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });
  const [next, setNext] = useState(0);
  const reportTypes = useSelector((state) => state.report);
  const setupReportStoredFilters = useSelector((state) => state.filtersSlice.reportSetup);
  const dispatch = useDispatch();

  const handleCloseReportType = () =>
    setReportTypeData(JSON.parse(JSON.stringify(initialReportState)));

  const handleCloseDeleteReportTypeModal = () =>
    setReportTypeData((prev) => ({ ...prev, openDeleteModal: false }));

  const handleDeleteReportTypeModal = (id) => {
    setReportTypeData((prev) => ({ ...prev, openDeleteModal: true, editDeleteId: id }));
  };

  const handleLocationFetch = async (projectId, thunk) => {
    const locationRes = await dispatch(thunk(projectId));
    if (locationRes.payload.status) {
      let locationLists = [
        {
          title: "All",
          [Constants.MONGOOSE_ID]: "all",
        },
      ];
      locationLists = [
        ...locationLists,
        ...locationRes.payload.data.map((val) => ({
          title: val.title,
          [Constants.MONGOOSE_ID]: val[Constants.MONGOOSE_ID],
        })),
      ];
      setProjectLocations(locationLists);
    }
  };

  const handleAssetFetch = async (projectId, type, locations = []) => {
    const assetsRes = await dispatch(assetListThunk(projectId));
    if (assetsRes.payload.status) {
      const assetLists = [
        {
          title: "All",
          [Constants.MONGOOSE_ID]: "all",
        },
      ];
      const fetchedAssets = assetsRes.payload.data.map((val) => ({
        title: val.cableName,
        [Constants.MONGOOSE_ID]: val[Constants.MONGOOSE_ID],
        locations: [
          val.fromLocation?.[Constants.MONGOOSE_ID],
          val.toLocation?.[Constants.MONGOOSE_ID],
        ],
      }));
      if (type === Common.ASSET_PER_LOCATION && locations.length > 0) {
        const tempLinkedAssets = fetchedAssets.filter((val) =>
          val.locations.some((loc) => locations.includes(loc))
        );
        setProjectAssets({
          allAssets: fetchedAssets,
          locationsLinkedAssets: [...assetLists, ...tempLinkedAssets],
        });
      } else if (type === Common.MULTIPLE_ASSETS) {
        setProjectAssets({
          allAssets: fetchedAssets,
          locationsLinkedAssets: [...assetLists, ...fetchedAssets],
        });
      } else if (type === Common.ASSET_PER_LOCATION && locations.length === 0) {
        setProjectAssets({ allAssets: fetchedAssets, locationsLinkedAssets: [] });
      }
    }
  };

  const handleEditReportType = async (data, editIndex) => {
    const tempType = data.type.replaceAll("_", " ");
    const dropdownFormat = {
      [Constants.MONGOOSE_ID]: "",
      title: "",
    };
    const tempBody = {
      project: data.project?.[Constants.MONGOOSE_ID],
      title: data.title,
      type: tempType.charAt(0).toUpperCase() + tempType.slice(1),
      isProgressable: data.isProgressable.toString(),
      sortOrder: data.sortOrder,
      isPublish: data.isPublish,
      scope: data.scope[0]?.[Constants.MONGOOSE_ID],
      locations: data.locations.map((val) => ({
        title: val.title,
        [Constants.MONGOOSE_ID]: val[Constants.MONGOOSE_ID],
      })),
      assets: data.assets.map((val) => ({
        title: val.cableName,
        [Constants.MONGOOSE_ID]: val[Constants.MONGOOSE_ID],
      })),
    };
    setReportTypeData((prev) => ({
      ...prev,
      type: "update",
      openModal: true,
      body: JSON.parse(JSON.stringify(tempBody)),
      editBody: JSON.parse(JSON.stringify(tempBody)),
      editDeleteId: data[Constants.MONGOOSE_ID],
      editIndex,
    }));

    if (data.type === "location")
      handleLocationFetch(data.project[Constants.MONGOOSE_ID], locationListByIdThunk);
    else if (data.type === "asset_per_location") {
      handleLocationFetch(data.project[Constants.MONGOOSE_ID], AssetLinkedLocationsThunk);
      handleAssetFetch(data.project[Constants.MONGOOSE_ID], tempBody.type, [
        ...tempBody.locations.map((val) => val[Constants.MONGOOSE_ID]),
      ]);
    } else if (data.type === "multiple_assets")
      handleAssetFetch(data.project[Constants.MONGOOSE_ID], tempBody.type);

    const scopeRes = await dispatch(scopesThunk(data.project[Constants.MONGOOSE_ID]));
    const tempScope = scopeRes.payload.data.data.map((item) => {
      const temp = { ...dropdownFormat };
      temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
      temp.title = item?.name;
      return temp;
    });
    if (scopeRes.payload.status === 200) {
      setProjectScopeList(tempScope);
    }
  };

  const { columns, rows } = SetupReportData(
    {
      reportTypeLoading: reportTypes.reportTypeLoading,
      reportType: reportTypes.reportType || [],
    },
    handleEditReportType,
    handleDeleteReportTypeModal,
    permission
  );

  const handleFilter = async (filterVale = filters) => {
    setTablePagination({ ...tablePagination, page: 0 });
    setNext(0);
    const paramData = {
      page: 0,
      perPage: tablePagination.perPage,
      project: filterVale[0].selectedValue,
      type: filterVale[1].selectedValue,
    };

    await dispatch(reloadReportTypeData());
    await dispatch(getAllReportTypes({ param: paramCreater(paramData), byPassSlice: false }));
  };

  useEffect(() => {
    const { openNewReportType, projectId } = location.state || { openNewReportType: false };
    const tempFilters = [...filters];
    const dropdownFormat = {
      [Constants.MONGOOSE_ID]: "",
      title: "",
    };
    if (projectId) {
      (async () => {
        tempFilters[0].selectedValue = projectId;
        const scopeRes = await dispatch(scopesThunk(projectId));
        const tempScope = scopeRes?.payload?.data?.data?.map((item) => {
          const temp = { ...dropdownFormat };
          temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
          temp.title = item?.name;
          return temp;
        });
        if (scopeRes?.payload?.status === 200) {
          setProjectScopeList(tempScope);
        }
      })();
    }
    const fetchProjects = async () => {
      const res = await dispatch(projectListThunk());
      tempFilters[0].list = [...tempFilters[0].list, ...(res.payload.data || [])];
      setFilters((prevFilters) => {
        const updatedFilters = [...prevFilters];
        updatedFilters[0] = {
          ...updatedFilters[0],
          selectedValue:
            setupReportStoredFilters?.length > 0
              ? setupReportStoredFilters[0]?.selectedValue
              : "all",
          list: [{ _id: "all", title: "All" }, ...res.payload.data],
        };
        return updatedFilters;
      });
      setProjectList(res.payload.data);
    };
    fetchProjects();
    if (setupReportStoredFilters?.length > 0) {
      setFilters((prevFilters) => {
        const updatedFilters = [...prevFilters];
        updatedFilters[0] = {
          ...updatedFilters[0],
          selectedValue: setupReportStoredFilters[0]?.selectedValue,
        };
        updatedFilters[1] = {
          ...updatedFilters[1],
          selectedValue: setupReportStoredFilters[1]?.selectedValue,
        };
        return updatedFilters;
      });
    } else {
      setFilters(tempFilters);
    }
    setReportTypeData((prev) => ({
      ...prev,
      openModal: openNewReportType,
      body: { ...prev.body, project: projectId || "" },
    }));
    if (setupReportStoredFilters?.length > 0) {
      handleFilter(setupReportStoredFilters);
    } else {
      handleFilter(tempFilters);
    }
  }, []);

  useEffect(() => {
    (async () => {
      if (reportTypeData.openModal) {
        const paramData = {
          page: 0,
          perPage: 1000,
          type: "all",
        };
        const setupReportRes = await dispatch(
          getAllReportTypes({ param: paramCreater(paramData), byPassSlice: true })
        );

        const reportLists = setupReportRes.payload.data.data.map((val) => ({
          ...val,
          title: `${val.title} - (${val.project.title})`,
        }));

        if (setupReportRes.payload.status === 200) {
          setProjectReportList(reportLists);
        }
      }
    })();
  }, [reportTypeData.openModal]);

  const handleReportTypeChange = async (e) => {
    const { name, value } = e.target;
    const temp = { ...reportTypeData };
    temp.body[name] = value;
    if (name === "project" || name === "type") {
      if (name === "project") {
        temp.body.reportId = "";
        temp.body.type = "";
        temp.loading = true;
      }
      setProjectAssets(initialProjectAssets);
      temp.body.locations = [];
      temp.body.assets = [];
    }
    setReportTypeData(temp);

    if (name === "project") {
      const dropdownFormat = {
        [Constants.MONGOOSE_ID]: "",
        title: "",
      };
      const scopeRes = await dispatch(scopesThunk(value));
      const tempScope = scopeRes.payload.data.data.map((item) => {
        const tempData = { ...dropdownFormat };
        tempData[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        tempData.title = item?.name;
        return tempData;
      });
      if (scopeRes.payload.status === 200) {
        setProjectScopeList(tempScope);
        setReportTypeData((prev) => ({ ...prev, loading: false }));
      }
    } else if (name === "type" && value === Common.LOCATION) {
      handleLocationFetch(reportTypeData.body.project, locationListByIdThunk);
      setProjectAssets([]);
    } else if (name === "type" && value === Common.ASSET_PER_LOCATION) {
      handleLocationFetch(reportTypeData.body.project, AssetLinkedLocationsThunk);
      handleAssetFetch(reportTypeData.body.project, value);
    } else if (name === "type" && value === Common.MULTIPLE_ASSETS) {
      handleAssetFetch(reportTypeData.body.project, value);
      setProjectLocations([]);
    }
  };

  const handleSortOrderChange = (e) => {
    const { value } = e.target;
    if (value < 0) return;
    const temp = { ...reportTypeData };
    temp.body.sortOrder = value;
    setReportTypeData(temp);
  };

  const filterLocationsLinkedAssets = (selectedLocations) => {
    const connectedAsset = projectAssets.allAssets.filter((val) =>
      val.locations.some((loc) => selectedLocations.includes(loc))
    );

    const locationsLinkedAssets = connectedAsset.length
      ? [
          {
            title: "All",
            [Constants.MONGOOSE_ID]: "all",
          },
          ...connectedAsset,
        ]
      : [];

    setProjectAssets({
      ...projectAssets,
      locationsLinkedAssets,
    });

    return connectedAsset;
  };

  const handleLocationAndAssetsChange = (e) => {
    const { name, value } = e.target;
    if (value[Constants.MONGOOSE_ID] !== "all") {
      const temp = { ...reportTypeData };
      temp.body[name] = [...temp.body[name], value];
      setReportTypeData(temp);
      if (temp.body.type === Common.ASSET_PER_LOCATION)
        filterLocationsLinkedAssets(temp.body.locations.map((val) => val[Constants.MONGOOSE_ID]));
    } else if (name === "locations" && value[Constants.MONGOOSE_ID] === "all") {
      const temp = { ...reportTypeData };
      temp.body.locations = projectLocations.filter((val) => val[Constants.MONGOOSE_ID] !== "all");
      setReportTypeData(temp);
      if (temp.body.type === Common.ASSET_PER_LOCATION)
        filterLocationsLinkedAssets(temp.body.locations.map((val) => val[Constants.MONGOOSE_ID]));
    } else if (name === "assets" && value[Constants.MONGOOSE_ID] === "all") {
      const temp = { ...reportTypeData };
      temp.body.assets = projectAssets.locationsLinkedAssets.filter(
        (val) => val[Constants.MONGOOSE_ID] !== "all"
      );
      setReportTypeData(temp);
    }
  };

  const handleRemoveLocationAndAssetsChange = (name, index) => {
    const updatedReportData = { ...reportTypeData };
    updatedReportData.body[name].splice(index, 1);

    if (updatedReportData.body.type === Common.ASSET_PER_LOCATION && name === "locations") {
      const selectedLocationIds = updatedReportData.body.locations.map(
        (loc) => loc[Constants.MONGOOSE_ID]
      );

      const connectedAssets = filterLocationsLinkedAssets(selectedLocationIds);
      updatedReportData.body.assets = updatedReportData.body.assets.filter((asset) =>
        connectedAssets.some((item) => item[Constants.MONGOOSE_ID] === asset[Constants.MONGOOSE_ID])
      );
    }

    setReportTypeData(updatedReportData);
  };

  const reportTypevalidation = () => {
    const { title, project, type } = reportTypeData.body;
    const errors = {};

    const titleValidation = Validator.validate("basic2", title);
    const projectValidation = Validator.validate("basic2", project);
    const typeValidation = Validator.validate("basic2", type);
    const scopeValidation = Validator.validate("basic2", reportTypeData.body.scope);

    if (titleValidation) errors.title = titleValidation;
    if (projectValidation) errors.project = projectValidation;
    if (typeValidation) errors.type = typeValidation;
    if (scopeValidation) errors.scope = scopeValidation;
    if (
      reportTypeData.body.type === Common.ASSET_PER_LOCATION ||
      reportTypeData.body.type === Common.LOCATION
    ) {
      if (reportTypeData.body.locations?.length === 0) errors.locations = Constants.REQUIRED;
    }
    if (
      reportTypeData.body.type === Common.ASSET_PER_LOCATION ||
      reportTypeData.body.type === Common.MULTIPLE_ASSETS
    ) {
      if (reportTypeData.body.assets?.length === 0) errors.assets = Constants.REQUIRED;
    }
    return Object.keys(errors).length > 0 ? errors : {};
  };

  const handleCreateReportType = async () => {
    setLoading(true);
    const errors = reportTypevalidation();
    setReportTypeData((prev) => ({ ...prev, errors }));
    if (Object.keys(errors).length === 0) {
      const body = {
        ...reportTypeData.body,
        title: reportTypeData.body.title.trim(),
        type: reportTypeData.body.type.replaceAll(" ", "_").toLowerCase(),
        locations: reportTypeData.body.locations.map((val) => val[Constants.MONGOOSE_ID]),
        assets: reportTypeData.body.assets.map((val) => val[Constants.MONGOOSE_ID]),
      };
      if (body.reportId === "") body.reportId = undefined;
      if (body.type === "location") body.assets = undefined;
      else if (body.type === "multiple_assets") body.locations = undefined;

      const res = await dispatch(createReportType(body));
      if (res.payload.status === 200) {
        handleFilter();
        dispatch(
          openSnackbar({
            message: Constants.SETUP_REPORT_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        handleCloseReportType();
      } else if (res.payload.status === 422) {
        setReportTypeData((prev) => ({
          ...prev,
          errors: { title: Constants.SETUP_REPORT_CREATE_ERROR },
        }));
      }
    }
    setLoading(false);
  };

  const handleClearLocations = () => {
    const temp = { ...reportTypeData };
    temp.body.locations = [];
    temp.body.assets = [];
    setReportTypeData(temp);
  };

  const handleClearAssets = () => {
    const temp = { ...reportTypeData };
    temp.body.assets = [];
    setReportTypeData(temp);
  };

  const isChanged = (prevArr, currentArr) => {
    if (prevArr.length !== currentArr.length) return true;
    if (
      prevArr.filter(
        (val) =>
          currentArr.findIndex((el) => el[Constants.MONGOOSE_ID] === val[Constants.MONGOOSE_ID]) ===
          -1
      ).length > 0
    )
      return true;

    return false;
  };

  const handleUpdateReportType = async () => {
    setLoading(true);
    const errors = reportTypevalidation();
    setReportTypeData((prev) => ({ ...prev, errors }));
    if (Object.keys(errors).length === 0) {
      const { body, editDeleteId, editBody } = reportTypeData;
      const tempBody = {
        ...(body.title !== editBody?.title && { title: body.title.trim() }),
        ...(body.isProgressable.toString() !== editBody?.isProgressable.toString() && {
          isProgressable: body.isProgressable,
        }),
        ...(body.sortOrder !== editBody?.sortOrder && { sortOrder: body.sortOrder }),
        ...(body.scope !== editBody?.scope && { scope: body.scope }),
      };

      if (body.type === Common.LOCATION) {
        tempBody.assets = undefined;
        if (isChanged(body.locations, editBody?.locations))
          tempBody.locations = reportTypeData.body.locations.map(
            (val) => val[Constants.MONGOOSE_ID]
          );
      } else if (body.type === Common.ASSET_PER_LOCATION) {
        if (isChanged(body.locations, editBody?.locations))
          tempBody.locations = reportTypeData.body.locations.map(
            (val) => val[Constants.MONGOOSE_ID]
          );
        if (isChanged(body.assets, editBody?.assets))
          tempBody.assets = reportTypeData.body.assets.map((val) => val[Constants.MONGOOSE_ID]);
      } else if (body.type === Common.MULTIPLE_ASSETS) {
        tempBody.locations = undefined;
        if (isChanged(body.assets, editBody?.assets))
          tempBody.assets = reportTypeData.body.assets.map((val) => val[Constants.MONGOOSE_ID]);
      }
      const res = await dispatch(updateReportType({ body: tempBody, reportTypeId: editDeleteId }));
      if (res.payload.status === 200) {
        dispatch(
          updateReportTypeReducer({
            updatedData: res.payload.data.data,
            editIndex: reportTypeData.editIndex,
          })
        );
        dispatch(
          openSnackbar({
            message: Constants.SETUP_REPORT_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        handleCloseReportType();
      } else if (res.payload.status === 422) {
        setReportTypeData((prev) => ({
          ...prev,
          errors: { title: Constants.SETUP_REPORT_CREATE_ERROR },
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SETUP_REPORT_UPDATE_ERROR,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    }
    setLoading(false);
  };

  const handleDeleteReportType = async () => {
    const { editDeleteId } = reportTypeData;
    const res = await dispatch(deleteReportType(editDeleteId));
    if (res.payload.status === 200) {
      await dispatch(removeReportType(editDeleteId));
      await dispatch(
        openSnackbar({
          message: Constants.SETUP_REPORT_DELETE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else if (res.payload.status === 400) {
      dispatch(
        openSnackbar({
          message: Constants.SETUP_REPORT_DELETE_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setReportTypeData((prev) => ({ ...prev, openDeleteModal: false, editDeleteId: "" }));
  };

  const handleFilterChange = (e) => {
    const temp = [...filters];
    const { value, name } = e.target;
    const index = filters.findIndex((filter) => filter.inputLabel === name);
    temp[index] = {
      ...temp[index],
      selectedValue: value,
    };
    setFilters(temp);
    dispatch(setStoreFilters({ module: "reportSetup", filters: temp }));
    handleFilter(temp);
  };

  const handleReset = async () => {
    const resetFilter = filters.map((filter) => ({ ...filter, selectedValue: "all" }));
    setFilters(resetFilter);
    dispatch(resetFilters({ module: "reportSetup" }));
    await handleFilter(resetFilter);
  };

  const handleTablePagination = async () => {
    const paramData = {
      page: next + 1,
      perPage: tablePagination.perPage,
      project: filters[0].selectedValue,
      type: filters[1].selectedValue,
    };
    const res = await dispatch(
      getAllReportTypes({ param: paramCreater(paramData), byPassSlice: false })
    );
    if (res.payload.status === 200) setNext(res.payload.data.data.length > 0 ? next + 1 : next);
  };

  const handleReload = async () => {
    await dispatch(reloadReportTypeData());
    handleFilter();
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between" alignItems="center">
        <PageTitle title={PageTitles.SETUP_REPORT} />
        <MDBox display="flex" flexDirection="row">
          {permission?.create && (
            <CustomButton
              title={ButtonTitles.SETUP_REPORT}
              icon={Icons.NEW}
              background={Colors.PRIMARY}
              color={Colors.WHITE}
              openModal={() => setReportTypeData((prev) => ({ ...prev, openModal: true }))}
            />
          )}
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleReload}
          />
        </MDBox>
      </MDBox>
      <Divider sx={{ marginTop: pxToRem(22) }} />
      <Feature name={FeatureTags.SETUP_REPORT}>
        <MDBox display="flex" justifyContent="space-between">
          <MDBox
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "start",
              alignItems: "flex-end",
            }}
          >
            {filters?.map((val) => {
              const isProject = val?.inputLabel === "Project";
              return isProject ? (
                <FormControl
                  key={val?.inputLabel}
                  variant="standard"
                  size="medium"
                  style={{ marginTop: "26px", width: 200, marginRight: "15px" }}
                >
                  <CustomAutoComplete
                    label={val?.inputLabel}
                    name={val?.inputLabel}
                    id={val?.inputLabel}
                    getOptionLabel={(option) => option.title || ""}
                    menu={val?.list}
                    value={{
                      title:
                        val?.list.find((item) => item[Constants.MONGOOSE_ID] === val?.selectedValue)
                          ?.title || "",
                    }}
                    handleChange={handleFilterChange}
                    valueStyle={{
                      backgroundColor: Colors.WHITE,
                      height: pxToRem(40),
                      verticalMarginTop: pxToRem(4),
                      menuWidth: 400,
                      inputWidth: 250,
                      padding: pxToRem(1),
                    }}
                    labelStyle={{
                      fontSize: pxToRem(14),
                      fontWeight: 600,
                      color: Colors.BLACK,
                    }}
                    textTransform={val.textTransform}
                  />
                </FormControl>
              ) : (
                <FilterDropdown
                  label={val.inputLabel}
                  name={val.inputLabel}
                  defaultValue={val?.selectedValue}
                  value={val?.selectedValue}
                  handleChange={handleFilterChange}
                  menu={val.list}
                  key={val.inputLabel}
                  maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
                  textTransform={val.textTransform}
                />
              );
            })}
            <ResetFilterButton handleReset={handleReset} style={{ marginLeft: "1rem" }} />
          </MDBox>
        </MDBox>
        <MDBox mt={3} mb={3}>
          <Grid item xs={12}>
            <Card>
              <MDBox>
                <DataTable
                  table={{ columns, rows }}
                  isSorted={false}
                  entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
                  showTotalEntries={false}
                  noEndBorder
                  loading={reportTypes.reportTypeLoading}
                  currentPage={tablePagination.page}
                  handleTablePagination={handleTablePagination}
                  handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
                />
              </MDBox>
            </Card>
          </Grid>
        </MDBox>

        {/* Create and Update Report type modal */}
        <BasicModal
          open={reportTypeData.openModal}
          handleClose={handleCloseReportType}
          title={
            reportTypeData.type === "new"
              ? ModalContent.NEW_REPORT_TITLE
              : ModalContent.EDIT_REPORT_TITLE
          }
          actionButton={
            (reportTypeData.type === "new" && !loading && ButtonTitles.SUBMIT) ||
            (reportTypeData.type === "new" && loading && ButtonTitles.SUBMIT_LOADING) ||
            (reportTypeData.type === "update" && !loading && ButtonTitles.UPDATE) ||
            (reportTypeData.type === "update" && loading && ButtonTitles.UPDATE_LOADING)
          }
          disabled={loading}
          handleAction={
            reportTypeData.type === "new" ? handleCreateReportType : handleUpdateReportType
          }
        >
          <FTextField
            label="Title*"
            name="title"
            id="title"
            placeholder="Enter your Report Title"
            type="text"
            value={reportTypeData.body.title}
            handleChange={handleReportTypeChange}
            width="100%"
            error={reportTypeData.errors?.title}
            helperText={reportTypeData.errors?.title}
            marginBottom={pxToRem(10)}
          />

          {/* <FDropdown2
            label="Project*"
            id="demo-select-small"
            name="project"
            value={reportTypeData.body.project}
            defaultValue=""
            options={projectList || []}
            error={reportTypeData.errors?.project}
            disabled={reportTypeData.type === "update"}
            helperText={reportTypeData.errors?.project}
            handleChange={handleReportTypeChange}
            marginBottom={pxToRem(10)}
          /> */}

          <CustomAutoComplete
            label="Project*"
            name="project"
            id="demo-select-small"
            hint="Select Project"
            getOptionLabel={(option) => option.title || ""}
            menu={projectList || []}
            value={{
              title:
                projectList.find(
                  (item) => item[Constants.MONGOOSE_ID] === reportTypeData.body.project
                )?.title || "",
            }}
            handleChange={handleReportTypeChange}
            valueStyle={{
              backgroundColor: Colors.WHITE,
              height: pxToRem(45),
              menuWidth: 400,
              inputWidth: 250,
            }}
            error={reportTypeData.errors?.project}
            disabled={reportTypeData.type === "update"}
            helperText={reportTypeData.errors?.project}
          />

          <FDropdown
            label="Report Type*"
            id="type"
            name="type"
            disabled={reportTypeData.type === "update" || reportTypeData.body.project === ""}
            value={reportTypeData.body.type}
            menu={[Common.LOCATION, Common.ASSET_PER_LOCATION, Common.MULTIPLE_ASSETS]}
            error={reportTypeData.errors?.type}
            helperText={reportTypeData.errors?.type}
            handleChange={(name, value, id) =>
              handleReportTypeChange({ target: { name, value, id } })
            }
            marginBottom={0}
            mr={0}
          />

          <MDBox display="flex" flexDirection="row" justifyContent="start" alignItems="center">
            <FTextField
              label="Sort Order*"
              name="sortOrder"
              id="sortOrder"
              placeholder="Enter Sort Order"
              type="number"
              value={reportTypeData.body.sortOrder}
              handleChange={handleSortOrderChange}
              width="50%"
              error={reportTypeData.errors?.sortOrder}
              helperText={reportTypeData.errors?.sortOrder}
              marginBottom={pxToRem(10)}
            />

            <CustomRadio
              label="is Progressable?*"
              name="isProgressable"
              list={[
                { label: "Yes", value: "true" },
                { label: "No", value: "false" },
              ]}
              value={reportTypeData.body.isProgressable.toString()}
              handleChange={handleReportTypeChange}
              style={{ marginLeft: "2rem" }}
            />
          </MDBox>

          {reportTypeData.type === "new" && (
            <FDropdown2
              label="Clone Questions from"
              id="cloneFrom"
              name="reportId"
              value={reportTypeData.body.reportId}
              options={projectReportList || []}
              error={reportTypeData.errors?.reportId}
              helperText={reportTypeData.errors?.reportId}
              handleChange={handleReportTypeChange}
              marginBottom={0}
              addClearIcon
            />
          )}

          {reportTypeData.body.project !== "" && !reportTypeData.loading && (
            <FDropdown2
              label="Scope*"
              id="scope"
              name="scope"
              value={reportTypeData.body.scope}
              options={projectScopeList || []}
              error={reportTypeData.errors?.scope}
              disabled={reportTypeData.body.project === ""}
              helperText={reportTypeData.errors?.scope}
              handleChange={handleReportTypeChange}
              marginBottom={0}
            />
          )}

          {(reportTypeData.body.type === Common.ASSET_PER_LOCATION ||
            reportTypeData.body.type === Common.LOCATION) && (
            <MultiSelectAutoComplete
              label="Locations*"
              name="locations"
              id="locations"
              value={reportTypeData.body.locations}
              menu={projectLocations || []}
              error={Boolean(reportTypeData.errors?.locations)}
              hint={
                reportTypeData.body.locations.length !== projectLocations.length - 1 &&
                "Enter all to select all locations"
              }
              helperText={reportTypeData.errors?.locations}
              handleChange={handleLocationAndAssetsChange}
              handleRemove={handleRemoveLocationAndAssetsChange}
              handleClear={handleClearLocations}
              getOptionLabel={(option) => option?.title}
            />
          )}

          {(reportTypeData.body.type === Common.ASSET_PER_LOCATION ||
            reportTypeData.body.type === Common.MULTIPLE_ASSETS) && (
            <MultiSelectAutoComplete
              label="Assets*"
              name="assets"
              id="assets"
              value={reportTypeData.body.assets}
              menu={projectAssets.locationsLinkedAssets || []}
              error={reportTypeData.errors?.assets}
              hint={
                reportTypeData.body.assets.length !==
                  projectAssets.locationsLinkedAssets.length - 1 && "Enter all to select all assets"
              }
              helperText={reportTypeData.errors?.assets}
              handleChange={handleLocationAndAssetsChange}
              handleRemove={handleRemoveLocationAndAssetsChange}
              handleClear={handleClearAssets}
              getOptionLabel={(option) => option?.title}
            />
          )}

          {reportTypeData.loading && (
            <MDBox py={5} display="flex" justifyContent="center" alignItems="center">
              {Icons.LOADING2}
            </MDBox>
          )}
        </BasicModal>

        {/* Delete Modal for Report type */}
        <DeleteModal
          open={reportTypeData.openDeleteModal}
          title={ModalContent.REPORT_DELETE_TITLE}
          message={ModalContent.REPORT_DELETE_MESSAGE}
          handleClose={handleCloseDeleteReportTypeModal}
          handleDelete={handleDeleteReportType}
        />
      </Feature>
    </DashboardLayout>
  );
}

export default SetupReport;

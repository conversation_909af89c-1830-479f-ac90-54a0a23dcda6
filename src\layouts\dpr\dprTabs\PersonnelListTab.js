import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import FormTextArea from "components/Form/FTextArea";

// Constants
import Constants, { defaultData, Common } from "utils/Constants";

// Thunk
import { getPersonnelDprListData } from "redux/Thunks/Dpr";

// Slice
import { setPersonnelListRemarks, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";
import { openSnackbar } from "redux/Slice/Notification";

// Data
import PersonnelListData from "../data/personnelListData";

function PersonnelListTab() {
  const dispatch = useDispatch();
  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { id } = useParams();

  const { dprData, loading, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  const getPersonnelListData = async () => {
    try {
      // API call to get Personnel List data
      await dispatch(getPersonnelDprListData(id || location?.state?.dprId));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const { personnelColumns, personnelRows } = PersonnelListData(
    dprData?.personnelList?.personnelList || []
  );

  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getPersonnelListData();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  useEffect(() => {
    if (displayedDprTabsObj?.personnelListTab > 0) {
      getPersonnelListData()
        .then(() => dispatch(updateIsLatestDataApiCompleted(true)))
        .catch(() => dispatch(updateIsLatestDataApiCompleted(false)));
    }
  }, [displayedDprTabsObj?.personnelListTab]);

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Personnel List
          </MDTypography>
        </MDBox>
        <MDBox>
          <Card>
            <MDBox>
              <DataTable
                table={{ columns: personnelColumns, rows: personnelRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={loading}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.personnelList?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setPersonnelListRemarks(e.target.value));
            }}
          />
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default PersonnelListTab;

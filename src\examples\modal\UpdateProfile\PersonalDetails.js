import React, { useEffect, useState } from "react";

// MUI components
import { Autocomplete, Grid, Icon, Modal } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import ModalTitle from "examples/NewDesign/ModalTitle";
import FDropdown from "components/Dropdown/FDropdown";
import MDTypography from "components/MDTypography";
import MDInput from "components/MDInput";
import CustomAutoComeplete from "components/Dropdown/CustomAutoComeplete";

// Styles
import style from "assets/style/Modal";

// Redux
import { useDispatch } from "react-redux";
import { updateAdminProfileThunk } from "redux/Thunks/SuperAdmin";
import { openSnackbar } from "redux/Slice/Notification";

// Constants
import Constants, { Icons, defaultData, ModalContent, Common } from "utils/Constants";
import Validations from "utils/Validations/index";

// 3rd party library
import PhoneInput from "react-phone-input-2";
import countries from "countries-list";
import PropTypes from "prop-types";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";
import { numericFieldValidation } from "utils/methods/methods";

const countryList = Object.values(countries.countries).map((country) => country.name);
export default function Personaldetails({
  openPersonal,
  setOpenPersonal,
  title,
  data,
  pdata,
  setUpdate,
  roleOptions = [],
  profileFunctionOptions = [],
}) {
  const dispatch = useDispatch();
  const [values, setValues] = useState({
    callingName: "",
    firstName: "",
    lastName: "",
    email: "",
    contactNumber: "",
    address: "",
    country: "",
    motherLanguage: "",
    role: "",
    profileFunction: "",
    resourceNumber: "",
  });
  const [initialData, setInitialData] = useState({});
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const formatContactNumber = (datanum, pdatanum, fieldName) => {
    if (title === ModalContent.UPDATE_ADMIN) {
      const number = datanum?.[fieldName]?.number
        ? `${datanum?.[fieldName]?.in}${datanum?.[fieldName]?.number}`
        : datanum?.[fieldName];
      return number;
    }
    if (pdatanum[0]?.[fieldName]?.in && pdatanum[0]?.[fieldName]?.number) {
      const number = `${pdatanum[0]?.[fieldName]?.in}${pdatanum[0]?.[fieldName]?.number}`;
      return number;
    }
    return pdatanum[0]?.[fieldName];
  };

  // Usage
  const phoneNumber = formatContactNumber(data, pdata, "contactNumber");
  const emergencyNumber = formatContactNumber(data, pdata, "emergencyContactNumber");

  const handleChange = (e) => {
    const { name, value } = e.target;

    setValues({
      ...values,
      [name]: value,
    });
  };

  const handlePhoneNumberChange = (num, country, type) => {
    setValues({
      ...values,
      [type]: {
        number: num.substring(country.dialCode.length),
        code: country.countryCode.toUpperCase(),
        in: `+${country.dialCode}`,
      },
    });
  };

  useEffect(() => {
    (async () => {
      if (
        (openPersonal && title !== ModalContent.UPDATE_ADMIN && pdata?.length !== 0) ||
        (data && data.length !== 0)
      ) {
        const checkValue = title !== ModalContent.UPDATE_ADMIN ? pdata[0] : data;
        setInitialData(checkValue);

        setValues((prevValues) => ({
          ...prevValues,
          callingName: checkValue?.callingName || prevValues.callingName,
          firstName: checkValue?.firstName || prevValues.firstName,
          lastName: checkValue?.lastName || prevValues.lastName,
          email: checkValue?.email || prevValues.email,
          contactNumber: checkValue?.contactNumber || prevValues.contactNumber,
          emergencyNumber: checkValue?.emergencyNumber ||
            prevValues?.emergencyNumber || {
              number: "",
              code: "",
              in: "",
            },
          address: checkValue?.address || prevValues.address,
          country: checkValue?.country || prevValues.country,
          motherLanguage: checkValue?.motherLanguage || prevValues.motherLanguage,
          role:
            checkValue?.role?.[Constants.MONGOOSE_ID] || prevValues.role?.[Constants.MONGOOSE_ID],
          profileFunction:
            checkValue?.profileFunction?.[Constants.MONGOOSE_ID] ||
            prevValues.profileFunction?.[Constants.MONGOOSE_ID],
          resourceNumber: checkValue?.resourceNumber || prevValues.resourceNumber,
        }));
      }
    })();
  }, [openPersonal]);

  const validation = () => {
    const {
      callingName,
      firstName,
      lastName,
      email,
      address,
      country,
      motherLanguage,
      role,
      profileFunction,
    } = values;

    const tempError = {};

    const callingNameError = Validations.validate("basic", callingName);
    const firstNameError = Validations.validate("basic", firstName);
    const lastNameError = Validations.validate("basic", lastName);
    const emailValidate = Validations.validate("email", email);
    const addressError = Validations.validate("basic2", address);
    const countryError = Validations.validate("basic", country);
    const motherLanguageError = Validations.validate("basic", motherLanguage);
    const languageSpecialCharError = Validations.validate(
      "specialCharacter",
      values.motherLanguage
    );
    const roleError = Validations.validate("basic2", role);
    const profileFunctionError = Validations.validate("basic2", profileFunction);

    if (callingNameError !== "") {
      tempError.callingName = callingNameError;
    }

    if (firstNameError !== "") {
      tempError.firstName = firstNameError;
    }

    if (lastNameError !== "") {
      tempError.lastName = lastNameError;
    }

    if (!email) {
      tempError.email = Constants.REQUIRED;
    } else if (emailValidate) {
      tempError.email = Constants.EMAIL_NOT_VALID;
    }
    if (roleError !== "") {
      tempError.role = roleError;
    }
    if (profileFunctionError !== "") {
      tempError.profileFunction = profileFunctionError;
    }
    if (
      (title !== ModalContent.UPDATE_ADMIN &&
        pdata[0]?.role.title.toLowerCase() === defaultData.ADMIN_ROLE) ||
      (data && data?.role.title.toLowerCase() === defaultData.ADMIN_ROLE)
    ) {
      if (addressError !== "") {
        tempError.address = addressError;
      }

      if (countryError !== "") {
        tempError.country = countryError;
      }
      if (languageSpecialCharError !== "") tempError.motherLanguage = languageSpecialCharError;
      if (motherLanguageError !== "") {
        tempError.motherLanguage = motherLanguageError;
      }
    }
    setErrors(tempError);

    const hasErrors = Object.values(tempError).filter((val) => val !== "").length > 0;

    return !hasErrors;
  };

  const handleClosePersonal = () => {
    setErrors({});
    setOpenPersonal(false);
    setValues(initialData);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    const val = validation();
    if (val) {
      setLoading(true);
      const updatedValues = {};

      if (pdata?.length !== 0 || data?.length !== 0) {
        const checkValue = title !== ModalContent.UPDATE_ADMIN ? pdata[0] : data;
        if (checkValue.firstName !== values.firstName) {
          updatedValues.firstName = values.firstName;
        }
        if (checkValue.lastName !== values.lastName) {
          updatedValues.lastName = values.lastName;
        }
        if (checkValue.email !== values.email) {
          updatedValues.email = values.email;
        }
        if (checkValue.contactNumber !== values.contactNumber) {
          updatedValues.contactNumber = values.contactNumber;
        }
        if (checkValue.emergencyContactNumber !== values.emergencyContactNumber) {
          updatedValues.emergencyContactNumber = values.emergencyContactNumber;
        }
        if (checkValue.address !== values.address) {
          updatedValues.address = values.address;
        }
        if (checkValue.country !== values.country) {
          updatedValues.country = values.country;
        }
        if (checkValue.motherLanguage !== values.motherLanguage) {
          updatedValues.motherLanguage = values.motherLanguage;
        }

        if (checkValue.role?.[Constants.MONGOOSE_ID] !== values.role) {
          updatedValues.role = values.role;
        }
        if (checkValue.profileFunction?.[Constants.MONGOOSE_ID] !== values.profileFunction) {
          updatedValues.profileFunction = values.profileFunction;
        }
        if (checkValue.callingName !== values.callingName) {
          updatedValues.callingName = values.callingName;
        }
        if (checkValue.resourceNumber !== values.resourceNumber) {
          updatedValues.resourceNumber = values.resourceNumber;
        }
      }

      const b = {
        body: updatedValues,
        id:
          title === ModalContent.UPDATE_ADMIN
            ? data[Constants.MONGOOSE_ID]
            : pdata[0][Constants.MONGOOSE_ID],
      };

      const res = await dispatch(updateAdminProfileThunk(b));

      if (res.payload.status === 200) {
        handleClosePersonal();
        await dispatch(
          openSnackbar({
            message: Constants.PROFILE_UPDATED_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setUpdate((prev) => !prev);
      } else if (res.payload.status === 422) {
        const newError = res.payload.data.data.error;
        let tempError = {};
        newError.forEach((item) => {
          tempError = { ...tempError, ...item };
        });
        setErrors(tempError);
      } else if (res.payload.status === 401) {
        const newErrors = {};
        if (res.payload.data?.message) {
          const errorMessage = res.payload.data.message;
          if (errorMessage.includes("Email")) {
            newErrors.email = errorMessage;
          }
          if (errorMessage.includes(Common.RESOURCE_NUMBER)) {
            newErrors.resourceNumber = errorMessage;
          }
        }
        setErrors(newErrors);
      }
      setLoading(false);
    }
  };

  return (
    <Modal
      open={openPersonal}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <MDBox sx={style}>
        <MDBox
          bgColor="info"
          p={3}
          textAlign="center"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          borderRadius="lg"
          sx={{ borderBottomRightRadius: 0, borderBottomLeftRadius: 0, height: pxToRem(72) }}
        >
          <ModalTitle title={title} color="white" />
          <Icon
            sx={{ cursor: "pointer", color: "beige" }}
            fontSize="medium"
            onClick={handleClosePersonal}
          >
            {Icons.CROSS}
          </Icon>
        </MDBox>
        <MDBox
          display="flex"
          flexWrap="wrap"
          px={3}
          py={2}
          sx={{
            maxHeight: 500,
            overflowY: "scroll",
            "::-webkit-scrollbar": { display: "none" },
            scrollbarWidth: "none",
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <MDTypography
                variant="caption"
                sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
              >
                {Common.USUAL_FIRST_NAME_LABEL}*
              </MDTypography>
              <MDInput
                sx={{
                  "& input": {
                    fontSize: "16px",
                    color: "#667085",
                  },
                }}
                name="callingName"
                placeholder={Common.USUAL_FIRST_NAME_LABEL}
                value={values.callingName}
                error={Boolean(errors.callingName)}
                helperText={errors.callingName}
                onChange={handleChange}
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0, color: "#FF2E2E" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <MDTypography
                variant="caption"
                sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
              >
                {Common.FIRST_NAME_LABEL} {Common.AS_PER_PASSPORT_LABEL}*
              </MDTypography>
              <MDInput
                sx={{
                  "& input": {
                    fontSize: "16px",
                    color: "#667085",
                  },
                }}
                name="firstName"
                placeholder="First Name"
                value={values.firstName}
                error={Boolean(errors.firstName)}
                helperText={errors.firstName}
                onChange={handleChange}
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0, color: "#FF2E2E" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <MDTypography
                variant="caption"
                sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
              >
                {Common.LAST_NAME_LABEL} {Common.AS_PER_PASSPORT_LABEL}*
              </MDTypography>
              <MDInput
                sx={{
                  "& input": {
                    fontSize: "16px",
                    color: "#667085",
                  },
                }}
                name="lastName"
                placeholder="Last Name"
                value={values.lastName}
                error={Boolean(errors.lastName)}
                helperText={errors.lastName}
                onChange={handleChange}
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0, color: "#FF2E2E" },
                }}
              />
            </Grid>

            {(title === ModalContent.UPDATE_ADMIN ||
              pdata[0]?.role.title.toLowerCase() === defaultData.ADMIN_ROLE) && (
              <>
                <Grid item xs={12} sm={6}>
                  <FDropdown
                    label={`${Common.COUNTRY_LABEL}*`}
                    menu={countryList}
                    value={values.country}
                    name="country"
                    id="country"
                    handleChange={(name, value, id) =>
                      handleChange({ target: { name, value, id } })
                    }
                    error={Boolean(errors.country)}
                    helperText={errors.country}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <MDTypography
                    variant="caption"
                    sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
                  >
                    {Common.MAIN_LANGUAGE_LABEL}*
                  </MDTypography>
                  <Autocomplete
                    options={["English", "Spanish", "Dutch", "French"]}
                    name="motherLanguage"
                    id="motherLanguage"
                    variant="standard"
                    sx={{
                      mb: 1,
                      "& .MuiAutocomplete-inputRoot": {
                        padding: "4px",
                      },
                    }}
                    freeSolo
                    popupIcon={Icons.DROPDOWN}
                    onChange={(e, value) =>
                      handleChange({ target: { name: "motherLanguage", value } })
                    }
                    value={values.motherLanguage}
                    error={Boolean(errors.motherLanguage)}
                    helperText={errors.motherLanguage}
                    renderInput={(params) => (
                      <MDInput
                        {...params}
                        name="motherLanguage"
                        onChange={(e) => handleChange(e)}
                        placeholder="Enter Language Name"
                        value={values.motherLanguage}
                        error={Boolean(errors.motherLanguage)}
                        helperText={errors.motherLanguage}
                        FormHelperTextProps={{
                          sx: { marginLeft: 0, color: "#FF2E2E" },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <MDTypography
                    variant="caption"
                    sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
                  >
                    {Common.ADDRESS_LABEL}*
                  </MDTypography>
                  <MDInput
                    sx={{
                      mb: 1,
                      "& .MuiInputBase-input": {
                        fontSize: "16px",
                        fontWeight: 400,
                        color: "#667085",
                      },
                    }}
                    multiline
                    autoComplete="off"
                    inputProps={{
                      style: { textTransform: "capitalize", flex: "1", minHeight: "4em" },
                    }}
                    name="address"
                    value={values.address}
                    error={Boolean(errors.address)}
                    helperText={errors.address}
                    FormHelperTextProps={{
                      sx: { marginLeft: 0, color: "#FF2E2E" },
                    }}
                    onChange={handleChange}
                    placeholder="Please Enter Address"
                    fullWidth
                  />
                </Grid>
              </>
            )}

            {(title === ModalContent.UPDATE_ADMIN ||
              pdata[0]?.role.title.toLowerCase() !== defaultData.ADMIN_ROLE) && (
              <>
                <Grid item xs={12} md={6}>
                  <MDTypography
                    variant="caption"
                    mb={1}
                    sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
                  >
                    {Common.EMAIL_LABEL}*
                  </MDTypography>
                  <MDInput
                    sx={{
                      "& input": {
                        fontSize: "16px",
                        color: "#667085",
                      },
                    }}
                    name="email"
                    placeholder="Email"
                    value={values.email}
                    error={Boolean(errors.email)}
                    helperText={errors.email}
                    onChange={handleChange}
                    fullWidth
                    FormHelperTextProps={{
                      sx: { marginLeft: 0, color: "#FF2E2E" },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <MDTypography
                    variant="caption"
                    mb={1}
                    sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
                  >
                    {Common.RESOURCE_NUMBER_LABEL}
                  </MDTypography>
                  <MDInput
                    sx={{
                      "& input": {
                        fontSize: "16px",
                        color: "#667085",
                      },
                    }}
                    name="resourceNumber"
                    placeholder="Resource Number"
                    value={values.resourceNumber}
                    error={Boolean(errors.resourceNumber)}
                    helperText={errors.resourceNumber}
                    onChange={handleChange}
                    onKeyDown={(e) => numericFieldValidation(e)}
                    fullWidth
                    FormHelperTextProps={{
                      sx: { marginLeft: 0, color: "#FF2E2E" },
                    }}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} md={6}>
              <MDTypography
                variant="caption"
                mb={1}
                sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
              >
                {Common.CONTACT_NUMBER_LABEL}*
              </MDTypography>

              <PhoneInput
                country="us"
                value={
                  phoneNumber && typeof phoneNumber === "string"
                    ? phoneNumber.replace(/\D/g, "")
                    : ""
                }
                onChange={(num, country) => handlePhoneNumberChange(num, country, "contactNumber")}
                inputStyle={{
                  width: "100%",
                  height: "43px",
                }}
              />

              <MDTypography variant="caption" color="error" sx={{ marginTop: 2 }}>
                {errors?.contactNumber}
              </MDTypography>
            </Grid>

            {(pdata?.[0]?.role.title.toLowerCase() === defaultData.ADMIN_ROLE ||
              data?.role.title.toLowerCase() === defaultData.ADMIN_ROLE) && (
              <Grid item xs={12} md={6}>
                <MDTypography
                  variant="caption"
                  mb={1}
                  sx={{ fontSize: pxToRem(14), fontWeight: 500, color: "#344054" }}
                >
                  {Common.EMERGENCY_CONTACT_NUMBER_LABEL}*
                </MDTypography>
                <PhoneInput
                  country="us"
                  value={
                    emergencyNumber && typeof emergencyNumber === "string"
                      ? emergencyNumber.replace(/\D/g, "")
                      : ""
                  }
                  onChange={(num, country) =>
                    handlePhoneNumberChange(num, country, "emergencyContactNumber")
                  }
                  inputStyle={{
                    width: "100%",
                    height: "43px",
                  }}
                />
                <MDTypography variant="caption" color="error" sx={{ marginTop: 2 }}>
                  {errors?.emergencyContactNumber}
                </MDTypography>
              </Grid>
            )}

            {roleOptions.length > 0 && (
              <Grid item xs={12} md={6}>
                <CustomAutoComeplete
                  label={`${Common.ROLE_NAME_LABEL}*`}
                  id="role"
                  name="role"
                  hint="Enter Role Name*"
                  handleChange={handleChange}
                  menu={roleOptions}
                  error={Boolean(errors.role)}
                  helperText={errors.role}
                  getOptionLabel={(option) => option.title || ""}
                  value={{
                    title: roleOptions.find((role) => role[Constants.MONGOOSE_ID] === values.role)
                      ?.title,
                  }}
                />
              </Grid>
            )}

            {profileFunctionOptions.length > 0 && (
              <Grid item xs={12} md={6}>
                <CustomAutoComeplete
                  label={`${Common.PROFILE_FUNCTION_LABEL}*`}
                  id="profileFunction"
                  name="profileFunction"
                  hint="Enter Profile Function Name*"
                  handleChange={handleChange}
                  menu={profileFunctionOptions}
                  error={Boolean(errors.profileFunction)}
                  helperText={errors.profileFunction}
                  getOptionLabel={(option) => option.name || ""}
                  value={{
                    name: profileFunctionOptions.find(
                      (profileFunction) =>
                        profileFunction[Constants.MONGOOSE_ID] === values.profileFunction
                    )?.name,
                  }}
                />
              </Grid>
            )}
          </Grid>
        </MDBox>
        <MDBox px={2} mb={2} mr={1}>
          <Grid container direction="row" justifyContent="flex-end" alignItems="center">
            <Grid item xs={0}>
              <MDButton
                variant="contained"
                color="info"
                onClick={handleUpdate}
                style={{ boxShadow: "none", textTransform: "none" }}
              >
                {loading ? "Loading..." : "Update"}
              </MDButton>
            </Grid>
          </Grid>
        </MDBox>
      </MDBox>
    </Modal>
  );
}

Personaldetails.propTypes = {
  openPersonal: PropTypes.bool.isRequired,
  setOpenPersonal: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  pdata: PropTypes.arrayOf(PropTypes.object).isRequired,
  setUpdate: PropTypes.func.isRequired,
  roleOptions: PropTypes.arrayOf(PropTypes.string),
  profileFunctionOptions: PropTypes.arrayOf(PropTypes.string),
};

Personaldetails.defaultProps = {
  roleOptions: [],
  profileFunctionOptions: [],
};

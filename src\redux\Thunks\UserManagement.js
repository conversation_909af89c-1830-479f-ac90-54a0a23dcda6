import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import ApiService from "../ApiService/ApiService";

const UserListThunk = createAsyncThunk("userlist/api", async (param) => {
  const queryString = param ? `?${param}` : "";
  const res = await ApiService.get(`users${queryString}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const UserListbyIdThunk = createAsyncThunk("userlistbyid/api", async (id) => {
  const res = await ApiService.get(`users/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const DeactivateUserThunk = createAsyncThunk("deactivate/api", async (body) => {
  const res = await ApiService.patch(
    `users/change-status`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  );
  return res;
});

export const CreateNewUser = createAsyncThunk("user/create", async (body) => {
  const res = await ApiService.post(
    `users`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateSyncupTime = createAsyncThunk("user/updateSyncupTime", async (paramAndBody) => {
  const res = await ApiService.patch(
    `accounts/${paramAndBody.accountId}`,
    { ...paramAndBody.body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createReview = createAsyncThunk("user/createReview", async (body) => {
  const res = await ApiService.patch(
    `/users/user-rating/${body.userId}`,
    { ...body.data },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteUserRatings = createAsyncThunk("user-rating/delete", async (body) => {
  const res = await ApiService.delete(`users/${body.ratingId}/delete-user-rating/${body.userId}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const nationalityList = createAsyncThunk("user/nationality", async () => {
  const res = await ApiService.get(`nationality`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const logbookListThunk = createAsyncThunk("logbook-list/api", async (body) => {
  const queryString = body.params ? `?${body.params}` : "";
  const res = await ApiService.get(`logbooks/${body.user}${queryString}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createLogNote = createAsyncThunk("user/createLogNote", async (body) => {
  const res = await ApiService.post(
    `/logbooks`,
    { ...body.data },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteUserLogNote = createAsyncThunk("user-logNote/delete", async (id) => {
  const res = await ApiService.delete(`logbooks/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});
export default UserListThunk;

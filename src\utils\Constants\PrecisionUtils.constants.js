// Precision tolerance for completion percentage comparisons
const COMPLETION_PRECISION = 0.001;

export const normalizeCompletion = (value) => {
  const numValue = Number(value ?? 0);
  return Math.round(numValue * 100) / 100;
};

export const isCompleted = (completion) => {
  const normalized = normalizeCompletion(completion);
  return Math.abs(normalized - 100) < COMPLETION_PRECISION;
};

export const getCompletionDisplayValue = (completion) => {
  const normalized = normalizeCompletion(completion);
  return normalized.toFixed(2);
};

export const getCompletionBackgroundColor = (completion, colors) => {
  if (!completion) return "transparent";

  const normalizedValue = normalizeCompletion(completion);

  if (normalizedValue <= 50) {
    return colors.PINK;
  }
  if (normalizedValue <= 75) {
    return colors.LIGHT_PINK;
  }
  if (normalizedValue < 100) {
    return colors.LIGHT_ORANGE1;
  }
  if (isCompleted(normalizedValue)) {
    return colors.LIGHT_GREEN3;
  }
  return "transparent";
};

export const formatCompletionPercentage = (isRequired, completion) => {
  if (isRequired && completion !== 0) {
    return `${getCompletionDisplayValue(completion)}%`;
  }
  return "";
};

const PrecisionUtils = {
  normalizeCompletion,
  isCompleted,
  getCompletionDisplayValue,
  getCompletionBackgroundColor,
  formatCompletionPercentage,
  COMPLETION_PRECISION,
};

export default PrecisionUtils;

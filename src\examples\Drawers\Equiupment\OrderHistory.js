/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import React, { useEffect, useState } from "react";

// Components
import Author from "components/Table/Author";
import Status from "components/Table/Status";

// Utils
import Constants, { defaultData } from "utils/Constants";

// 3rd party library
import moment from "moment";

export default function StockHistoy(OrderHistory) {
  const [orderRow, setOrderRow] = useState([]);

  useEffect(() => {
    if (OrderHistory) {
      const tempRows = OrderHistory?.map((element, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          orderNo: (
            <Author name={element?.pmOrder ? element?.pmOrder?.orderNumber : Constants.NA} />
          ),
          date: <Author name={moment(element?.createdAt).format(defaultData.WEB_DATE_FORMAT)} />,
          status: <Status title={`${element?.pmOrderManageEquipment?.status.replace("-", " ")}`} />,
          outquantity: (
            <Author
              name={element?.wmDispatchQuantity ? element?.wmDispatchQuantity : Constants.NA}
            />
          ),
          inquantity: (
            <Author
              name={element?.wmReceivedQuantity ? element?.wmReceivedQuantity : Constants.NA}
            />
          ),
        };
        return temp;
      });
      setOrderRow([...tempRows]);
    }
  }, [OrderHistory]);
  return {
    orderColumns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Order No.", accessor: "orderNo", width: "5%" },
      { Header: "Order Date", accessor: "date", align: "left" },
      { Header: "Status", accessor: "status", align: "left" },
      { Header: "Out Quantity", accessor: "outquantity", align: "left" },
      { Header: "In Quantity", accessor: "inquantity", align: "left" },
    ],
    orderRow,
  };
}

import React, { useEffect, useState } from "react";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import { Box, Divider, FormControl, Grid, Typography } from "@mui/material";

// Components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import pxToRem from "assets/theme/functions/pxToRem";
import FullScreenImageComponent from "components/ViewFullImage/ViewImage";
import EquipmentDetailDrawer from "examples/Drawers/Equiupment/EquipmentDetail";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import MultiSelectDropdown from "components/Dropdown/MultiSelectDropdown";
import BasicModal from "examples/modal/BasicModal/BasicModal2";
import ResetFilterButton from "components/Buttons/ResetButton";

// Data
import DataTable from "examples/Tables/DataTable";
import InvenotryDataTable from "examples/Tables/DataTable/InventoryTableWithLazyLoading";
import ProductData from "layouts/resources/data/productData";
import StockUpdateData from "layouts/resources/data/stockUpdateData";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { openSnackbar } from "redux/Slice/Notification";
import ProductListThunk, {
  equipmentStockUpdateThunk,
  exportInventoryExcelData,
  equipmentLocationThunk,
  typeConvertThunk,
} from "redux/Thunks/Equipment";
import configThunk, { equipmentConfig } from "redux/Thunks/Config";
import {
  reloadData,
  resetListState,
  updateStockCheckbox,
  updateQuantity,
  deleteStock,
} from "redux/Slice/Equipment";

// Constants
import Constants, {
  Icons,
  Colors,
  ModalContent,
  PageTitles,
  defaultData,
  ButtonTitles,
  Common,
  FiltersModuleName,
} from "utils/Constants";
import SearchBar from "components/Search/SearchInTable";
import { paramCreater } from "utils/methods/methods";
import CustomDrawer from "components/Drawer/CustomDrawer";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import InventoryRegister from "./InventoryRegisterForm/InventoryRegister";

const initialLocation = {
  loading: false,
  open: false,
  equipmentName: "",
  quantity: 0,
  totalQuantity: 0,
  id: "",
  data: [],
};

function ProductDetails() {
  const dispatch = useDispatch();
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[8]?.screensInfo?.agreement;
  const productList = useSelector((state) => state.product);
  const stockData = useSelector((state) => state.product);
  const [tablePagination, setTablePagination] = useState({
    page: defaultData.PAGE,
    perPage: defaultData.PER_PAGE_6,
  });
  let debounceTimeout;
  const [filters, setFilters] = useState([
    {
      inputLabel: FiltersModuleName.MISSING,
      list: [
        { [Constants.MONGOOSE_ID]: "all", title: "All" },
        { [Constants.MONGOOSE_ID]: "qr", title: "QR" },
        { [Constants.MONGOOSE_ID]: "certificate", title: "Certificate" },
        { [Constants.MONGOOSE_ID]: "certificate_expired", title: "Certificate Expired" },
        {
          [Constants.MONGOOSE_ID]: "certificate_expire_in_30",
          title: "Certificate Expiring in 30 days",
        },
        {
          [Constants.MONGOOSE_ID]: "certificate_expire_in_60",
          title: "Certificate Expiring in 60 days",
        },
      ],
      selectedValue: "all",
    },
    {
      inputLabel: FiltersModuleName.CONDITION,
      list: [
        { [Constants.MONGOOSE_ID]: "ok", title: "OK" },
        { [Constants.MONGOOSE_ID]: "maintenance", title: "Maintenence" },
        { [Constants.MONGOOSE_ID]: "write-off", title: "Write Off" },
      ],
      selectedValue: ["ok", "maintenance"],
    },
    {
      inputLabel: "Category",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      isLoading: false,
    },
    {
      inputLabel: "Type",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      isLoading: false,
    },
    {
      inputLabel: "Product Number",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      isLoading: false,
    },
    {
      inputLabel: "Name",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      isLoading: false,
    },
    {
      inputLabel: "Serial number",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
      isLoading: false,
    },
  ]);
  const [stockShow, setStockShow] = useState(false);
  const [openStockEditModal, setOpenStockEditModal] = useState(false);
  const [openInventoryModal, setOpenInventoryModal] = useState({ right: false });
  const [exportLoading, setExportLoading] = useState(false);
  const [shouldUpdateState, setShouldUpdateState] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [equipmentId, setEquipmentId] = useState("");
  const [equipmentLocation, setEquipmentLocation] = useState(initialLocation);
  const [openPermanentType, setOpenPermanentType] = useState({
    open: false,
    loading: false,
    data: {},
    newType: "",
    oldType: "",
  });
  const [equipmentOptionsList, setEquipmentOptionsList] = useState([]);
  const [fullScreenImage, setFullScreenImage] = useState(null);
  // use for handling equipment drawer
  const [equipmentAnchor, setEquipmentAnchor] = useState({ right: false });
  const [equipmentDrawerId, setEquipmentDrawerId] = useState("");

  // Common Function to Create Params for get Product API
  const createParamData = (filter, pagination = {}) => {
    const filterMapping = [
      { key: "missing", value: filter[0]?.selectedValue },
      {
        key: "condition",
        value: filter[1]?.selectedValue?.length > 0 ? filter[1]?.selectedValue?.join(",") : "",
      },
      { key: "equipmentCategory", value: filter[2]?.selectedValue },
      { key: "type", value: filter[3]?.selectedValue },
      { key: "equipmentNumber", value: filter[4]?.selectedValue },
      { key: "name", value: filter[5]?.selectedValue },
      { key: "serialNumber", value: filter[6]?.selectedValue },
    ];

    return filterMapping.reduce(
      (acc, { key, value }) => {
        if (
          value &&
          typeof value === "string" &&
          value?.trim() !== "" &&
          value.toLowerCase() !== "select" &&
          value?.toLowerCase() !== "all"
        ) {
          return { ...acc, [key]: value?.trim() };
        }
        return acc;
      },
      { ...pagination }
    );
  };

  const fetchProductList = async (filterValue = filters) => {
    setTablePagination({ ...tablePagination, page: defaultData.PAGE });

    const pagination = { page: defaultData.PAGE, perPage: tablePagination.perPage };
    const paramData = createParamData(filterValue, pagination);

    await dispatch(ProductListThunk(paramCreater(paramData)));
  };

  const getEquipmentConfigFunc = async () => {
    await dispatch(equipmentConfig());
  };

  const handleImageFullView = (imageUrl) => {
    setFullScreenImage(imageUrl);
  };
  const handleCloseFullView = () => {
    setFullScreenImage(null);
  };

  // open Equipment drawer from rigth
  const handleOpenEquipmentDetailDrawer = async (id) => {
    setEquipmentAnchor({ right: true });
    setEquipmentDrawerId(id);
  };

  const handleCloseEquipmentDetailDrawer = async (id) => {
    setEquipmentAnchor({ right: false });
    setEquipmentDrawerId(id);
  };

  // Open Inventory drawer
  const openInventoryRegisterModalFunc = (open) => {
    setOpenInventoryModal({ right: open });
  };

  const handleEditDrawerOpen = (item) => {
    setEquipmentId(item);
    setOpenInventoryModal({ right: true });
  };

  const handleInventoryDrawerClose = () => {
    setOpenInventoryModal({ right: false });
    setEquipmentId("");
  };

  const handleStockUpdateCheckboxChange = async (element, checked) => {
    const elemObj = {
      ...element,
      stockQuantity: 0,
      totalStock: Number(element.quantity) || 0,
    };
    await dispatch(updateStockCheckbox({ equipmentData: elemObj, checked }));
  };

  const handleStockChange = async (e, Id, name) => {
    const { value = 0 } = e.target;
    const updatedStockValue =
      name === "validateStockQuantity" && value === "" ? 0 : parseInt(value, 10) || 0;

    await dispatch(updateQuantity({ stockQuantity: updatedStockValue, equipmentId: Id }));
  };

  const handleUpdateStock = async () => {
    const isValid = !stockData.stockData.some(
      (item) => item.stockQuantity === 0 || item.totalStock < 0
    );
    if (isValid) {
      const body = stockData.stockData.map((item) => ({
        equipment: item[Constants.MONGOOSE_ID],
        quantity: item?.totalStock,
      }));

      setButtonLoading(true);
      setDisableSubmit(true);

      const res = await dispatch(equipmentStockUpdateThunk(body));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenStockEditModal(false);
        setStockShow(false);
        setButtonLoading(false);
        await dispatch(resetListState());
        await dispatch(fetchProductList());
        await dispatch(
          openSnackbar({
            message: Constants.STOCKS_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else {
      await dispatch(
        openSnackbar({
          message: Constants.INVALID_VALUE,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleDeleteStock = async (deleteId) => {
    await dispatch(deleteStock({ deleteId }));
  };

  // function to open equipment location modal
  const handleOpenEquipmentLocation = async (data) => {
    setEquipmentLocation({ open: true, loading: true });
    const res = await dispatch(equipmentLocationThunk(data.id));
    setEquipmentLocation({ open: true, loading: false });
    if (res.payload.status === Common.API_STATUS_200) {
      setEquipmentLocation({
        open: true,
        equipmentName: data.name,
        quantity: data.quantity,
        totalQuantity: res?.payload?.data?.data?.totalQuantity,
        id: data.id,
        data: res?.payload?.data?.data?.data,
      });
    } else {
      setEquipmentLocation(initialLocation);
      await dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    const getEquipmentList = Array.isArray(ConfigData?.screens?.[8]?.screensInfo?.properties)
      ? ConfigData.screens[8].screensInfo.properties
      : [];

    const equipmentItem = getEquipmentList.find((item) => item.id === Common.EQUIPMENT_TYPE);
    const createDropdownListFunc = (listArr = []) => {
      const temp = listArr.map((item) => ({
        title: item.title,
        [Constants.MONGOOSE_ID]: item.id,
      }));

      return temp;
    };

    if (equipmentItem) {
      const withoutTempItems = equipmentItem.options.filter(
        (item) => item?.currency !== Common.TEMP_CURRENCY
      );
      const temp = createDropdownListFunc(withoutTempItems);
      setEquipmentOptionsList(temp);
    }
  }, [ConfigData]);

  const handleOpenPermanentType = async (data) => {
    setOpenPermanentType({
      open: true,
      loading: false,
      data,
      newType: "",
      equipmentId: data?.[Constants.MONGOOSE_ID],
      oldType: data?.equipmentType[Constants.MONGOOSE_ID],
    });
  };

  const handleTypeConversion = async () => {
    if (openPermanentType?.newType === "") {
      await dispatch(
        openSnackbar({
          message: Constants.TYPE_REQUIRED_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }
    const payload = {
      id: openPermanentType?.equipmentId,
      body: {
        oldEquipmentType: openPermanentType?.oldType,
        newEquipmentType: openPermanentType?.newType,
      },
    };
    setOpenPermanentType({ ...openPermanentType, loading: true });
    const res = await dispatch(typeConvertThunk(payload));
    setOpenPermanentType({ ...openPermanentType, loading: false });
    if (res.payload.status === Common.API_STATUS_200) {
      setOpenPermanentType({ open: false, loading: false, data: {} });
      await dispatch(reloadData());
      await dispatch(fetchProductList());
      await dispatch(
        openSnackbar({
          message: Constants.TYPE_CONVERSION_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else {
      await dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  // Equipment Table data
  const { columns, rows } = ProductData({
    productList: productList.list,
    handleEditDrawerOpen,
    handleOpenEquipmentDetailDrawer,
    permissions: permission,
    stockShow,
    updateStockCheckboxFunc: handleStockUpdateCheckboxChange,
    stockData: stockData.stockData,
    handleOpenEquipmentLocation,
    handleOpenPermanentType,
  });

  const { stockColumns, stockRows } = StockUpdateData(
    stockData.stockData,
    handleStockChange,
    handleDeleteStock
  );

  const handleTablePagination = async (pageNumber) => {
    if (productList.pagination?.completed?.includes(pageNumber)) {
      setTablePagination((prev) => ({ ...prev, page: pageNumber }));
      return;
    }
    const pagination = { page: pageNumber, perPage: tablePagination.perPage };
    const paramData = createParamData(filters, pagination);
    const res = await dispatch(ProductListThunk(paramCreater(paramData)));

    if (res.payload.status === Common.API_STATUS_200) {
      setTablePagination((prev) => ({ ...prev, page: pageNumber }));
    }
  };

  const handleReload = async () => {
    await dispatch(reloadData());
    fetchProductList();
  };

  const handleFilterType = async (e) => {
    const { name, value } = e.target;
    if (value === "") return;
    let newFilters;
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === name) {
          return {
            ...filter,
            selectedValue: value,
          };
        }
        return filter;
      });
      newFilters = updatedFilters;
      return updatedFilters;
    });

    await fetchProductList(newFilters);
  };

  const handleSearch = async (searchValue = filters, filterIndex) => {
    setFilters((prev) => {
      const temp = [...prev];
      temp[filterIndex].isLoading = true;
      return temp;
    });

    const pagination = { page: 0, perPage: tablePagination.perPage };
    const paramData = createParamData(searchValue, pagination);

    try {
      const res = await dispatch(ProductListThunk(paramCreater(paramData)));
      const temp = [...searchValue];

      let suggestions;
      const inventoryDataList = res?.payload?.data?.data?.inventoryData || [];
      switch (searchValue[filterIndex].inputLabel) {
        case "Name":
          suggestions = inventoryDataList.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item.name,
          }));
          break;
        case "Serial number":
          suggestions = inventoryDataList.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item?.serialNumber !== null ? item?.serialNumber : "",
          }));
          break;
        case "Product Number":
          suggestions = inventoryDataList.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item.equipmentNumber,
          }));
          break;
        case "Type":
          suggestions = inventoryDataList.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item.equipmentType.type,
          }));
          break;
        case "Category":
          suggestions = inventoryDataList.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item.equipmentType.equipmentCategory[0].name,
          }));
          break;
        default:
          suggestions = [];
      }

      const uniqueSuggestions = suggestions.reduce((acc, current) => {
        const duplicate = acc.find((item) => item.title === current.title);
        if (!duplicate) {
          acc.push(current);
        }
        return acc;
      }, []);

      // Add the 'All' option and update the filter list
      temp[filterIndex].list = [
        { [Constants.MONGOOSE_ID]: "all", title: "All" },
        ...uniqueSuggestions,
      ];
      temp[filterIndex].isLoading = false;

      setFilters(temp);
    } catch (error) {
      setFilters((prev) => {
        const temp = [...prev];
        temp[filterIndex].isLoading = false;
        return temp;
      });
    }
  };

  const debounce =
    (func, delay) =>
    (...args) => {
      const context = this;
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => func.apply(context, args), delay);
    };

  const debouncedHandleSearch = debounce((e, filterIndex) => {
    setTablePagination({ ...tablePagination, page: 0 });
    const temp = [...filters];
    temp[filterIndex].selectedValue = e.target.value;
    handleSearch(temp, filterIndex);
  }, 300);

  const handleReset = async () => {
    await dispatch(reloadData());
    const tempFilters = filters.map((filter) => {
      if (filter.inputLabel === FiltersModuleName.CONDITION) {
        return { ...filter, selectedValue: ["ok", "maintenance"] };
      }
      return { ...filter, selectedValue: "all" };
    });
    setFilters(tempFilters);
    await fetchProductList(tempFilters);
  };

  useEffect(() => {
    dispatch(configThunk());
    fetchProductList(filters);
  }, [shouldUpdateState]);

  useEffect(() => {
    getEquipmentConfigFunc();
    return dispatch(resetListState());
  }, []);

  const handleInventoryExport = async () => {
    const paramData = createParamData(filters);
    const body = {
      param: new URLSearchParams(paramData).toString(),
      selectedState: {},
    };
    setExportLoading(true);
    const res = await dispatch(exportInventoryExcelData(body));
    setExportLoading(false);
    if (res.payload.status === Common.API_STATUS_200) {
      const url = window.URL.createObjectURL(res.payload.data);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Invenotry.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
      await dispatch(
        openSnackbar({
          message: Constants.INVENTORY_EXCEL_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else {
      await dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };
  return (
    <DashboardLayout module={defaultData.EQUIPMENT_SCREEN_ID}>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between" alignItems="center">
        <PageTitle title={PageTitles?.EQUIPMENT} />
        <MDBox display="flex" flexDirection="row">
          {permission?.create && (
            <CustomButton
              title="Register"
              icon="add_circle_outline"
              background="#191A51"
              color="#ffffff"
              openModal={openInventoryRegisterModalFunc}
            />
          )}

          <Divider
            orientation="vertical"
            sx={{
              backgroundColor: "var(--gray-300, #D0D5DD)",
              height: "auto",
              marginLeft: pxToRem(16),
              marginRight: 0,
            }}
          />

          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleReload}
          />
        </MDBox>
      </MDBox>

      <Divider sx={{ marginTop: 2 }} />

      <MDBox display="flex" justifyContent="space-between">
        <MDBox>
          <MDBox
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "start",
              alignItems: "flex-end",
            }}
            mx={0}
          >
            {/* Search bar */}
            {filters.slice(2).map((item, index) => (
              <SearchBar
                freeSolos
                width={pxToRem(200)}
                key={item.inputLabel.replace(" ", "")}
                options={item?.list.map((val) => val?.title) || []}
                filters={filters}
                label={item?.inputLabel}
                value={
                  item?.selectedValue
                    ? item.selectedValue.charAt(0).toUpperCase() +
                      item.selectedValue.substring(1).toLowerCase()
                    : ""
                }
                placeholder={item?.inputLabel}
                isLoading={item?.isLoading}
                debouncedHandleSearch={(e) => debouncedHandleSearch(e, index + 2)}
                handleFilterChange={(e, value) =>
                  debouncedHandleSearch({ target: { name: item.inputLabel, value } }, index + 2)
                }
              />
            ))}

            <MDBox
              sx={{
                display: "flex",
                flexWrap: "wrap",
                justifyContent: "start",
                alignItems: "flex-end",
              }}
            >
              {filters
                .slice(0, 2)
                .filter((val) => val.inputLabel !== "Search")
                ?.map((val) => {
                  if (val.inputLabel === FiltersModuleName.MISSING) {
                    return (
                      <FilterDropdown
                        key={val.inputLabel}
                        label={val.inputLabel}
                        name={val.inputLabel}
                        value={val?.selectedValue}
                        handleChange={handleFilterType}
                        menu={val.list}
                      />
                    );
                  }
                  if (val.inputLabel === FiltersModuleName.CONDITION) {
                    return (
                      <FormControl variant="standard" size="small">
                        <MultiSelectDropdown
                          key={val.inputLabel}
                          id={val.inputLabel}
                          label={val.inputLabel}
                          name={val.inputLabel}
                          defaultValue={val?.selectedValue}
                          menu={val.list}
                          values={val.selectedValue}
                          valueStyle={{ backgroundColor: Colors.WHITE }}
                          labelStyle={{ fontWeight: 600 }}
                          handleChange={(name, value) =>
                            handleFilterType({ target: { name, value } })
                          }
                          hint={Common.SELECT_CONDITION}
                        />
                      </FormControl>
                    );
                  }
                  return null;
                })}

              <MDBox mr={4}>
                <BasicButton
                  title={exportLoading ? ButtonTitles.EXPORTING : ButtonTitles.EXPORT}
                  icon={Icons.EXPORT}
                  background={Colors.WHITE}
                  border
                  disabled={exportLoading}
                  color={Colors.PRIMARY}
                  action={handleInventoryExport}
                />
              </MDBox>

              <ResetFilterButton handleReset={handleReset} style={{ marginLeft: "1rem" }} />

              {permission?.update && (
                <BasicButton
                  title={!stockShow ? ButtonTitles.ADD_STOCKS : ButtonTitles.CANCEL}
                  icon={!stockShow ? Icons.ACCEPT2 : Icons.CROSS4}
                  background={Colors.WHITE}
                  border
                  borderColor={Colors.PRIMARY}
                  color={Colors.PRIMARY}
                  action={() => setStockShow((prev) => !prev)}
                  style={{ marginTop: pxToRem(45) }}
                />
              )}

              {stockShow && stockData?.stockData?.length > 0 && (
                <BasicButton
                  title={ButtonTitles.EDIT_STOCKS}
                  icon={Icons.EDIT}
                  background={Colors.WHITE}
                  color={Colors.PRIMARY}
                  borderColor={Colors.PRIMARY}
                  border
                  action={() => setOpenStockEditModal(true)}
                />
              )}
            </MDBox>
          </MDBox>
        </MDBox>
      </MDBox>

      <MDBox mt={3} mb={5} key={productList.list}>
        {/* Equipments Table */}
        <InvenotryDataTable
          table={{ columns, rows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.PER_PAGE_6 }}
          showTotalEntries={false}
          pagination={{ variant: "gradient", color: "info" }}
          totalRecords={productList.inventoryListTotalRecords}
          loading={productList.loading}
          licenseRequired
          currentPage={tablePagination.page}
          handleTablePagination={handleTablePagination}
          handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
        />
      </MDBox>

      {/* View Equipment Details Drawer */}
      {equipmentAnchor.right && (
        <EquipmentDetailDrawer
          equipmentAnchor={equipmentAnchor}
          equipmentId={equipmentDrawerId}
          closeDrawer={handleCloseEquipmentDetailDrawer}
          handleViewImage={handleImageFullView}
          equipmentKey="equipmentDrawer"
        />
      )}

      <FullScreenImageComponent
        fullScreenImage={fullScreenImage}
        handleCloseFullView={handleCloseFullView}
        src={fullScreenImage}
      />

      {/* Stock Edit Quantity Modal */}
      {openStockEditModal && (
        <BasicModal
          title={ModalContent.UPDATE_STOCKS}
          open={openStockEditModal}
          handleClose={() => setOpenStockEditModal(false)}
          handleAction={handleUpdateStock}
          disabled={stockData.stockData?.length === 0 || disableSubmit}
          actionButton={buttonLoading === false ? ButtonTitles.SAVE : ButtonTitles.LOADING}
          py={0}
          minWidth="65%"
        >
          <MDBox
            sx={{
              minWidth: "100%",
              maxHeight: "400px",
              overflowY: "scroll",
              "::-webkit-scrollbar": {
                width: "5px",
              },
              "::-webkit-scrollbar-thumb": {
                background: "gray",
              },
              scrollbarWidth: "thin",
              scrollbarColor: "gray transparent",
            }}
          >
            <DataTable
              table={{ columns: stockColumns, rows: stockRows }}
              isSorted={false}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
            />
          </MDBox>
        </BasicModal>
      )}

      {/* Location Modal */}
      <BasicModal
        title="Equipment Location"
        open={equipmentLocation.open}
        handleClose={() => setEquipmentLocation(initialLocation)}
        handleAction={() => setEquipmentLocation(initialLocation)}
        actionButton={ButtonTitles.CANCEL}
        py={0}
        width={pxToRem(500)}
      >
        <Box
          sx={{
            minWidth: equipmentLocation?.data?.length > 5 ? 500 : 350, // Reduced minWidth
            p: 1,
            borderRadius: 1,
            bgcolor: "background.paper",
          }}
        >
          {equipmentLocation?.data?.length === 0 ? (
            <Typography
              variant="h6"
              sx={{ textAlign: "center", color: "#3D3E80", fontWeight: 700 }}
            >
              No Data Available
            </Typography>
          ) : (
            <>
              {/* Displaying equipment title and total quantity */}
              {equipmentLocation.loading ? (
                <MDBox
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                >
                  {Icons.LOADING2}
                </MDBox>
              ) : (
                <Box sx={{ mb: 1, textAlign: "left" }}>
                  {" "}
                  {/* Reduced margin bottom */}
                  <Box>
                    <Typography
                      variant="body2"
                      fontWeight={600}
                      sx={{
                        color: "#3D3E80",
                        textTransform: "capitalize",
                        wordWrap: "break-word",
                        whiteSpace: "normal",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {equipmentLocation.equipmentName}
                    </Typography>
                    <MDBox display="flex" justifyContent="flex-start" alignItems="center">
                      <Typography variant="body2" fontWeight={500} sx={{ color: "#FF914D" }}>
                        Available Quantity:{" "}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontWeight={500}
                        sx={{ color: "#3D3E80", marginLeft: "5px" }}
                      >
                        {equipmentLocation.quantity}{" "}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontWeight={500}
                        sx={{ color: "#FF914D", marginLeft: "35px" }}
                      >
                        Total Quantity:
                      </Typography>
                      <Typography
                        variant="body2"
                        fontWeight={500}
                        sx={{ color: "#3D3E80", marginLeft: "5px" }}
                      >
                        {equipmentLocation.totalQuantity}{" "}
                      </Typography>
                    </MDBox>
                  </Box>
                </Box>
              )}

              {/* Location List */}
              <Grid
                container
                spacing={1}
                sx={{
                  maxHeight: "300px",
                  overflow: "auto",
                  padding: 1,
                  "::-webkit-scrollbar": {
                    width: "5px",
                  },
                  "::-webkit-scrollbar-thumb": {
                    background: Colors.LIGHT_GRAY,
                  },
                  scrollbarWidth: "thin",
                  scrollbarColor: "gray transparent",
                }}
              >
                {equipmentLocation?.data?.map((item) => (
                  <Grid item xs={6} sm={12} key={item.location}>
                    {" "}
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        bgcolor: "#F5F5FA",
                      }}
                    >
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        sx={{ color: "#3D3E80", textTransform: "capitalize" }}
                      >
                        {item?.tracker}
                      </Typography>
                      <Typography variant="body2" fontWeight={500} sx={{ color: "#FF914D" }}>
                        Quantity: {item?.quantity}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </>
          )}
        </Box>
      </BasicModal>

      {/* Permanent Type Modal */}
      <BasicModal
        title={ModalContent.TYPE_CONVERSION_TITLE}
        open={openPermanentType.open}
        handleClose={() => setOpenPermanentType({ open: false })}
        handleAction={handleTypeConversion}
        actionButton={
          openPermanentType.loading === false ? ButtonTitles.SAVE : ButtonTitles.LOADING
        }
        py={0}
      >
        <MDBox mt={1}>
          <Typography variant="body2" fontWeight={500}>
            Choose equipment type to set as permanent{" "}
          </Typography>
          <CustomAutoComplete
            name={Common.PROJECT_ORDER_EQUIPMENT_TYPE_NAME}
            id={Common.PROJECT_ORDER_EQUIPMENT_TYPE_NAME}
            hint={Common.PROJECT_ORDER_EQUIPMENT_TYPE_LABEL}
            getOptionLabel={(option) => option.title || ""}
            menu={equipmentOptionsList}
            value={{
              title: equipmentOptionsList.find(
                (item) => item[Constants.MONGOOSE_ID] === openPermanentType?.newType
              )?.title,
            }}
            handleChange={(e) =>
              setOpenPermanentType({ ...openPermanentType, newType: e.target.value })
            }
          />
        </MDBox>
      </BasicModal>
      {/* Drawer to Register or Edit Inventory Equipment */}
      <CustomDrawer
        defaultAnchor={openInventoryModal}
        width="80%"
        onDrawerClose={handleInventoryDrawerClose}
      >
        <InventoryRegister
          closeDrawerFunc={handleInventoryDrawerClose}
          equipmentId={equipmentId}
          setShouldUpdateState={setShouldUpdateState}
        />
      </CustomDrawer>
    </DashboardLayout>
  );
}

export default ProductDetails;

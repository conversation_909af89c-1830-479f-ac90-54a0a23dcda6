import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";
import moment from "moment";

// Material Design
import { IconButton, Tabs, styled, Tab, Tooltip } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import CustomDrawer from "components/Drawer/CustomDrawer";
import MDTypography from "components/MDTypography";
import DprView from "layouts/dpr/dprView";

// Redux
import { getDprDetails } from "redux/Thunks/Dpr";

// Assets
import pxToRem from "assets/theme/functions/pxToRem";
import bgIm from "assets/images/t2CCNu53AN.gif";

// Constants
import { Colors, Icons, defaultData } from "utils/Constants";
import { formatKeyName } from "utils/methods/methods";

const CustomTabs = styled(Tabs)({
  backgroundColor: Colors.WHITE,
  display: "flex",
  justifyContent: "space-between",
  width: "100%",
  flexWrap: "wrap",
  padding: 0,
  margin: 0,
  borderBottom: `1px solid ${Colors.LIGHT_GRAY}`,
  "& .MuiTabs-indicator": {
    backgroundColor: Colors.PRIMARY,
    height: 3,
  },
});

const CustomTab = styled(Tab)({
  textTransform: "none",
  minWidth: "auto",
  fontWeight: 600,
  fontSize: pxToRem(14),
  padding: `${pxToRem(12)} ${pxToRem(24)}`,
  color: Colors.DARK_GRAY,
  "&:hover": {
    color: Colors.PRIMARY,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: Colors.PRIMARY,
    fontWeight: 700,
  },
});

export default function ViewDpr({ viewDprAnchor, setDprAnchor, dprViewId }) {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(0);
  const { dprData, allDprVersionData } = useSelector((state) => state.dprs);

  const [allDPRVersionDataList, setAllDPRVersionDataList] = useState([]);

  const adjustVersionDataArray = (versionDataArr = [], prevVersion) => {
    const prevVersionNumber = parseInt(prevVersion.replace("v", ""), 10);

    if (versionDataArr.length === prevVersionNumber) {
      setAllDPRVersionDataList(versionDataArr);
      return;
    }

    // If array length is greater than prevVersionNumber, removing the second last element.
    if (versionDataArr.length > prevVersionNumber) {
      const updatedArray = [...versionDataArr];
      updatedArray.splice(updatedArray.length - 2, 1);
      setAllDPRVersionDataList(updatedArray);
      return;
    }

    setAllDPRVersionDataList(versionDataArr);
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      await dispatch(getDprDetails(dprViewId));
      setLoading(false);
    })();
  }, [dprViewId]);

  useEffect(() => {
    if (allDprVersionData && allDprVersionData.length > 0) {
      const lastVersion = dprData.prevVersion || dprData.version;
      adjustVersionDataArray(allDprVersionData, lastVersion);
    }
  }, [allDprVersionData, dprData]);

  return (
    <CustomDrawer
      defaultAnchor={viewDprAnchor}
      onDrawerClose={() => setDprAnchor({ right: false })}
    >
      {!loading ? (
        <MDBox display="flex" flexDirection="column" gap={1}>
          <MDBox
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            gap={2}
            py={1}
            px={3}
            borderBottom={Colors.PRIMARY}
            sx={{
              position: "sticky",
              top: 0,
              backgroundColor: Colors.PRIMARY,
              zIndex: 10,
            }}
          >
            <MDBox display="flex" gap={0} alignItems="center">
              <IconButton
                sx={{
                  borderRadius: 0,
                  fontSize: pxToRem(18),
                  height: "min-content",
                  color: "#fff",
                }}
                onClick={() => setDprAnchor({ right: false })}
              >
                {Icons.CROSS}
              </IconButton>
              <MDTypography
                sx={{
                  fontSize: pxToRem(16),
                  fontWeight: 600,
                  color: "#fff",
                  padding: "5px 0px",
                }}
              >
                {dprData?.project || "DPR Details"}
              </MDTypography>
            </MDBox>
          </MDBox>
          <MDBox
            sx={{
              display: "flex",
              flexDirection: "column",
              width: "100%",
              px: { xs: 2, md: 4 },
            }}
          >
            <MDBox display="flex" flexWrap="wrap" justifyContent="space-between" gap={2}>
              {/* DPR Date */}
              <MDBox flex={1} minWidth="100px">
                <MDTypography
                  variant="body1"
                  sx={{ fontWeight: 700, fontSize: "1rem", color: "#2C3E50" }}
                >
                  DPR Date
                </MDTypography>
                <MDBox sx={{ fontSize: "1rem", fontWeight: 400, color: "#344054" }}>
                  {moment(dprData?.date).format(defaultData.WEB_DATE_FORMAT) || "-"}
                </MDBox>
              </MDBox>

              {/* DPR Number */}
              <MDBox flex={1} minWidth="100px">
                <MDTypography
                  variant="body1"
                  sx={{ fontWeight: 700, fontSize: "1rem", color: "#2C3E50" }}
                >
                  DPR Number
                </MDTypography>
                <MDBox sx={{ fontSize: "1rem", fontWeight: 400, color: "#344054" }}>
                  {dprData?.dprNumber || "-"}
                </MDBox>
              </MDBox>

              {/* Client */}
              <MDBox flex={1} minWidth="300px">
                <MDTypography
                  variant="body1"
                  sx={{ fontWeight: 700, fontSize: "1rem", color: "#2C3E50" }}
                >
                  Client
                </MDTypography>
                <MDBox sx={{ fontSize: "1rem", fontWeight: 400, color: "#344054" }}>
                  {dprData?.client || "-"}
                </MDBox>
              </MDBox>

              {/* Project */}
              <MDBox flex={1} minWidth="300px">
                <MDTypography
                  variant="body1"
                  sx={{ fontWeight: 700, fontSize: "1rem", color: "#2C3E50" }}
                >
                  Project
                </MDTypography>
                <MDBox
                  sx={{
                    fontSize: "1rem",
                    fontWeight: 400,
                    color: "#344054",
                  }}
                >
                  {dprData?.project.length > defaultData.MEDIUM_CONTENT_LENGTH ? (
                    <Tooltip title={dprData?.project || "-"} arrow>
                      <span>
                        {`${dprData?.project?.slice(0, defaultData.MEDIUM_CONTENT_LENGTH)}...`}
                      </span>
                    </Tooltip>
                  ) : (
                    dprData?.project || "-"
                  )}
                </MDBox>
              </MDBox>
            </MDBox>

            {/* Status Section */}
            <MDBox display="flex" flexDirection="column" alignItems="flex-start" mt={2}>
              <MDTypography
                variant="body1"
                sx={{ fontWeight: 700, fontSize: "1rem", color: "#2C3E50" }} // Bold title
              >
                Status
              </MDTypography>
              <MDBox
                px={2}
                py={0.8}
                borderRadius="lg"
                sx={{
                  backgroundColor: Colors.LIGHT_BLUE,
                  color: Colors.PRIMARY,
                  fontSize: "1rem",
                  fontWeight: 600,
                  textAlign: "center",
                  minWidth: "120px",
                }}
              >
                {formatKeyName(dprData?.status) || "-"}
              </MDBox>
            </MDBox>
          </MDBox>

          <MDBox px={4}>
            <CustomTabs
              value={selectedVersion}
              onChange={(e, val) => setSelectedVersion(val)}
              aria-label="version tabs"
            >
              {allDPRVersionDataList
                .slice()
                .reverse()
                .map((version) => (
                  <CustomTab
                    key={version.prevVersion || version.version}
                    label={
                      dprData?.prevVersion !== dprData?.version
                        ? `${version.prevVersion}`
                        : `${version.version}`
                    }
                    sx={{ minWidth: "auto", px: 2 }}
                    style={{
                      fontSize: pxToRem(16),
                      fontWeight: 600,
                      color: "white",
                    }}
                  />
                ))}
            </CustomTabs>

            {allDPRVersionDataList
              .slice()
              .reverse()
              .map(
                (version, index) =>
                  selectedVersion === index && (
                    <MDBox key={version.prevVersion || version.version}>
                      <DprView dprData={version} />
                    </MDBox>
                  )
              )}
          </MDBox>
        </MDBox>
      ) : (
        <MDBox marginTop="40vh" display="flex" justifyContent="center" alignItems="center">
          <MDBox
            component="img"
            src={bgIm}
            alt="Wind Turbine"
            sx={{
              objectFit: "cover",
            }}
          />
        </MDBox>
      )}
    </CustomDrawer>
  );
}

ViewDpr.propTypes = {
  viewDprAnchor: PropTypes.objectOf(PropTypes.any).isRequired,
  setDprAnchor: PropTypes.func.isRequired,
  dprViewId: PropTypes.string.isRequired,
};

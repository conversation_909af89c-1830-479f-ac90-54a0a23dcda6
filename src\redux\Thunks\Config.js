import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import ApiService from "redux/ApiService/ApiService";
import { normalizeParamsAndAddValues } from "utils/methods/methods";

const configThunk = createAsyncThunk("config/api", async (params) => {
  const projectStatusObj = { projectStatus: ["open", "completed"].join(",") };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);
  const requestURL = updatedParams?.toString() ? `files/config?${updatedParams}` : "files/config";

  const res = await ApiService.get(requestURL, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const equipmentConfig = createAsyncThunk("equipment-config/api", async () => {
  const res = await ApiService.get("files/equipment", {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export default configThunk;

import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import licenseListThunk, { accountLicenseThunk } from "redux/Thunks/License";
import Constants from "utils/Constants";

const initialState = {
  licenseLoading: Constants.IDLE,
  ownerLicenseLoading: Constants.IDLE,
  allLicense: [],
  permissions: [],
};

export const LicenseSlice = createSlice({
  name: "License",
  initialState,
  reducers: {},

  extraReducers: {
    [licenseListThunk.pending]: (state) => {
      state.licenseLoading = Constants.PENDING;
    },
    [licenseListThunk.fulfilled]: (state, { payload }) => {
      state.licenseLoading = Constants.FULFILLED;
      state.allLicense = payload.data;
    },
    [licenseListThunk.rejected]: (state) => {
      state.licenseLoading = Constants.REJECTED;
    },
    [accountLicenseThunk.pending]: (state) => {
      state.ownerLicenseLoading = Constants.PENDING;
    },
    [accountLicenseThunk.fulfilled]: (state, { payload }) => {
      state.ownerLicenseLoading = Constants.FULFILLED;
      state.permissions = payload.data;
    },
    [accountLicenseThunk.rejected]: (state) => {
      state.ownerLicenseLoading = Constants.REJECTED;
    },
    [resetStateThunk.fulfilled]: (state) => {
      state.licenseLoading = Constants.IDLE;
      state.ownerLicenseLoading = Constants.IDLE;
      state.allLicense = [];
      state.permissions = [];
    },
  },
});

export default LicenseSlice.reducer;

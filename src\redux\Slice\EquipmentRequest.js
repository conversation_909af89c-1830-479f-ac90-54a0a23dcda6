import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import projectOrderRequestListing, {
  getPMOrders,
  getPMReturnOrders,
  getPMOrderDetails,
  getProjectOrderHistory,
  getProjectOrderHistoryById,
  getProjectReturnOrderById,
  projectOrderShoppingListing,
  getShoppingDetailsById,
} from "redux/Thunks/EquipmentRequest";
import Constants, { Common } from "utils/Constants";

const initialState = {
  loading: Constants.IDLE,
  requestDetailsLoading: Constants.IDLE,
  list: [],
  shoppingLoading: Constants.IDLE,
  shoppingList: [],
  requestDetails: {},
  shoppingDetailsByIdLoading: Constants.IDLE,
  shoppingDetailsByIdList: {},
  approverOrderLoading: Constants.IDLE,
  approverOrderList: [],
  approverReturnOrderLoading: Constants.IDLE,
  approverReturnOrderList: [],
  currentProjectLayerId: "",
  currentEquipmentTypeLayerId: "",
  approverOrderProjectData: "",
  approverOrderEquipmentTypeData: "",
  approverOrderEquipmentData: "",
  approverReturnProjectData: "",
  approverReturnOrderData: "",

  projectOrderHistoryLoading: Constants.IDLE,
  projectOrderHistoryList: [],
  projectOrderHistoryByIdLoading: Constants.IDLE,
  projectOrderHistoryByIdList: [],

  projectReturnOrderByIdLoading: Constants.IDLE,
  projectReturnOrderByIdList: [],

  currentStatus: Constants.STATUS_PENDING,
};

export const equipmentOrderSlice = createSlice({
  name: "equipmentOrderSlice",
  initialState,
  reducers: {
    reloadData(state) {
      state.loading = Constants.PENDING;
    },

    reloadShoppingData(state) {
      state.shoppingLoading = Constants.PENDING;
    },

    reloadRequestDetails(state) {
      state.requestDetailsLoading = Constants.PENDING;
    },
    OrderReloadData(state) {
      state.projectOrderHistoryLoading = Constants.PENDING;
    },

    ReturnOrderReloadData(state) {
      state.approverReturnOrderLoading = Constants.PENDING;
    },

    // Store Nested table required data
    loadProjectLayerData(state, action) {
      state.currentProjectLayerId = action.payload;
    },

    loadEquipmentTypeLayerData(state, action) {
      state.currentEquipmentTypeLayerId = action.payload;
    },
    loadApproverProjectLayerData(state, action) {
      state.approverOrderProjectData = action.payload;
    },
    loadApproverEquipmentTypeLayerData(state, action) {
      state.approverOrderEquipmentTypeData = action.payload;
    },
    loadApproverEquipmentLayerData(state, action) {
      state.approverOrderEquipmentData = action.payload;
    },
    loadReturnProjectLayerData(state, action) {
      state.approverReturnProjectData = action.payload;
    },

    loadReturnOrderLayerData(state, action) {
      state.approverReturnOrderData = action.payload;
    },

    // Update the requested quantity for Pending & Queue tab on user layer table
    updateEquipmentOrderQuantity: (state, action) => {
      const { userId, equipId, type, newValue, currentStatus } = action.payload;
      const data =
        currentStatus === Constants.QUEUE ? state.shoppingDetailsByIdList : state.requestDetails;
      if (!data || !data.equipmentTypes) return;

      data.equipmentTypes = data.equipmentTypes.map((equipment) => {
        if (equipment[Constants.MONGOOSE_ID] === equipId) {
          const updatedUsers = equipment.users.map((user) => {
            if (user.equipmentRequestId === userId) {
              let updatedApprovedQuantity =
                currentStatus === Constants.STATUS_PENDING
                  ? user.approvedQuantity || 0
                  : user.pmApprovedQuantity || 0;

              if (type === "increment") {
                updatedApprovedQuantity += 1;
              } else if (type === "decrement" && updatedApprovedQuantity > 0) {
                updatedApprovedQuantity -= 1;
              } else if (type === "input") {
                updatedApprovedQuantity = parseInt(newValue, 10) || 0;
              }

              return {
                ...user,
                [currentStatus === Constants.STATUS_PENDING
                  ? "approvedQuantity"
                  : "pmApprovedQuantity"]: updatedApprovedQuantity,
              };
            }
            return user;
          });

          const totalApprovedQuantity = updatedUsers.reduce(
            (sum, u) => sum + (u.approvedQuantity || 0),
            0
          );

          return {
            ...equipment,
            users: updatedUsers,
            totalApprovedQuantity,
            pmOrderManageDetails: {
              ...equipment.pmOrderManageDetails,
              pmRequestedQuantity: updatedUsers.reduce(
                (sum, u) => sum + (u.pmApprovedQuantity || 0),
                0
              ),
            },
            totalAmount: (equipment?.price ?? 0) * totalApprovedQuantity,
          };
        }
        return equipment;
      });

      // Calculate total approved quantity at the project level
      data.totalApprovedQuantity = data.equipmentTypes.reduce(
        (sum, equipment) => sum + (equipment.pmOrderManageDetails.pmRequestedQuantity || 0),
        0
      );
      data.totalAmount = data.equipmentTypes.reduce((sum, equipment) => {
        const price = equipment?.price ?? 0;
        const pmRequestedQuantity = equipment?.pmOrderManageDetails?.pmRequestedQuantity ?? 0;
        return (
          sum +
          price *
            pmRequestedQuantity *
            (equipment?.quantityType?.priceType === Common.RENTAL ? data?.totalDays ?? 0 : 1)
        );
      }, 0);
    },

    // Reject Order
    rejectOrder: (state, action) => {
      const { type, dataId } = action.payload;

      if (!state.requestDetails || !state.requestDetails.equipmentTypes) return;
      if (type === Common.EQUIPMENT_TEXT) {
        state.requestDetails.equipmentTypes = state.requestDetails.equipmentTypes.filter(
          (equipment) => equipment[Constants.MONGOOSE_ID] !== dataId
        );
      } else {
        state.requestDetails.equipmentTypes = state.requestDetails.equipmentTypes.map(
          (equipment) => ({
            ...equipment,
            users: equipment.users.filter((user) => user.equipmentRequestId !== dataId),
          })
        );
      }
    },

    // Add Approvers comments for pending tab
    getApproverComments(state, action) {
      const temp = state.requestDetails.equipmentTypes.map((equip) =>
        equip[Constants.MONGOOSE_ID] === action.payload?.equipmentId
          ? { ...equip, pmComments: action.payload?.comments }
          : equip
      );
      state.requestDetails.equipmentTypes = temp;
    },

    getCartPMComments(state, action) {
      const temp = state.shoppingDetailsByIdList.equipmentTypes.map((equip) =>
        equip[Constants.MONGOOSE_ID] === action.payload?.equipmentId
          ? {
              ...equip,
              pmOrderManageDetails: {
                ...equip.pmOrderManageDetails,
                comments: {
                  ...equip.pmOrderManageDetails?.comments,
                  pmComments: action.payload?.comments,
                },
              },
            }
          : equip
      );
      state.shoppingDetailsByIdList.equipmentTypes = temp;
    },
    // To store current status for approver screen
    storeCurrentStatus(state, action) {
      state.currentStatus = action.payload;
    },
  },

  extraReducers: {
    // Project Order Listing
    [projectOrderRequestListing.pending]: (state) => {
      if (state.list.length === 0) state.loading = Constants.PENDING;
    },
    [projectOrderRequestListing.fulfilled]: (state, action) => {
      state.loading = Constants.FULFILLED;
      if (state.currentStatus !== action.payload.currentStatus) return;
      state.list = action.payload.data.data;
    },
    [projectOrderRequestListing.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },

    // Project Shopping Listing
    [projectOrderShoppingListing.pending]: (state) => {
      if (state.shoppingList?.length === 0) state.shoppingLoading = Constants.PENDING;
    },
    [projectOrderShoppingListing.fulfilled]: (state, action) => {
      state.shoppingLoading = Constants.FULFILLED;
      // if (state.currentStatus !== action.payload.currentStatus) return;
      state.shoppingList = action.payload.data.data;
    },
    [projectOrderShoppingListing.rejected]: (state) => {
      state.shoppingLoading = Constants.REJECTED;
    },

    // Project Shopping Details ById
    [getShoppingDetailsById.pending]: (state) => {
      state.shoppingDetailsByIdLoading = Constants.PENDING;
    },
    [getShoppingDetailsById.fulfilled]: (state, action) => {
      state.shoppingDetailsByIdLoading = Constants.FULFILLED;

      const { data = {} } = action.payload.data;
      const { equipmentTypes = [], ...rest } = data;

      // Process each equipment type
      const processedEquipmentTypes = equipmentTypes.map((equip) => {
        const totalApprovedQuantity = equip?.users?.reduce((acc, user) => {
          const quantityToAdd = user?.approvedQuantity
            ? user?.approvedQuantity
            : user?.engineerRequestedQuantity || 0;
          return acc + parseInt(quantityToAdd, 10);
        }, 0);

        return {
          ...equip,
          totalApprovedQuantity: totalApprovedQuantity.toString(), // Ensure it's a string
          pmComments: equip?.pmComments ? equip?.pmComments : [],
          totalAmount:
            (equip?.price ?? 0) * (equip?.pmOrderManageDetails?.pmRequestedQuantity ?? 0),

          users: equip?.users?.map((user) => ({
            ...user,
            approvedQuantity: user?.approvedQuantity
              ? user?.approvedQuantity
              : user?.engineerRequestedQuantity,
          })),
        };
      });

      // Calculate totalApprovedQuantity across all equipment types
      const totalApprovedQuantityOverall = processedEquipmentTypes.reduce(
        (acc, equip) => acc + parseInt(equip?.pmOrderManageDetails?.pmRequestedQuantity || 0, 10),
        0
      );
      const totalCartAmount = processedEquipmentTypes.reduce(
        (acc, equip) =>
          acc +
          (equip?.price ?? 0) *
            (equip?.pmOrderManageDetails?.pmRequestedQuantity ?? 0) *
            (equip?.quantityType?.priceType === Common.RENTAL ? rest?.totalDays ?? 1 : 1),
        0
      );
      // Assign everything to the state
      state.shoppingDetailsByIdList = {
        ...rest,
        equipmentTypes: processedEquipmentTypes,
        totalApprovedQuantity: totalApprovedQuantityOverall, // Add key outside the array
        totalAmount: totalCartAmount,
      };
    },

    [getShoppingDetailsById.rejected]: (state) => {
      state.shoppingDetailsByIdLoading = Constants.REJECTED;
    },

    // PM Order Details
    [getPMOrderDetails.pending]: (state) => {
      state.requestDetailsLoading = Constants.PENDING;
    },
    [getPMOrderDetails.fulfilled]: (state, action) => {
      state.requestDetailsLoading = Constants.FULFILLED;

      const { data } = action.payload.data;

      const { equipmentTypes = [], ...rest } = data;

      const processedEquipmentTypes = equipmentTypes.map((equip) => {
        const totalApprovedQuantity = equip?.users?.reduce((acc, user) => {
          const quantityToAdd = user?.approvedQuantity
            ? user?.approvedQuantity
            : user?.engineerRequestedQuantity || 0;
          return acc + parseInt(quantityToAdd, 10);
        }, 0);

        return {
          ...equip,
          totalApprovedQuantity: totalApprovedQuantity.toString(),
          pmComments: equip?.pmComments ? equip?.pmComments : [],
          users: equip?.users?.map((user) => ({
            ...user,
            approvedQuantity: user?.approvedQuantity
              ? user?.approvedQuantity
              : user?.engineerRequestedQuantity,
          })),
        };
      });

      state.requestDetails = {
        ...rest,
        equipmentTypes: processedEquipmentTypes,
      };
    },

    [getPMOrderDetails.rejected]: (state) => {
      state.requestDetailsLoading = Constants.REJECTED;
      state.requestDetails = {};
    },

    // PM Order Listing
    [getPMOrders.pending]: (state) => {
      if (state.approverOrderList.length === 0) state.approverOrderLoading = Constants.PENDING;
    },
    [getPMOrders.fulfilled]: (state, { payload }) => {
      state.approverOrderLoading = Constants.FULFILLED;
      state.approverOrderList = payload.data.data;
    },
    [getPMOrders.rejected]: (state) => {
      state.approverOrderLoading = Constants.REJECTED;
    },

    // PM Return Order Listing
    [getPMReturnOrders.pending]: (state) => {
      if (state.approverReturnOrderList.length === 0)
        state.approverReturnOrderLoading = Constants.PENDING;
    },
    [getPMReturnOrders.fulfilled]: (state, { payload }) => {
      state.approverReturnOrderLoading = Constants.FULFILLED;
      state.approverReturnOrderList = payload.data.data;
    },
    [getPMReturnOrders.rejected]: (state) => {
      state.approverReturnOrderLoading = Constants.REJECTED;
    },

    // Project Return Order By Id
    [getProjectReturnOrderById.pending]: (state) => {
      state.projectReturnOrderByIdLoading = Constants.PENDING;
    },
    [getProjectReturnOrderById.fulfilled]: (state, { payload }) => {
      state.projectReturnOrderByIdLoading = Constants.FULFILLED;
      state.projectReturnOrderByIdList = payload?.data?.data || [];
    },
    [getProjectReturnOrderById.rejected]: (state) => {
      state.projectReturnOrderByIdLoading = Constants.REJECTED;
    },

    // Project Order History
    [getProjectOrderHistory.pending]: (state) => {
      state.projectOrderHistoryLoading = Constants.PENDING;
    },
    [getProjectOrderHistory.fulfilled]: (state, { payload }) => {
      state.projectOrderHistoryLoading = Constants.FULFILLED;
      state.projectOrderHistoryList = payload?.data?.data || [];
    },
    [getProjectOrderHistory.rejected]: (state) => {
      state.projectOrderHistoryLoading = Constants.REJECTED;
    },

    // Project Order History By Id
    [getProjectOrderHistoryById.pending]: (state) => {
      state.projectOrderHistoryByIdLoading = Constants.PENDING;
    },
    [getProjectOrderHistoryById.fulfilled]: (state, { payload }) => {
      state.projectOrderHistoryByIdLoading = Constants.FULFILLED;
      state.projectOrderHistoryByIdList = payload?.data?.data || [];
    },
    [getProjectOrderHistoryById.rejected]: (state) => {
      state.projectOrderHistoryByIdLoading = Constants.REJECTED;
    },

    [resetStateThunk.fulfilled]: (state) => {
      state.loading = Constants.IDLE;
      state.list = [];
    },
  },
});

export const {
  reloadData,
  OrderReloadData,
  ReturnOrderReloadData,
  loadProjectLayerData,
  loadEquipmentTypeLayerData,
  updateEquipmentOrderQuantity,
  loadApproverProjectLayerData,
  loadApproverEquipmentTypeLayerData,
  loadApproverEquipmentLayerData,
  loadReturnProjectLayerData,
  loadReturnOrderLayerData,
  getApproverComments,
  storeCurrentStatus,
  reloadRequestDetails,
  rejectOrder,
  reloadShoppingData,
  getCartPMComments,
} = equipmentOrderSlice.actions;
export default equipmentOrderSlice.reducer;

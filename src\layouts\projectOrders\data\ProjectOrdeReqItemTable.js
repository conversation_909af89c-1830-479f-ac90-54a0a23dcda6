import React, { useMemo, useState } from "react";
import PropTypes from "prop-types";

// MUI
import { IconButton, Popover } from "@mui/material";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";

// Utils
import { Icons } from "utils/Constants";

// Comment Popover for long comments
function CommentPopover({ comment = [] }) {
  const newComment = comment?.[0]?.comment || "";

  const [anchorEl, setAnchorEl] = useState(null);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handlePopoverOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <MDBox display="flex" alignItems="center" maxWidth="180px" justifyContent="space-between">
        {/* Truncate text dynamically */}
        <MDTypography
          sx={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            maxWidth: "150px",
            display: "inline-block",
          }}
        >
          {newComment}
        </MDTypography>

        {/* Show info icon only if comment is long */}
        {(comment.length > 1 || newComment.length > 10) && (
          <IconButton size="small" onClick={handlePopoverOpen}>
            {Icons.INFO}
          </IconButton>
        )}
      </MDBox>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        PaperProps={{
          sx: {
            mt: 3,
            backgroundColor: "#fff !important", // Ensure visibility
            boxShadow: 3, // Elevation
            maxWidth: "300px",
            borderRadius: "8px", // Smooth corners
            border: "1px solid #ddd", // Subtle border
          },
        }}
      >
        {comment.map((item, index) => (
          <MDBox key={item.time}>
            <MDTypography>{`${index + 1}. ${item.comment}`}</MDTypography>
          </MDBox>
        ))}
      </Popover>
    </>
  );
}

CommentPopover.propTypes = {
  comment: PropTypes.string.isRequired,
};

// Project Order Request Item Table
export default function ProjectOrdeReqItemTable({
  dataList = [],
  deleteReqItemFunc = () => {},
  updateStateAtAddItem,
}) {
  const rows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((elem, index) => ({
      srNo: <Author name={index + 1} />,
      productCategory: <Author name={elem?.productCategory} />,
      productType: <Author name={elem?.productType} />,
      quantityType: <Author name={elem?.quantityType} />,
      quantity: <Author name={elem?.engineerRequestedQuantity} />,
      comment: <CommentPopover comment={elem?.engineerComment || []} />,
      action: (
        <MDBox>
          <IconButton
            aria-label="delete"
            color="error"
            onClick={() => deleteReqItemFunc(elem?.equipmentType)}
          >
            {Icons.CROSS6}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList, updateStateAtAddItem]);

  const columns = [
    { Header: "No.", accessor: "srNo", width: "2%" },
    { Header: "Product Category", accessor: "productCategory", align: "left", width: "25%" },
    { Header: "Product Type", accessor: "productType", align: "left" },
    { Header: "Quantity Type", accessor: "quantityType", align: "left" },
    { Header: "Requested Qty", accessor: "quantity", align: "left" },
    { Header: "Comment", accessor: "comment", align: "left" },
    { Header: "Action", accessor: "action", align: "center" },
  ];

  return {
    requestedItemsColumns: columns,
    requestedItemsRows: rows,
  };
}

// Importing Dependencies
import { createSlice } from "@reduxjs/toolkit";

// Importing Thunks
import getCertificateTypeOptions, { UserOptionsListThunk } from "redux/Thunks/DropdownOptions";

// Constants from UtilsgetCertificateTypeOptions
import Constants from "utils/Constants/";

const getUserNameLabelName = (callingName, firstName, lastName) => {
  switch (true) {
    case !!callingName && !!firstName:
      return `${callingName} ${lastName || ""}`.trim();

    case !callingName && !!firstName:
      return `${firstName} ${lastName || ""}`.trim();

    case !!callingName && !firstName:
      return `${callingName}  ${lastName || ""}`.trim();

    default:
      return "";
  }
};

const initialState = {
  userCertificateTypeList: [],
  usersDropdownList: [], // All users at once
};

const dropdownOptionsSlice = createSlice({
  name: "dropdownOptionsSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Dropdown Options List for user Certifificate Type
      .addCase(getCertificateTypeOptions.pending, (state) => {
        state.userCertificateTypeList = [];
      })
      .addCase(getCertificateTypeOptions.fulfilled, (state, action) => {
        const { payload } = action;
        const userCertificateRes = payload?.data?.data || [];
        const certificateOptionsArr = userCertificateRes.map((item) => ({
          title: item?.name,
          [Constants.MONGOOSE_ID]: item?.[Constants.MONGOOSE_ID],
          validityDate: item?.validityDate,
          disabled: false,
          hide: false,
        }));

        state.userCertificateTypeList = certificateOptionsArr;
      })
      .addCase(getCertificateTypeOptions.rejected, (state) => {
        state.userCertificateTypeList = [];
      })

      // Dropdown Options List for All Users
      .addCase(UserOptionsListThunk.pending, (state) => {
        state.usersDropdownList = [];
      })
      .addCase(UserOptionsListThunk.fulfilled, (state, action) => {
        const { payload } = action;
        const userOptionsRes = payload?.data?.data?.usersData || [];
        const userOptionsArr = userOptionsRes.map((item) => ({
          title: getUserNameLabelName(item?.callingName, item?.firstName, item?.lastName),
          [Constants.MONGOOSE_ID]: item?.[Constants.MONGOOSE_ID],
          disabled: false,
          hide: false,
        }));

        state.usersDropdownList = userOptionsArr;
      })
      .addCase(UserOptionsListThunk.rejected, (state) => {
        state.usersDropdownList = [];
      });
  },
});

// Export the reducer
export default dropdownOptionsSlice.reducer;

import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import { filterProjectStatusFunc, normalizeParamsAndAddValues } from "utils/methods/methods";
import ApiService from "../ApiService/ApiService";

const createReportType = createAsyncThunk("create-report-type", async (body) => {
  const res = await ApiService.post(
    `reports`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getAllReportTypes = createAsyncThunk("report-types/list", async (arv) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(arv?.param, projectStatusObj);

  const res = await ApiService.get(`reports?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  const params = new URLSearchParams(updatedParams);
  const page = params.get("page");

  return page === "0"
    ? { data: res.data, type: "add", status: res.status, byPassSlice: arv.byPassSlice }
    : { data: res.data, type: "append", status: res.status, byPassSlice: arv.byPassSlice };
});

export const getReportTypeById = createAsyncThunk("report-types/get", async (id) => {
  const res = await ApiService.get(`reports/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateReportType = createAsyncThunk("reports/update", async (body) => {
  const res = await ApiService.patch(
    `reports/${body.reportTypeId}`,
    {
      ...body.body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateIsPrintable = createAsyncThunk("reports/update-isPrintable", async (body) => {
  const res = await ApiService.post(
    `report/user-answers/update-is-printable`,
    {
      ...body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createQuestions = createAsyncThunk("report-types/create-parameter", async (body) => {
  const res = await ApiService.post(
    `report/questions`,
    {
      ...body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateQuestions = createAsyncThunk("report-types/update-questions", async (body) => {
  const res = await ApiService.put(
    `report/questions/${body?.questionId}`,
    {
      ...body.body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteReportQuestions = createAsyncThunk(
  "setup-report/delete-questions",
  async (questionId) => {
    const res = await ApiService.delete(`report/questions/${questionId}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const deleteReportType = createAsyncThunk("report-types/delete", async (id) => {
  const res = await ApiService.delete(`reports/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createReport = createAsyncThunk("report/create", async (body) => {
  const res = await ApiService.post(
    `reports`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getAllReports = createAsyncThunk("report/list", async (param) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(param, projectStatusObj);

  const res = await ApiService.get(`v2/report/users/final-reports?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getReportById = createAsyncThunk("report/get", async (id) => {
  const res = await ApiService.get(`report/users/report-details/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateReportAnswer = createAsyncThunk("report/update", async (body) => {
  const res = await ApiService.patch(`report/user-answers/${body.answerId}`, body.data, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateReportStatus = createAsyncThunk("report/update-status", async (body) => {
  const res = await ApiService.patch(
    `report/users/update-status`,
    {
      ...body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )

    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const unAssignAnswerThunk = createAsyncThunk("report/unassign", async (body) => {
  const res = await ApiService.patch(
    `report/user-answers/${body.answerId}/unassign/${body.id}`,
    { answerTitleId: body.answerTitleId },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const assignAnswerThunk = createAsyncThunk("report/assign", async (body) => {
  const res = await ApiService.patch(
    `report/user-answers/assign-title`,
    {
      ...body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const exportReportThunk = createAsyncThunk("export-report/api", async (reportId) => {
  const res = await fetch(`${process.env.REACT_APP_BASE_URL}/reports/${reportId}/export`, {
    method: "GET",
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  }).then((response) => response.blob());
  return res;
});

export const getAllQuestionTypes = createAsyncThunk("report-questions-types/list", async () => {
  const res = await ApiService.get(`report/questions/parameter-types`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteUserReport = createAsyncThunk("user-report-types/delete", async (id) => {
  const res = await ApiService.delete(`report/users/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createReportDocument = createAsyncThunk("create-report-document", async (body) => {
  const res = await ApiService.post(
    "report-document",
    {
      ...body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteReportDocument = createAsyncThunk("delete-report-document", async (id) => {
  const res = await ApiService.delete(`report-document/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getReportDocument = createAsyncThunk("get-report-document", async (id) => {
  const res = await ApiService.get(`report-document/user-project-report/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateReportDocument = createAsyncThunk("update-report-document", async (body) => {
  const res = await ApiService.put(
    `report-document/${body.userProjectReportId}`,
    {
      ...body.body,
    },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createWebReport = createAsyncThunk("create-web-report", async (body) => {
  const res = await ApiService.post(
    "/report/users/create-web-report",
    { ...body },
    { headers: { Authorization: `Bearer ${Sessions.userToken}` } }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const exportReportPdf = createAsyncThunk("export-report/api", async (body) => {
  const res = await ApiService.post(`/report/users/export-report-pdf`, body, {
    headers: {
      Authorization: `Bearer ${Sessions.userToken}`,
    },
    responseType: "blob",
  });

  return res.data;
});

export default createReportType;

import Sessions from "utils/Sessions";
import { createAsyncThunk } from "@reduxjs/toolkit";
import ApiService from "redux/ApiService/ApiService";

const getAllCertificates = createAsyncThunk("/get-certificates", async (param) => {
  const res = await ApiService.get(`/upload-certificate/certificate-list?${param}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  const params = new URLSearchParams(param);
  const page = params.get("page");
  return page === "0"
    ? { data: res.data, type: "add", status: res.status }
    : { data: res.data, type: "append", status: res.status };
});

export const approveCertificate = createAsyncThunk("/approve-certificate", async (payload) => {
  const res = await ApiService.patch(
    `/upload-certificate/${payload.id}`,
    { ...payload.body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const createUserCertificate = createAsyncThunk("/create-user-certificate", async (body) => {
  const res = await ApiService.post(
    `/upload-certificate`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

// To Upload multiple Certificate at once for multiple users, from User Dashboard.
export const uploadMultipleUsersCertificates = createAsyncThunk(
  "/create-multiple-users-certificate",
  async (body) => {
    const res = await ApiService.post("/upload-certificate/upload-multiple-certificates", body, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const deleteCertificate = createAsyncThunk(
  "delete-certificate/api",
  async (certificateId) => {
    const res = await ApiService.delete(`/upload-certificate/hard/delete/${certificateId}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export default getAllCertificates;

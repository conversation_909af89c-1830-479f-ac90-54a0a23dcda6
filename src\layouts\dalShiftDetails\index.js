import React, { useEffect, useState } from "react";

// Material component
import { Button, Card, Divider, Grid, FormControl } from "@mui/material";
import MDBox from "components/MDBox";

// Custom Component
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import NewShift from "examples/modal/shift";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { projectListThunk, dalShitFiltersThunk, exportAllShiftPdf } from "redux/Thunks/Filter";
import { deleteShiftThunk } from "redux/Thunks/DalShift";
import { openSnackbar } from "redux/Slice/Notification";
import configThunk from "redux/Thunks/Config";

// Data
import DailyShiftData from "layouts/dalShiftDetails/data/dailyShiftData";
import DataTable from "examples/Tables/DataTable";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";

// Feature Flag
import { Feature } from "flagged";

// Constant
import Constants, {
  Icons,
  ModalContent,
  PageTitles,
  ButtonTitles,
  Colors,
  FeatureTags,
  defaultData,
  FiltersModuleName,
  Common,
} from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";
import UserListThunk from "redux/Thunks/UserManagement";
import { reloadData, updateCompleted, removeShift } from "redux/Slice/DalShift";
import { setStoreFilters, resetFilters } from "redux/Slice/Filter";
import FontComponent from "components/Responsive/fonts";
import { findPageNumberAfterRecordDelete, paramCreater } from "utils/methods/methods";

const shiftsFilterArr = [
  {
    inputLabel: FiltersModuleName.PROJECT,
    list: [FiltersModuleName.SHIFT_DETAILS_FILTERS_TITLE_OBJ],
    selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
  },
  {
    inputLabel: FiltersModuleName.CREATED,
    list: FiltersModuleName.SHIFT_DETAILS_CREATED_FILTER_OPTIONS,
    selectedValue: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
  },
  {
    inputLabel: FiltersModuleName.STATUS,
    list: FiltersModuleName.SHIFT_DETAILS_STATUS_FILTER_OPTIONS,
    selectedValue: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
  },
];

function DalShiftDetails() {
  const mongooseId = "_id";
  const dispatch = useDispatch();

  const shiftData = useSelector((state) => state?.dalShift);
  const shiftStoredFilters = useSelector((state) => state.filtersSlice.shift);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[5]?.screensInfo?.agreement;

  const initialFilters = shiftStoredFilters?.length > 0 ? shiftStoredFilters : shiftsFilterArr;

  const [shouldUpdateState, setShouldUpdateState] = useState(false);
  const [openDeleteModal, setDeleteModal] = useState(false);
  const [openNewShift, setOpenNewShift] = useState(false);
  const [filters, setFilters] = useState(initialFilters);
  const [selectedId, setSelectedId] = useState("");
  const [activeUsers, setActiveUsers] = useState([]);

  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.PER_PAGE_5,
  });
  const [exportLoadder, setExportLoadder] = useState(false);

  const fontSize = FontComponent({
    sizes: {},
  });

  const handleCloseDeleteModal = () => setDeleteModal(false);
  const handleOpenDeleteModal = (id) => {
    setSelectedId(id);
    setDeleteModal(true);
  };

  const { columns, rows } = DailyShiftData(handleOpenDeleteModal, shiftData?.shiftList);

  const handleErrorFunc = () => {
    dispatch(
      openSnackbar({
        message: Constants.SOMETHING_WENT_WRONG,
        notificationType: Constants.NOTIFICATION_ERROR,
      })
    );
  };

  // Func to get Shift Details list
  const getShiftDetailsDataFunc = async (temp = filters, currentPage = 0) => {
    setTablePagination({ ...tablePagination, page: currentPage });
    const paramData = {
      page: currentPage,
      perPage: tablePagination.perPage,
      project: temp[0].selectedValue,
      date: temp[1].selectedValue.replace(/\s+/g, "_").toLowerCase(),
      status: temp[2].selectedValue.replace(/\s+/g, "_").toLowerCase(),
    };

    const params = paramCreater(paramData);
    await dispatch(dalShitFiltersThunk(params));
  };

  const handleTablePagination = async (value) => {
    // check alrady fetched data
    if (shiftData.shiftListingPagiantion.completed.includes(value)) return;
    const data = new URLSearchParams({
      page: value,
      perPage: tablePagination.perPage,
      project: filters[0].selectedValue,
      date: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      status: filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    });
    await dispatch(dalShitFiltersThunk(data));
  };

  const handleDeleteShift = async () => {
    const res = await dispatch(deleteShiftThunk(selectedId));

    if (res.payload.status === 200) {
      await dispatch(removeShift(selectedId));
      const { page, perPage } = tablePagination;
      const { totalRecords } = shiftData.shiftListingPagiantion;

      const data = new URLSearchParams({
        page: findPageNumberAfterRecordDelete(page, perPage, totalRecords),
        perPage: tablePagination.perPage,
        project: filters[0].selectedValue,
        date: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
        status: filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
      });
      const shiftRes = await dispatch(dalShitFiltersThunk(data));
      if (shiftRes.payload.status === Common.API_STATUS_200) {
        const completedPages = [...shiftData.shiftListingPagiantion.completed];
        for (let i = completedPages.length - 1; i >= 0; i -= 1) {
          if (completedPages[i] > tablePagination.page) {
            completedPages.splice(i, 1);
          }
        }
        await dispatch(updateCompleted(completedPages));
        dispatch(
          openSnackbar({
            message: Constants.SHIFT_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      }
    } else if (res.payload.status === Common.API_STATUS_400) {
      dispatch(
        openSnackbar({
          message: Constants.INVALID_SHIFT,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    } else if (res.payload.status === Common.API_STATUS_500) {
      handleErrorFunc();
    }
    setDeleteModal(false);
  };

  // Get Project List Func for dropdown.
  const getProjectListFunc = async () => {
    try {
      const projectList = await dispatch(projectListThunk());
      if (projectList?.payload?.status) {
        setFilters((prev) => {
          const updatedFilters = prev.map((filter) => {
            if (filter.inputLabel === FiltersModuleName.PROJECT) {
              return {
                ...filter,
                selectedValue: projectList?.payload?.data?.some(
                  (project) => project[mongooseId] === filters[0]?.selectedValue
                )
                  ? filters[0]?.selectedValue
                  : FiltersModuleName.ALL_IN_SMALL_CASE,

                list: [
                  FiltersModuleName.SHIFT_DETAILS_FILTERS_TITLE_OBJ,
                  ...projectList.payload.data,
                ],
              };
            }
            return filter;
          });

          return updatedFilters;
        });
        setShouldUpdateState((prev) => !prev);
      }
    } catch (error) {
      handleErrorFunc();
    }
  };

  // get User List Func
  const getUserListFunc = async () => {
    const params = paramCreater({ isActive: true });
    try {
      const res = await dispatch(UserListThunk(params));
      if (res?.payload?.status) {
        const userList = res.payload.data.data.usersData || [];
        const filteredUserList = userList.filter(
          (user) =>
            user.role?.title.toLowerCase() !== defaultData.ADMIN_ROLE &&
            user.role?.title.toLowerCase() !== defaultData.SUPER_ADMIN_ROLE
        );

        setActiveUsers(filteredUserList);
      }
    } catch (error) {
      handleErrorFunc();
    }
  };

  useEffect(() => {
    getProjectListFunc();
    getUserListFunc();
  }, []);

  useEffect(() => {
    getShiftDetailsDataFunc();
  }, [shouldUpdateState]);

  const handleFilterType = async (e) => {
    const { name, value } = e.target;

    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === name) {
          return {
            ...filter,
            selectedValue: value,
          };
        }
        return filter;
      });

      // Updating the Store also with the latest value.
      dispatch(setStoreFilters({ module: "shift", filters: updatedFilters }));
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  const handleReset = async () => {
    setFilters((prevFilters) => {
      const updatedFilters = prevFilters.map((filter) => ({
        ...filter,
        selectedValue: filter.list[0][mongooseId] || filter.list[0],
      }));
      return updatedFilters;
    });

    dispatch(resetFilters({ module: "shift" }));
    setShouldUpdateState((prev) => !prev);
  };

  const handleReload = async () => {
    await dispatch(reloadData());
    await dispatch(configThunk());
    await getProjectListFunc();

    setShouldUpdateState((prev) => !prev);
  };

  const handleExport = async (format) => {
    setExportLoadder(true);
    const currentDate = new Date();
    const filename = `Reynard_shift_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.${format}`;
    const queryParams = {
      project: filters[0].selectedValue,
      date: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      status: filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const res = await dispatch(exportAllShiftPdf(queryString));
    if (res.error) {
      handleErrorFunc();
      setExportLoadder(false);
      return;
    }
    const url = window.URL.createObjectURL(res.payload);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    setExportLoadder(false);
  };

  return (
    <DashboardLayout module={defaultData.SHIFT_SCREEN_ID}>
      <DashboardNavbar />
      <MDBox
        display="flex"
        flexDirection={{ md: "row", sm: "column" }}
        justifyContent={{ md: "space-between" }}
        alignItems={{ lg: "space-between", sm: "center" }}
      >
        <PageTitle title={PageTitles.DAL_SHIFT} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          {permission?.create && (
            <CustomButton
              title={ButtonTitles.NEW_SHIFT}
              icon={Icons.NEW}
              background={Colors.PRIMARY}
              color={Colors.WHITE}
              openModal={setOpenNewShift}
            />
          )}
          <Divider
            orientation="vertical"
            sx={{
              backgroundColor: "var(--gray-300, #D0D5DD)",
              height: "auto",
              marginLeft: pxToRem(16),
              marginRight: 0,
            }}
          />
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleReload}
          />
        </MDBox>
      </MDBox>
      <Divider sx={{ marginTop: pxToRem(22) }} />
      <Feature name={FeatureTags.SHIFT_DETAILS}>
        <MDBox display="flex" justifyContent="space-between" mx={0}>
          <MDBox
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "start",
              alignItems: "flex-end",
              mx: 0,
              rowGap: pxToRem(16),
            }}
          >
            {filters?.map((val) => {
              const isProject = val?.inputLabel === FiltersModuleName.PROJECT;
              return isProject ? (
                <FormControl
                  key={val?.inputLabel}
                  variant="standard"
                  size="medium"
                  style={{ marginTop: "26px", width: 200, marginRight: "15px" }}
                >
                  <CustomAutoComplete
                    label={val?.inputLabel}
                    name={val?.inputLabel}
                    id={val?.inputLabel}
                    getOptionLabel={(option) => option.title || ""}
                    menu={val?.list}
                    value={{
                      title:
                        val?.list.find((item) => item[Constants.MONGOOSE_ID] === val?.selectedValue)
                          ?.title || "",
                    }}
                    handleChange={handleFilterType}
                    valueStyle={{
                      backgroundColor: Colors.WHITE,
                      height: pxToRem(40),
                      verticalMarginTop: pxToRem(4),
                      menuWidth: 400,
                      inputWidth: 250,
                      padding: pxToRem(1),
                    }}
                    labelStyle={{
                      fontSize: pxToRem(14),
                      fontWeight: 600,
                      color: Colors.BLACK,
                    }}
                  />
                </FormControl>
              ) : (
                <FilterDropdown
                  key={val.inputLabel}
                  label={val.inputLabel}
                  name={val.inputLabel}
                  defaultValue={val?.selectedValue}
                  value={val?.selectedValue}
                  handleChange={handleFilterType}
                  menu={val.list}
                  maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
                />
              );
            })}
            <MDBox sx={{ marginRight: pxToRem(8) }}>
              <Button
                sx={{
                  mr: 1,
                  backgroundColor: "#fff",
                  "&:hover": {
                    backgroundColor: "#fff",
                  },
                  fontSize,
                  height: pxToRem(40),
                  textTransform: "capitalize",
                }}
                variant="outlined"
                color="info"
                onClick={handleReset}
                startIcon={Icons.RESET_FILTER}
              >
                {ButtonTitles.RESET_FILTER}
              </Button>
            </MDBox>
            <BasicButton
              title={exportLoadder ? ButtonTitles.EXPORTING : ButtonTitles.EXPORT}
              icon={Icons.EXPORT}
              background={Colors.WHITE}
              color={Colors.BLACK}
              border
              action={() => handleExport("xlsx")}
              disabled={filters[0].selectedValue === "all" || exportLoadder || rows.length === 0}
              style={{ btnMarginLeft: pxToRem(0) }}
            />
          </MDBox>
        </MDBox>
        <MDBox mt={3} mb={3}>
          <Grid item xs={12}>
            <Card sx={{ boxShadow: "none" }}>
              <MDBox>
                <DataTable
                  table={{ columns, rows }}
                  isSorted={false}
                  entriesPerPage={{ defaultValue: defaultData.PER_PAGE_5 }}
                  showTotalEntries={false}
                  noEndBorder
                  loading={shiftData.loading}
                  licenseRequired
                  currentPage={tablePagination.page}
                  handleTablePagination={handleTablePagination}
                  handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
                  isGotoVisisble
                />
              </MDBox>
            </Card>
          </Grid>
        </MDBox>

        {/* New Shift */}
        {openNewShift && (
          <NewShift
            openShiftModal={openNewShift}
            setOpenShiftModal={setOpenNewShift}
            setShouldUpdateState={setShouldUpdateState}
            activeUsers={activeUsers}
          />
        )}

        {openDeleteModal && (
          <DeleteModal
            open={openDeleteModal}
            title={ModalContent.SHIFT_DELETE_TITLE}
            message={ModalContent.DELETE_SHIFT}
            handleClose={handleCloseDeleteModal}
            handleDelete={handleDeleteShift}
          />
        )}
      </Feature>
    </DashboardLayout>
  );
}

export default DalShiftDetails;

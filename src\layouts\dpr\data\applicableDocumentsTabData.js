import { useEffect, useState } from "react";
import moment from "moment";

import Author from "components/Table/Author";
import { formatKeyName } from "utils/methods/methods";
import Constants, { DropdownOptions, defaultData } from "utils/Constants";

const dropdownOptions = [...DropdownOptions.PROJECT_DOCUMENT_TYPES_AT_SETUP_PROJECT];

// Create a type-to-index map without mutating `acc`
const typeOrderMap = dropdownOptions.reduce(
  (acc, item, index) => ({ ...acc, [item[Constants.MONGOOSE_ID]]: index }),
  {}
);

function applicableDocumentsTabData(applicableDocsList = []) {
  const [applicableDocsRows, setApplicableDocsRows] = useState([]);

  const createEmptyAuthorCell = () => (
    <Author name="" style={{ fontWeight: "bold" }} cellColor="#f6f7ff" />
  );

  useEffect(() => {
    if (applicableDocsList.length > 0) {
      const rowsData = [];

      // Sort resData based on options order, unknown types go to the end
      const sortedApplicableDocsList = [...applicableDocsList]?.sort((a, b) => {
        const indexA = typeOrderMap[a.type] ?? Infinity;
        const indexB = typeOrderMap[b.type] ?? Infinity;
        return indexA - indexB;
      });

      sortedApplicableDocsList?.forEach((elem) => {
        // Push the document type row
        rowsData.push({
          docType: (
            <Author
              name={formatKeyName(elem.type)}
              style={{ fontWeight: "bold" }}
              cellColor="#f6f7ff"
            />
          ),
          docNumber: createEmptyAuthorCell(),
          version: createEmptyAuthorCell(),
          date: createEmptyAuthorCell(),
        });

        // Push each document row
        elem.documents?.forEach((doc) => {
          rowsData.push({
            docType: <Author name={doc.title || Constants.NA} cellColor="#fff" />,
            docNumber: <Author name={doc.documentNumber} cellColor="#fff" />,
            version: (
              <Author style={{ textTransform: "initial" }} name={doc.version} cellColor="#fff" />
            ),
            date: (
              <Author
                name={
                  doc?.date ? moment(doc.date).format(defaultData.WEB_DATE_FORMAT) : Constants.NA
                }
                cellColor="#fff"
              />
            ),
          });
        });
      });

      setApplicableDocsRows(rowsData);
    }
  }, [applicableDocsList]);

  const applicableDocColumns = [
    { Header: "Type", accessor: "docType", width: "20%" },
    { Header: "Number", accessor: "docNumber", align: "left", width: "20%" },
    { Header: "Version", accessor: "version", align: "left", width: "5%" },
    { Header: "Date", accessor: "date", align: "left", width: "20%" },
  ];

  return {
    applicableDocColumns,
    applicableDocsRows,
  };
}

export default applicableDocumentsTabData;

import React, { useEffect, useState } from "react";

// Common Components
import MDBox from "components/MDBox";

// Data Table
import DataTable from "examples/Tables/DataTable";
import ProjectOrderHistoryData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectOrderHistoryData";
import ProjectReturnOrderData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectReturnOderData";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { ReturnOrderReloadData } from "redux/Slice/EquipmentRequest";
import { getProjectOrderHistory, getPMReturnOrders } from "redux/Thunks/EquipmentRequest";
import { openSnackbar } from "redux/Slice/Notification";

// Utils
import Constants, { defaultData, FiltersModuleName, Common } from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

export default function projectOrderHistory({ filters, setFilters, shouldUpdateState }) {
  const dispatch = useDispatch();

  const [next, setNext] = useState(0);
  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });

  const {
    projectOrderHistoryList,
    projectOrderHistoryLoading,
    approverReturnOrderList,
    approverReturnOrderLoading,
  } = useSelector((state) => state.equipmentRequest);

  const { projectHistoryColumns, projectHistoryRows } = ProjectOrderHistoryData({
    dataList: projectOrderHistoryList,
  });

  const { projectReturnColumns, projectReturnRows } = ProjectReturnOrderData({
    dataList: approverReturnOrderList,
  });

  const createParamObj = () => {
    const paramData = {
      search: filters[0].selectedValue,
      project: filters[1].selectedValue,
    };

    Object.keys(paramData).forEach((key) => {
      if (paramData[key] === "" || paramData[key] === "all" || paramData[key] === null) {
        delete paramData[key];
      }
    });

    return paramData;
  };

  const filtersSearchLoadingFunc = () => {
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === FiltersModuleName.SEARCH) {
          return {
            ...filter,
            isLoading: false,
          };
        }
        return filter;
      });
      return updatedFilters;
    });
  };

  // Function to get listing of project orders requests
  const getProjectOrderHistoryFunc = async () => {
    const params = createParamObj();

    try {
      const res = await dispatch(getProjectOrderHistory(paramCreater(params)));
      if (res?.payload?.status === Common.API_STATUS_200) {
        filtersSearchLoadingFunc();
      }
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const getProjectReturnOrderHistory = async () => {
    setTablePagination({ ...tablePagination, page: 0 });
    setNext(0);
    const params = createParamObj();

    await dispatch(ReturnOrderReloadData());
    const res = await dispatch(getPMReturnOrders(paramCreater(params)));

    if (res?.payload?.status === Common.API_STATUS_200) {
      filtersSearchLoadingFunc();
    }
  };

  const handleTablePagination = async () => {
    const params = createParamObj();

    const res = await dispatch(getPMReturnOrders(paramCreater(params)));
    if (res.payload.status === 200) setNext(res.payload.data.length > 0 ? next + 1 : next);
  };

  useEffect(() => {
    if (filters[2].selectedValue === Constants.RETURN_ORDERS) {
      getProjectReturnOrderHistory();
    } else {
      getProjectOrderHistoryFunc();
    }
  }, [shouldUpdateState]);

  return (
    <MDBox mt={3} mb={5}>
      <DataTable
        table={{
          columns:
            filters[2].selectedValue === Constants.RETURN_ORDERS
              ? projectReturnColumns
              : projectHistoryColumns,
          rows:
            filters[2].selectedValue === Constants.RETURN_ORDERS
              ? projectReturnRows
              : projectHistoryRows,
        }}
        isSorted={false}
        entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
        showTotalEntries={false}
        pagination={{ variant: "gradient", color: "info" }}
        loading={
          filters[2].selectedValue === Constants.RETURN_ORDERS
            ? approverReturnOrderLoading
            : projectOrderHistoryLoading
        }
        licenseRequired
        currentPage={tablePagination.page}
        handleTablePagination={handleTablePagination}
        handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
      />
    </MDBox>
  );
}

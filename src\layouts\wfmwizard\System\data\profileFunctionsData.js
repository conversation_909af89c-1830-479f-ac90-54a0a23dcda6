import React, { useEffect, useState } from "react";

// Components
import Author from "components/Table/Author";

// Material Components
import { IconButton, Switch } from "@mui/material";
import MDBox from "components/MDBox";

// Utils
import Constants, { Icons, defaultData } from "utils/Constants";

export default function ProfileFunctionsData(
  profileFunctionsData,
  handleEdit,
  handleDelete,
  handleStatusChange
) {
  const [rows, setRows] = useState([]);
  useEffect(() => {
    if (profileFunctionsData) {
      const tempRows = profileFunctionsData?.map((element, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          name: <Author name={element?.name} maxContent={defaultData.MEDIUM_CONTENT_LENGTH} />,
          sortOrder: (
            <Author name={element?.sortOrder} maxContent={defaultData.MEDIUM_CONTENT_LENGTH} />
          ),
          isActive: (
            <Switch
              checked={element?.isActive}
              onChange={(e) =>
                handleStatusChange(e, {
                  body: {
                    isActive: e.target.checked,
                    id: element[Constants.MONGOOSE_ID],
                  },
                })
              }
            />
          ),
          action: (
            <MDBox>
              <IconButton
                fontSize="medium"
                sx={{ cursor: "pointer", color: "#475467" }}
                onClick={() =>
                  handleEdit({
                    body: {
                      name: element.name,
                      sortOrder: element.sortOrder,
                      id: element[Constants.MONGOOSE_ID],
                    },
                  })
                }
              >
                {Icons.EDIT2}
              </IconButton>
              <IconButton
                color="secondary"
                fontSize="medium"
                sx={{ cursor: "pointer" }}
                onClick={() => handleDelete(element[Constants.MONGOOSE_ID])}
              >
                {Icons.DELETE}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...tempRows]);
    }
  }, [profileFunctionsData]);
  return {
    columns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Profile Function Name", accessor: "name", align: "left" },
      { Header: "Sort Order", accessor: "sortOrder", align: "center", width: "15%" },
      { Header: "Is Active", accessor: "isActive", align: "center", width: "15%" },
      { Header: "Action", accessor: "action", width: "5%", align: "left" },
    ],
    rows,
  };
}

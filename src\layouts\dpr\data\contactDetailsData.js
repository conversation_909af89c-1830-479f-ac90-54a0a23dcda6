import { useEffect, useState } from "react";

// Components
import Author from "components/Table/Author";

function ContactDetailsData(contactList) {
  const [contactRows, setContactRows] = useState([]);

  useEffect(() => {
    if (contactList) {
      const list = contactList.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          function: <Author name={item.function} />,
          name: <Author name={item?.name} />,
          email: <Author name={item?.email} style={{ textTransform: "none" }} />,
          phone: <Author name={item?.phone} />,
        };
        return temp;
      });
      setContactRows([...list]);
    }
  }, [contactList]);

  return {
    contactColumns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Function", accessor: "function", width: "20%" },
      { Header: "Name", accessor: "name", align: "left", width: "30%" },
      { Header: "Email Address", accessor: "email", align: "left", width: "30%" },
      { Header: "Phone Number", accessor: "phone", align: "left", width: "30%" },
    ],

    contactRows,
  };
}

export default ContactDetailsData;

import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

import { useDispatch, useSelector } from "react-redux";
import { useParams, useLocation, useSearchParams } from "react-router-dom";

// Material UI components
import { Card, Checkbox, FormControlLabel, FormGroup } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import DetailProgressTable from "examples/Tables/DataTable/DetailTimeAnalysisTable";
import FormTextArea from "components/Form/FTextArea";
import FontComponent from "components/Responsive/fonts";
import CardBreakPoint from "components/Responsive/BreakPoints";
import ContactDetails from "layouts/dpr/data/contactDetailsData";
import ProjectTrackerTable from "examples/Tables/DataTable/projectTrackerTable";

// Constants
import Constants, { Common, defaultData, Colors } from "utils/Constants";

// Thunk
import {
  getContactDetailsDPR,
  getProjectTrackerDprData,
  getDetailsProgressData,
} from "redux/Thunks/Dpr";

import ProgressSummaryData from "layouts/dpr/data/progressSummaryData";

// Slice
import { setProgressData, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";
import { openSnackbar } from "redux/Slice/Notification";
import Author from "components/Table/Author";

const showDetailProgressColumns = [
  { Header: "Location", accessor: "rowspanlocation", isRowspan: true, width: "20%" },
  { Header: "Asset", accessor: "rowspanasset", isRowspan: true, width: "20%" },
  { Header: "Report", accessor: "rowspanreport", align: "left", width: "20%" },
  { Header: "Completed Tasks", accessor: "completedTasks", align: "left", width: "20%" },
];

function ProgressTab({ isDetailedProgressChecked, setIsDetailedProgressChecked }) {
  const dispatch = useDispatch();

  const { id } = useParams();

  const location = useLocation();
  const { projectStatus } = location.state || {};

  const [searchParams] = useSearchParams();
  const projectId = searchParams.get("projectId");

  // Add pagination state for detail progress table
  const [currentPage, setCurrentPage] = useState(0);

  const { dprData, loading, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  const totalCompletion = (
    Number(dprData?.progressData?.progressSummary?.totalCompletions) || 0
  ).toFixed(2);

  const CardTitleFontSize = FontComponent({ sizes: CardBreakPoint.baseTitleBreakPoint });

  const getContactDetails = async () => {
    const data = await dispatch(getContactDetailsDPR(id || location?.state?.dprId));
    if (data?.payload?.status !== true) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const getDetailsProgress = async () => {
    try {
      await dispatch(getDetailsProgressData(location?.state?.dprId));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const getProjectReportProgress = async () => {
    const res = await dispatch(
      getProjectTrackerDprData({
        dprId: id || location?.state?.dprId,
        projectId: location?.state?.projectId || projectId,
      })
    );
    if (!res?.payload?.status) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const fetchAllLatestData = async () => {
    try {
      await Promise.all([getContactDetails(), getProjectReportProgress(), getDetailsProgress()]);
      dispatch(updateIsLatestDataApiCompleted(true)); // Set true only if all APIs succeed
    } catch (error) {
      dispatch(updateIsLatestDataApiCompleted(false)); // In case of error, keep it false
    }
  };

  // Calling this useEffect when the project status changes to open and the dpr details api has been completed
  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getContactDetails();
      getProjectReportProgress();
      getDetailsProgress();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  // Calling this useEffect when refresh is clicked
  useEffect(() => {
    if (displayedDprTabsObj?.progressTab > 0) {
      fetchAllLatestData();
    }
  }, [displayedDprTabsObj?.progressTab]);

  const { contactColumns, contactRows } = ContactDetails(dprData?.progressData?.dprMembers);

  const { columns, rows, extraHeadersList, footerList } = ProgressSummaryData(
    dprData?.progressData?.progressSummary?.scopeData,
    dprData?.progressData?.progressSummary?.projectTrackerData,
    totalCompletion
  );

  const processApiDataWithGrouping = (data) => {
    const rowsData = [];

    data?.forEach((entry) => {
      const locationName = entry.locationTitle;

      // Push location group row
      rowsData.push({
        rowspanlocation: (
          <Author
            cellColor={Colors.TABLE_ROW_COLOR}
            isRowSpan
            style={{ fontWeight: "bold", textTransform: "capitalize" }}
            name={locationName}
          />
        ),
        rowspanasset: <Author cellColor={Colors.TABLE_ROW_COLOR} isRowSpan name="" />,
        rowspanreport: <Author cellColor={Colors.TABLE_ROW_COLOR} isRowSpan name="" />,
        completedTasks: <Author cellColor={Colors.TABLE_ROW_COLOR} name="" />,
        isGroup: true,
      });

      entry.reports?.forEach((report) => {
        const assets = report.assets || [];
        const questions = report.completedTasks || [];
        const rowCount = Math.max(assets.length, questions.length);
        if (rowCount > 0) {
          for (let i = 0; i < rowCount; i += 1) {
            const asset = assets[i];
            const question = questions[i];

            rowsData.push({
              rowspanlocation: "",
              rowspanasset:
                assets.length > 0 && asset ? (
                  <>
                    <Author
                      maxContent={defaultData.MEDIUM_CONTENT_LENGTH_2}
                      name={`${asset?.cableName || ""}`}
                      style={{
                        textTransform: "capitalize",
                      }}
                    />
                    <Author
                      maxContent={defaultData.MEDIUM_CONTENT_LENGTH_2}
                      name={`( ${asset.fromLocation?.title || ""} → ${
                        asset.toLocation?.title || ""
                      } )`}
                      style={{
                        textTransform: "capitalize",
                        color: Colors.LIGHT_GRAY1,
                      }}
                    />
                  </>
                ) : (
                  ""
                ),
              rowspanreport: i === 0 ? <Author name={report.title} /> : "",
              completedTasks: question ? <Author name={question.title} /> : "",
            });
          }
        } else {
          // No assets or questions – show report with "No questions available"
          rowsData.push({
            rowspanlocation: "",
            rowspanasset: "",
            rowspanreport: <Author name={report.title} />,
            completedTasks: <Author name="No questions available" />,
          });
        }
      });
    });

    return rowsData;
  };

  const showDetailProgressRows = processApiDataWithGrouping(dprData?.progressData?.detailProgress);
  return (
    <MDBox mt={1} width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography
            // p={pxToRem(7)}
            variant="h6"
            fontWeight="medium"
            sx={{ fontSize: CardTitleFontSize }}
          >
            Contact Details Site
          </MDTypography>
        </MDBox>
        <MDBox mb={2}>
          <Card>
            <MDBox>
              <DataTable
                table={{ columns: contactColumns, rows: contactRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={loading}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
      </Card>
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Activities of the last 24 hours
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.progressData?.last24Hours}
            name="last24hours"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setProgressData({ ...dprData?.progressData, last24Hours: e.target.value }));
            }}
          />
        </MDBox>
      </Card>
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Planned Activities next 24 hours
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.progressData?.next24Hours}
            name="next24hours"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(
                setProgressData({
                  ...dprData?.progressData,
                  next24Hours: e.target.value,
                })
              );
            }}
          />
        </MDBox>
      </Card>
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Progress Summary
          </MDTypography>
        </MDBox>
        <MDBox>
          <Card>
            <MDBox>
              <ProjectTrackerTable
                table={{ columns, rows }}
                isSorted={false}
                loading={loading}
                extraHeaders={extraHeadersList}
                footerList={footerList}
              />
            </MDBox>
          </Card>
        </MDBox>

        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  name="showOnDpr"
                  checked={isDetailedProgressChecked}
                  onChange={(e, checked) => setIsDetailedProgressChecked(checked)}
                />
              }
              label={Common.SHOW_DETAILED_PROGRESS_LABEL}
              variant="body1"
              sx={{ fontWeight: 500 }}
            />
          </FormGroup>
        </MDBox>

        {isDetailedProgressChecked && (
          <MDBox mb={2}>
            <Card>
              <MDBox>
                <DetailProgressTable
                  table={{ columns: showDetailProgressColumns, rows: showDetailProgressRows }}
                  isSorted={false}
                  entriesPerPage={{ defaultValue: 500 }}
                  showTotalEntries={false}
                  noEndBorder
                  loading={loading}
                  licenseRequired
                  currentPage={currentPage}
                  handleCurrentPage={setCurrentPage}
                  handleTablePagination={(page) => setCurrentPage(page)}
                />
              </MDBox>
            </Card>
          </MDBox>
        )}
        <MDBox mb={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.progressData?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setProgressData({ ...dprData?.progressData, remarks: e.target.value }));
            }}
          />
        </MDBox>
      </Card>
    </MDBox>
  );
}

ProgressTab.propTypes = {
  isDetailedProgressChecked: PropTypes.bool.isRequired,
  setIsDetailedProgressChecked: PropTypes.func.isRequired,
};

export default ProgressTab;

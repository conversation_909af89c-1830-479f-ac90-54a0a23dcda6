import React from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box } from "@mui/material";

// Material Dashboard React components examples
import ProfileInfoCard from "examples/Cards/InfoCards/ProfileInfoCard";

// Constantd from Utils
import Constants from "utils/Constants";

function GdprTab({ tabValue, profile = [] }) {
  const { gdpr = [] } = profile;
  return (
    <Box>
      <ProfileInfoCard
        tabChangeValue={tabValue}
        key={profile?.[Constants.MONGOOSE_ID]}
        title="GDPR"
        info={{
          "Grant permission to use information for business purposes": gdpr[0]?.answer
            ? "Yes"
            : "No",
          "Grant permission to use e-mail for communication": gdpr[1]?.answer ? "Yes" : "No",
        }}
        action={{ route: "", tooltip: "Edit Profile" }}
        shadow={false}
      />
    </Box>
  );
}

GdprTab.propTypes = {
  tabValue: PropTypes.number.isRequired,
  profile: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default GdprTab;

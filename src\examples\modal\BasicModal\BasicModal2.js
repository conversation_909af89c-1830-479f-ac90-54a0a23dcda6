/* eslint-disable react/function-component-definition */

// MUI components
import { Grid, Icon, Modal } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts";
import { ModalBreakPoint } from "components/Responsive/BreakPoints";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Constants
import { Icons, Colors } from "utils/Constants";

// 3rd party library
import PropTypes from "prop-types";

function BasicModal2({
  open,
  handleClose,
  title,
  children,
  actionButton,
  handleAction,
  disabled,
  btnLoading,
  btnLoadingText,
  width = "50%",
  isAdditionalBtnRequired,
  additionalBtnTitle,
  additionalBtnIcon,
  additionalBtnAction,
  additionalBtnDisabled,
}) {
  return (
    <Modal
      open={open}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <MDBox
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width,
          bgcolor: "background.paper",
          borderRadius: pxToRem(8),
          p: 0,
        }}
      >
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          borderRadius="lg"
          sx={{
            borderBottomRightRadius: 0,
            borderBottomLeftRadius: 0,
            height: pxToRem(55),
            padding: "8px 16px 8px 16px",
            // marginBottom: "8px",
          }}
        >
          <MDTypography
            sx={{
              fontSize: FontComponent({ sizes: ModalBreakPoint.baseTitleBreakPoint }),
              color: "#191D31",
              fontWeight: "700",
            }}
          >
            {title}
          </MDTypography>
          <Icon sx={{ cursor: "pointer" }} fontSize="medium" onClick={handleClose}>
            {Icons.CLOSE}
          </Icon>
        </MDBox>
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          px={3}
          sx={{
            maxHeight: 500,
            overflowY: "scroll",
            "::-webkit-scrollbar": { display: "none" },
            scrollbarWidth: "none",
          }}
        >
          {children}
        </MDBox>

        <MDBox px={2} mb={2} mr={1} mt={2}>
          <Grid container direction="row" justifyContent="flex-end" alignItems="center" gap={2}>
            {isAdditionalBtnRequired && (
              <Grid item xs={0}>
                <BasicButton
                  title={additionalBtnTitle}
                  icon={additionalBtnIcon}
                  background={Colors.WHITE}
                  color={Colors.PRIMARY}
                  action={additionalBtnAction}
                  disabled={additionalBtnDisabled}
                  border
                  borderColor={Colors.PRIMARY}
                  modalFontSize
                  style={{ textTransform: "none", boxShadow: "none" }}
                />
              </Grid>
            )}
            <Grid item xs={0}>
              <MDButton
                variant="contained"
                disabled={disabled}
                color="info"
                onClick={handleAction}
                style={{ textTransform: "none", boxShadow: "none" }}
              >
                <span
                  style={{
                    fontSize: FontComponent({ sizes: ModalBreakPoint.extraSmallTitleBreakPoint }),
                  }}
                >
                  {btnLoading ? btnLoadingText : actionButton}
                </span>
              </MDButton>
            </Grid>
          </Grid>
        </MDBox>
      </MDBox>
    </Modal>
  );
}

BasicModal2.propTypes = {
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  actionButton: PropTypes.string.isRequired,
  handleAction: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  width: PropTypes.string,
  isAdditionalBtnRequired: PropTypes.bool,
  additionalBtnTitle: PropTypes.string,
  additionalBtnIcon: PropTypes.string,
  additionalBtnAction: PropTypes.func,
  additionalBtnDisabled: PropTypes.bool,
  btnLoading: PropTypes.bool,
  btnLoadingText: PropTypes.string,
};

BasicModal2.defaultProps = {
  children: null,
  disabled: false,
  width: "50%",
  isAdditionalBtnRequired: false,
  additionalBtnTitle: "",
  additionalBtnIcon: "",
  additionalBtnAction: () => {},
  additionalBtnDisabled: false,
  btnLoadingText: "",
  btnLoading: false,
};
export default BasicModal2;

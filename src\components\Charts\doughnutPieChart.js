import React, { useState } from "react";
import PropTypes from "prop-types";

// MUI Components
import { Box, Tooltip } from "@mui/material";
import pxToRem from "assets/theme/functions/pxToRem";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";

// Custom Components
import FDropdown from "components/Dropdown/FDropdown";

// Constant
import Constants, { Colors } from "utils/Constants";

const getTextLabel = (label, value, total) => {
  if (label === "percentage") {
    if (value !== 0) {
      return `${Math.floor((value / total) * 100)}%`;
    }
    return "0%";
  }
  return value;
};

function DoughnutPieChart({
  data,
  title,
  totalCount,
  isapplyFilter,
  filterData,
  handleFilterChange,
  label,
  isRiskChart = false,
}) {
  const [hoveredSlice, setHoveredSlice] = useState(null);
  let startAngle = -90;
  const total = data.reduce((acc, slice) => acc + slice.value, 0);

  const singleSliceData = data.find((slice) => slice.value === totalCount);
  let isSingleSlice = false;
  if (singleSliceData && totalCount !== 0) {
    isSingleSlice = true;
  }

  const getTooltipContent = (slice) => {
    if (isRiskChart) {
      const percentage =
        slice.value !== 0 ? `${((slice.value / totalCount) * 100).toFixed(2)}%` : "0.00%";
      if (slice.name === Constants.RISK_LOW)
        return `${Constants.RISK_LOW}: ${slice.value} (${percentage})`;
      if (slice.name === Constants.RISK_MEDIUM)
        return `${Constants.RISK_MEDIUM}: ${slice.value} (${percentage})`;
      if (slice.name === Constants.RISK_HIGH)
        return `${Constants.RISK_HIGH}: ${slice.value} (${percentage})`;
      return slice.value;
    }
    return `${slice.name}: ${slice.value} (${((slice.value / totalCount) * 100).toFixed(2)}%)`;
  };

  const getChartInfoValue = (item, totalCountValue) => {
    const percentage =
      item.value !== 0 ? `${((item.value / totalCountValue) * 100).toFixed(2)}%` : "0.00%";
    return percentage;
  };

  const getRiskValue = (riskName) => {
    switch (riskName) {
      case Constants.RISK_LOW:
        return `${Constants.RISK_LOW} ${Constants.RISK_LOW_VALUE}`;
      case Constants.RISK_MEDIUM:
        return `${Constants.RISK_MEDIUM} ${Constants.RISK_MEDIUM_VALUE}`;
      case Constants.RISK_HIGH:
        return `${Constants.RISK_HIGH} ${Constants.RISK_HIGH_VALUE}`;
      default:
        return "";
    }
  };

  return (
    <MDBox
      sx={{
        display: "flex",
        gap: { lg: pxToRem(50), md: pxToRem(30), sm: pxToRem(20), xs: pxToRem(15) },
        flexWrap: "wrap",
        justifyContent: { xs: "center", lg: "flex-start", md: "flex-start", sm: "center" },
        alignItems: "flex-start",
        minHeight: pxToRem(350),
      }}
    >
      <MDBox
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minWidth: pxToRem(300),
          minHeight: pxToRem(300),
        }}
      >
        <svg width="300" height="300" style={{ marginTop: pxToRem(35) }}>
          {totalCount !== 0 && total === totalCount && !isSingleSlice ? (
            data.map((slice) => {
              const percentage = (slice.value / totalCount) * 100;
              const endAngle = startAngle + (percentage * 360) / 100;
              const largeArcFlag = percentage > 50 ? 1 : 0;
              // Calculate outer slice coordinates
              const outerStartX = Math.cos((startAngle * Math.PI) / 180) * 100 + 150;
              const outerStartY = Math.sin((startAngle * Math.PI) / 180) * 100 + 150;
              const outerEndX = Math.cos((endAngle * Math.PI) / 180) * 100 + 150;
              const outerEndY = Math.sin((endAngle * Math.PI) / 180) * 100 + 150;

              // Calculate inner slice coordinates
              const innerStartX = Math.cos((startAngle * Math.PI) / 180) * 80 + 150;
              const innerStartY = Math.sin((startAngle * Math.PI) / 180) * 80 + 150;
              const innerEndX = Math.cos((endAngle * Math.PI) / 180) * 80 + 150;
              const innerEndY = Math.sin((endAngle * Math.PI) / 180) * 80 + 150;

              // Calculate text position
              const textAngle = (startAngle + endAngle) / 2;
              const textRadius = 90;
              const textX = Math.cos((textAngle * Math.PI) / 180) * textRadius + 150;
              const textY = Math.sin((textAngle * Math.PI) / 180) * textRadius + 155;

              startAngle = endAngle;

              return (
                <Tooltip
                  key={slice.name}
                  title={getTooltipContent(slice)}
                  arrow
                  componentsProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: Colors.PRIMARY,
                        fontSize: pxToRem(18),
                        padding: pxToRem(10),
                        "& .MuiTooltip-arrow": {
                          color: "#000",
                        },
                      },
                    },
                  }}
                >
                  <g
                    onMouseEnter={() => setHoveredSlice(slice.name)}
                    onMouseLeave={() => setHoveredSlice(null)}
                    style={{
                      cursor: "pointer",
                      opacity: hoveredSlice === null || hoveredSlice === slice.name ? 1 : 0.7,
                      transition: "opacity 0.3s ease",
                    }}
                  >
                    {/* Outer border */}
                    <path
                      d={`M ${outerStartX} ${outerStartY} A 100 100 0 ${largeArcFlag} 1 ${outerEndX} ${outerEndY}`}
                      fill="none"
                      stroke={slice.color}
                      strokeWidth="40"
                    />
                    {/* Inner border */}
                    <path
                      d={`M ${innerEndX} ${innerEndY} A 80 80 0 ${largeArcFlag} 0 ${innerStartX} ${innerStartY}`}
                      fill="none"
                      stroke={slice.color}
                      strokeWidth="40"
                    />
                    {slice.value > 0 && (
                      <text
                        x={textX}
                        y={textY}
                        textAnchor="middle"
                        fill={slice.textColor}
                        style={{ fontWeight: "600" }}
                      >
                        {getTextLabel(label, slice.value, totalCount)}
                      </text>
                    )}
                    <text
                      x="150"
                      y="165"
                      textAnchor="middle"
                      fill="#000"
                      style={{ fontWeight: "800", fontSize: "42px", pointerEvents: "none" }}
                    >
                      {label === "percentage" ? null : totalCount}
                    </text>
                  </g>
                </Tooltip>
              );
            })
          ) : (
            <Tooltip
              title={isSingleSlice ? getTooltipContent(singleSliceData) : "No data"}
              arrow
              componentsProps={{
                tooltip: {
                  sx: {
                    backgroundColor: "#000",
                    fontSize: pxToRem(16),
                    padding: pxToRem(10),
                    "& .MuiTooltip-arrow": {
                      color: "#000",
                    },
                  },
                },
              }}
            >
              <g>
                <circle
                  cx="150"
                  cy="150"
                  r="125"
                  fill={isSingleSlice ? singleSliceData.color : Colors.LIGHT_GRAY}
                  style={{ cursor: isSingleSlice ? "pointer" : "default" }}
                />
                <circle cx="150" cy="150" r="65" fill="#FFFFFF" />
                <text
                  x="150"
                  y="165"
                  textAnchor="middle"
                  fill="#000"
                  style={{ fontWeight: "800", fontSize: "42px", pointerEvents: "none" }}
                >
                  {label === "percentage" ? null : totalCount}
                </text>
                {isSingleSlice && (
                  <text
                    x="150"
                    y="250"
                    textAnchor="middle"
                    fill={singleSliceData.textColor}
                    style={{ fontWeight: "600" }}
                  >
                    {label === "percentage" ? "100%" : singleSliceData.value}
                  </text>
                )}
              </g>
            </Tooltip>
          )}
        </svg>
      </MDBox>
      <MDBox
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start",
          minWidth: { xs: "100%", sm: "200px", md: "250px", lg: "280px" },
          maxWidth: pxToRem(350),
        }}
      >
        <MDTypography
          sx={{
            fontWeight: "700",
            fontSize: pxToRem(24),
            lineHeight: pxToRem(38),
            color: "#000000",
            marginBottom: pxToRem(15),
          }}
        >
          {title}
        </MDTypography>
        {isapplyFilter && (
          <MDBox sx={{ marginBottom: pxToRem(15) }}>
            <FDropdown
              id={filterData.name}
              name={filterData.name}
              menu={filterData.menu}
              value={filterData.selectedValue}
              handleChange={(name, value, id) =>
                handleFilterChange({ target: { name, value, id } })
              }
            />
          </MDBox>
        )}
        <MDBox sx={{ display: "flex", flexDirection: "column", gap: pxToRem(8) }}>
          {data.map((item) => (
            <MDBox
              key={item.name}
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                gap: pxToRem(15),
                padding: pxToRem(5),
              }}
            >
              <Box
                component="span"
                backgroundColor={item.color}
                sx={{
                  borderRadius: pxToRem(5),
                  width: pxToRem(20),
                  height: pxToRem(20),
                  display: "inline-block",
                  flexShrink: 0,
                }}
              />
              <MDTypography
                sx={{
                  fontSize: pxToRem(16),
                  fontWeight: "600",
                  color: "#000000",
                  minWidth: pxToRem(80),
                  textAlign: "right",
                  flexShrink: 0,
                }}
              >
                {getChartInfoValue(item, totalCount)}
              </MDTypography>
              <MDTypography
                sx={{
                  fontSize: pxToRem(16),
                  fontWeight: "600",
                  color: "#000000",
                  whiteSpace: "nowrap",
                  flex: 1,
                  textAlign: "left",
                }}
              >
                {isRiskChart ? getRiskValue(item.name) : item.name}
              </MDTypography>
            </MDBox>
          ))}
        </MDBox>
      </MDBox>
    </MDBox>
  );
}

DoughnutPieChart.defaultProps = {
  isapplyFilter: false,
  filterData: {
    selectedValue: "",
    menu: [],
    name: "select",
  },
  handleFilterChange: () => {},
  label: "text",
  isRiskChart: false,
};

DoughnutPieChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  title: PropTypes.string.isRequired,
  totalCount: PropTypes.number.isRequired,
  isapplyFilter: PropTypes.bool,
  filterData: PropTypes.shape({
    selectedValue: PropTypes.string,
    name: PropTypes.string,
    menu: PropTypes.arrayOf(PropTypes.object),
  }),
  handleFilterChange: PropTypes.func,
  label: PropTypes.string,
  isRiskChart: PropTypes.bool,
};

export default DoughnutPieChart;

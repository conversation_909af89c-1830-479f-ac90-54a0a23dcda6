{"name": "material-dashboard-2-react", "version": "2.1.0", "private": true, "author": "Creative Tim", "license": "See license in https://www.creative-tim.com/license", "description": "React version of Material Dashboard 2 by <PERSON> Tim", "bugs": {"url": "https://github.com/creativetimofficial/material-dashboard-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/material-dashboard-react.git"}, "engines": {"node": "14 || 15 || 16", "npm": ">=6"}, "dependencies": {"@emotion/cache": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "5.4.1", "@mui/material": "5.4.1", "@mui/styled-engine": "5.4.1", "@reduxjs/toolkit": "^1.9.1", "@testing-library/react": "12.1.2", "@testing-library/user-event": "13.5.0", "axios": "^1.7.8", "chart.js": "3.4.1", "chroma-js": "2.4.2", "countries-list": "^2.6.1", "flagged": "^2.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "moment-timezone": "^0.5.46", "pdf-lib": "^1.17.1", "prop-types": "15.8.1", "qrcode.react": "^3.1.0", "react": "17.0.2", "react-chartjs-2": "3.0.4", "react-datepicker": "^4.25.0", "react-dom": "17.0.2", "react-dropzone-uploader": "^2.11.0", "react-github-btn": "1.2.1", "react-phone-input-2": "^2.15.1", "react-redux": "^8.0.5", "react-router-dom": "^6.28.0", "react-scripts": "5.0.0", "react-signature-canvas": "^1.0.6", "react-table": "7.7.0", "redux-persist": "^6.0.0", "stylis": "4.0.13", "stylis-plugin-rtl": "2.1.1", "uuid": "^9.0.0", "web-vitals": "2.1.4", "yup": "0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "format": "prettier --write \"src/**/*.{js,css}\"", "prepare": "husky install", "pre-commit": "lint-staged"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "eslint": "8.8.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.3.0", "husky": "^8.0.3", "lint-staged": "^13.2.1", "prettier": "2.5.1"}}
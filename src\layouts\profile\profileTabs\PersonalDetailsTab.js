import React, { useState } from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box } from "@mui/material";

// Material Dashboard React example components
import ProfileInfoCard from "examples/Cards/InfoCards/ProfileInfoCard";

// Custom components
import PersonalDetailsEditDrawer from "examples/Drawers/profile/PersonalDetailsDrawer";

// Constants imported from Utils
import Constants, { defaultData, Common } from "utils/Constants";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import { useSelector } from "react-redux";

// Define profile prop types
const profileShape = {
  [Constants.mongooseID]: PropTypes.string,
  callingName: PropTypes.string,
  firstName: PropTypes.string,
  lastName: PropTypes.string,
  resourceNumber: PropTypes.string,
  contactNumber: PropTypes.shape({
    in: PropTypes.string,
    number: PropTypes.string,
  }),
  country: PropTypes.string,
  nationality: PropTypes.string,
  address: PropTypes.string,
  motherLanguage: PropTypes.string,
  role: PropTypes.shape({
    accessType: PropTypes.string,
  }),
  prefAirportDeprt: PropTypes.string,
  travelTimeToAirport: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  secondaryPrefAirportDeprt: PropTypes.string,
  travelTimeToSecondAirport: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  clothesSize: PropTypes.string,
  shoeSize: PropTypes.string,
  windaId: PropTypes.string,
  curriculumVitae: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      url: PropTypes.string,
    })
  ),
  nextOfKin: PropTypes.arrayOf(
    PropTypes.shape({
      [Constants.mongooseID]: PropTypes.string,
      kinName: PropTypes.string,
      relationship: PropTypes.string,
      kinStreet: PropTypes.string,
      kinArea: PropTypes.string,
      kinCity: PropTypes.string,
      kinState: PropTypes.string,
      kinCountry: PropTypes.string,
      kinZip: PropTypes.string,
      kinContactNumber: PropTypes.shape({
        in: PropTypes.string,
        number: PropTypes.string,
      }),
    })
  ),
};

function PersonalDetailsTab({ tabValue, profile = {}, onUpdate }) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens[11]?.screensInfo?.agreement;
  const handleDrawerOpen = () => {
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
    if (onUpdate) {
      onUpdate();
    }
  };

  const formatPhone = (contactNumber) =>
    contactNumber?.number ? `${contactNumber?.in} ${contactNumber?.number}` : Constants.NA;

  const formatAddress = () => {
    if (profile?.role?.accessType === defaultData.WEB_ACCESSTYPE) {
      return profile?.address || Constants.NA;
    }
    const addressParts = [
      profile?.street,
      profile?.area,
      profile?.zipCode,
      profile?.city,
      profile?.state,
    ];

    const address = addressParts.filter((part) => part).join(",");

    return address || Constants.NA;
  };

  const infoData = {
    [Common.USUAL_FIRST_NAME_LABEL]: profile?.callingName || Constants.NA,
    [`${Common.FIRST_NAME_LABEL}s ${Common.AS_PER_PASSPORT_LABEL}`]:
      profile?.firstName || Constants.NA,
    [`${Common.LAST_NAME_LABEL}s ${Common.AS_PER_PASSPORT_LABEL}`]:
      profile?.lastName || Constants.NA,
    [Common.RESOURCE_NUMBER_LABEL]: profile?.resourceNumber || Constants.NA,
    [Common.CONTACT_NUMBER_LABEL]: formatPhone(profile?.contactNumber),
    Country: profile?.country || Constants.NA,
    Nationality: profile?.nationality || Constants.NA,
    [Common.ADDRESS_LABEL]: formatAddress(),
    [Common.MAIN_LANGUAGE_LABEL]: profile?.motherLanguage || Constants.NA,
    ...(profile?.role?.accessType !== defaultData.WEB_ACCESSTYPE && {
      [Common.PREF_AIRPORT_LABEL]: profile?.prefAirportDeprt || Constants.NA,
      [Common.TRAVEL_TIME_TO_AIRPORT_LABEL]: profile?.travelTimeToAirport ?? Constants.NA,
      [Common.SECOND_PREF_AIRPORT_LABEL]: profile?.secondaryPrefAirportDeprt || Constants.NA,
      [Common.TRAVEL_TIME_TO_SECOND_AIRPORT_LABEL]:
        profile?.travelTimeToSecondAirport ?? Constants.NA,
      [Common.CLOTHES_SIZE_LABEL]: profile?.clothesSize || Constants.NA,
      [Common.SHOE_SIZE_LABEL]: profile?.shoeSize || Constants.NA,
      [Common.WINDA_ID_LABEL]: profile?.windaId || Constants.NA,
      [Common.CV_LABEL]: profile?.curriculumVitae?.[0] || Constants.NA,
    }),
  };

  return (
    <Box>
      <ProfileInfoCard
        tabChangeValue={tabValue}
        key={profile?.[Constants.mongooseID]}
        title="Personal Details"
        info={infoData}
        action={{ route: "", tooltip: "Edit Profile" }}
        shadow={false}
        showEditIcon={!!permission?.update}
        onEditClick={handleDrawerOpen}
      />

      {profile?.role?.accessType === defaultData.WEB_ACCESSTYPE
        ? null
        : profile?.nextOfKin?.length > 0 && (
            <MDBox>
              <MDTypography
                variant="h5"
                fontWeight="medium"
                color="text"
                textTransform="capitalize"
                mt={2}
                ml={2}
              >
                Next of Kin
              </MDTypography>

              {profile?.nextOfKin.map((kin, index) => (
                <ProfileInfoCard
                  tabChangeValue={tabValue}
                  key={kin?.[Constants.mongooseID]}
                  title={`Kin ${index + 1}`}
                  info={{
                    Name: kin?.kinName || Constants.NA,
                    Relationship: kin?.relationship || Constants.NA,
                    Address:
                      [
                        kin?.kinStreet,
                        kin?.kinArea,
                        kin?.kinCity,
                        kin?.kinState,
                        kin?.kinCountry,
                        kin?.kinZip,
                      ]
                        .filter(Boolean) // removes falsy values like undefined, null, ""
                        .join(",") // joins only existing values with comma
                        .trim() || Constants.NA,
                    "Contact Number": kin?.kinContactNumber
                      ? `${kin?.kinContactNumber?.in || ""} ${
                          kin?.kinContactNumber?.number || ""
                        }`.trim()
                      : Constants.NA,
                  }}
                  action={{ route: "", tooltip: "Edit Profile" }}
                  shadow={false}
                />
              ))}
            </MDBox>
          )}

      <PersonalDetailsEditDrawer open={isDrawerOpen} onClose={handleDrawerClose} data={profile} />
    </Box>
  );
}

PersonalDetailsTab.propTypes = {
  tabValue: PropTypes.number.isRequired,
  profile: PropTypes.shape(profileShape).isRequired,
  onUpdate: PropTypes.func,
};

PersonalDetailsTab.defaultProps = {
  onUpdate: null,
};

export default PersonalDetailsTab;

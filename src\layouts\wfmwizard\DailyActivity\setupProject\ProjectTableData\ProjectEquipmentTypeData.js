import MDTypography from "components/MDTypography";
import MDBox from "components/MDBox";
import { useEffect, useState } from "react";
import Constants, { Icons } from "utils/Constants";
import { IconButton } from "@mui/material";
import Author from "components/Table/Author";

export default function ProjectEquipmentTypeData(
  typeList,
  handleOpenNewModal,
  setModalType,
  editLists,
  setEditLists,
  handleDelete,
  permission
) {
  const [rows, setRows] = useState([]);

  const handleEdit = (item) => {
    setModalType("Update");
    setEditLists({ ...editLists, equipmentType: item });
    handleOpenNewModal("DPR Equipment");
  };

  useEffect(() => {
    if (typeList) {
      const list = typeList.map((item) => {
        const temp = {
          type: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              <Author name={item?.equipmentType?.type} />
            </MDTypography>
          ),
          sortOrder: <Author name={item?.sortOrder} />,
          action: (
            <MDBox>
              {permission?.update && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleEdit(item)}
                  sx={{ cursor: "pointer" }}
                >
                  {Icons.EDIT}
                </IconButton>
              )}{" "}
              &nbsp;
              {permission?.delete && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleDelete("Equipment Type", item[Constants.MONGOOSE_ID])}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [typeList]);

  return {
    projectEquipmentTypeColumns: [
      { Header: "Equipment Type", accessor: "type" },
      { Header: "Sort Order", accessor: "sortOrder", width: "10%", align: "center" },
      ...(permission?.update || permission?.delete
        ? [{ Header: "Action", accessor: "action", width: "10%", align: "right" }]
        : []),
    ],
    projectEquipmentTypeRows: rows,
  };
}

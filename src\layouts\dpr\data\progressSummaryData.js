/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";

// Common Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";

// Constants
import Constants, { Colors, defaultData, PrecisionUtils } from "utils/Constants";

// 3rd party libraries
import moment from "moment";

export default function ProgressSummaryData(
  data,
  reportData,
  totalCompletionPercentage,
  handleLocationClick = () => {}
) {
  // Initial State
  const [rows, setRows] = useState([]);
  const [scopeHeader, setScopeHeader] = useState([]);
  const [reportTitleHeader, setReportTitleHeader] = useState([]);
  const [reportValueHeader, setReportValueHeader] = useState([]);
  const [reportCompletionFooter, setReportCompletionFooter] = useState([]);
  const [reportTotalFooter, setReportTotalFooter] = useState([]);

  const handleCellBackground = (value) =>
    PrecisionUtils.getCompletionBackgroundColor(value, Colors);

  const completionPercentage = (isRequired, completion) =>
    PrecisionUtils.formatCompletionPercentage(isRequired, completion);

  useEffect(() => {
    if (data) {
      const reportTitleList = [];
      const reportValueList = [];
      const reportCompletionList = [];
      let totalReportColSpan = 0;

      const scopeList = data.map((scope) => {
        scope?.reports?.forEach((report) => {
          reportTitleList.push({
            Header: `${report?.title}`,
            align: "center",
            colSpan: 1,
            key: report[Constants.MONGOOSE_ID],
          });
          reportValueList.push({
            Header: `${report?.weightage?.toFixed(2)}%`,
            align: "center",
            colSpan: 1,
            type: report?.type,
            key: report[Constants.MONGOOSE_ID],
            accessor: report[Constants.MONGOOSE_ID],
          });
          reportCompletionList.push({
            Footer: `${report?.reportCompletion?.toFixed(2) || 0}%`,
            align: "center",
            colSpan: 1,
            key: report[Constants.MONGOOSE_ID],
          });
        });
        totalReportColSpan += scope?.reports?.length || 0;
        return {
          Header: scope?.name,
          align: "center",
          colSpan: scope?.reports?.length,
          key: scope[Constants.MONGOOSE_ID],
        };
      });

      // set the headers and footers
      setScopeHeader([{ Header: "", colSpan: 2, key: "scope" }, ...scopeList]);
      setReportTitleHeader([{ Header: "", colSpan: 2, key: "reportTitle" }, ...reportTitleList]);
      setReportValueHeader([
        {
          Header: "Location",
          accessor: "location",
          align: "left",
          width: "20%",
          type: "location",
          key: "location",
        },
        {
          Header: "Asset",
          accessor: "asset",
          align: "left",
          width: "20%",
          type: "asset",
          key: "asset",
        },
        ...reportValueList,
      ]);
      setReportCompletionFooter([
        { Footer: "Completion", align: "center", colSpan: 2, key: "completion" },
        ...reportCompletionList,
      ]);
      setReportTotalFooter([
        { Footer: "", align: "center", colSpan: totalReportColSpan, key: "completion" },
        { Footer: "Total Completion", align: "center", colSpan: 1, key: "totalCompletion" },
        {
          Footer: `${PrecisionUtils.getCompletionDisplayValue(totalCompletionPercentage)}%`,
          align: "center",
          colSpan: 1,
          key: "totalPercentage",
        },
      ]);
    }
    if (reportData) {
      const dataList = reportData?.map((report) => ({
        key: report[Constants.MONGOOSE_ID],
        rowSpan: report?.assets?.length || 1,
        colSpan: 1,
        align: "left",
        location: (
          <MDBox
            sx={{
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              width: "max-content",
              maxWidth: "250px",
            }}
            onClick={() => {
              handleLocationClick(report[Constants.MONGOOSE_ID], report?.locations);
            }}
          >
            <Author name={report?.locations || ""} style={{ textTransform: "none" }} />
          </MDBox>
        ),
        assets: report?.assets?.map((asset) => ({
          title: (
            <Author
              maxContent={defaultData.MEDIUM_CONTENT_LENGTH_2}
              name={`${asset?.title || ""}`}
              style={{ width: "max-content", maxWidth: "300px", textTransform: "none" }}
            />
          ),
          key: asset[Constants.MONGOOSE_ID],
        })),
        reports: report?.reportList?.map((item) => ({
          key: item[Constants.MONGOOSE_ID],
          type: item?.type,
          rowSpan: item?.type === "location" ? report?.assets?.length || 1 : 1,
          colSpan: 1,
          align: "center",
          reportList: item?.reportList?.map((ele) => ({
            completion: (
              <MDBox sx={{ width: "100%", display: "flex", justifyContent: "center" }}>
                <MDBox sx={{ width: "max-content" }}>
                  {!PrecisionUtils.isCompleted(ele?.completion) ? (
                    <Author
                      style={{ textTransform: "none" }}
                      name={completionPercentage(ele?.isRequired, ele?.completion)}
                    />
                  ) : (
                    <Author
                      name={moment(ele?.completedDate).format(
                        ele.status.some((status) =>
                          [Constants.CHECKED, Constants.CLOSED].includes(status)
                        )
                          ? defaultData.WEB_DATE_FORMAT
                          : completionPercentage(ele?.isRequired, ele?.completion)
                      )}
                      style={{ textTransform: "none" }}
                    />
                  )}
                </MDBox>
              </MDBox>
            ),
            isRequired: ele?.isRequired,
            bgColor: ele?.isRequired ? handleCellBackground(ele?.completion) : Colors.GREY1,
          })),
        })),
      }));

      setRows(dataList);
    }
  }, [data, reportData, totalCompletionPercentage]);

  return {
    columns: reportValueHeader,
    extraHeadersList: [scopeHeader, reportTitleHeader],
    footerList: [reportCompletionFooter, reportTotalFooter],
    rows,
  };
}

import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import AdminListThunk from "redux/Thunks/SuperAdmin";
import Constants from "utils/Constants";

const initialState = {
  loading: Constants.IDLE,
  lists: [],
};

export const superAdminSlice = createSlice({
  name: "superadmin",
  initialState,
  reducers: {},

  extraReducers: {
    [AdminListThunk.pending]: (state) => {
      if (state.lists.length === 0) state.loading = Constants.PENDING;
    },
    [AdminListThunk.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      if (payload?.type === "add") {
        state.lists = payload.data;
      } else {
        state.lists.push(...payload.data);
      }
    },
    [AdminListThunk.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    [resetStateThunk.fulfilled]: (state) => {
      state.loading = Constants.IDLE;
      state.lists = [];
    },
  },
});

export default superAdminSlice.reducer;

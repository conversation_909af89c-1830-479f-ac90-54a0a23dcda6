import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams, useSearchParams } from "react-router-dom";
import "react-datepicker/dist/react-datepicker.css";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";

// Material Design
import { styled, Divider, FormControl, Select, MenuItem } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

// Functions
import { Feature } from "flagged";

// Utils
import Constants, {
  PageTitles,
  FeatureTags,
  DropdownOptions,
  Colors,
  Common,
  Icons,
  defaultData,
} from "utils/Constants";
import { getVersion } from "utils/methods/methods";

// Themes
import pxToRem from "assets/theme/functions/pxToRem";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import ExportHOC from "examples/HigherOrderComponents/ExportHOC";
import FTextField from "components/Form/FTextField";
import MDButton from "components/MDButton";
import FontComponent from "components/Responsive/fonts";
import { ButtonBreakPoint } from "components/Responsive/BreakPoints";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import MDTypography from "components/MDTypography";
import ReloadDateTime from "components/DateTime/ReloadDateTime";

// Thunks
import { updateDprData, getDprDetails, updateWorkingStatus } from "redux/Thunks/Dpr";
import { openSnackbar } from "redux/Slice/Notification";
import {
  setEditDprStatus,
  updateDisplayedDprTabsObjAtReload,
  resetDisplayedDprTabsObj,
  resetDPR,
  updateIsLatestDataApiCompleted,
  updateSyncUpTimeForTabs,
  updateTabTextColor,
} from "redux/Slice/Dpr";

// Tabs
import ProgressTab from "./dprTabs/ProgressTab";
import QhseTab from "./dprTabs/QhseTab";
import TimeAnalysisTab from "./dprTabs/TimeAnalysisTab";
import PersonnelListTab from "./dprTabs/PersonnelListTab";
import EquipmentTab from "./dprTabs/EquipmentTab";
import CommentAndSignaturesTab from "./dprTabs/CommentAndSignaturesTab";
import ApplicableDocumentsTab from "./dprTabs/ApplicableDocumentsTab";

const TabContainer = styled("div")({
  display: "flex",
  width: "100%",
  backgroundColor: "#F6F7FF",
});

// Dpr Details Styles
const removeCursorFromInput = {
  style: { cursor: "not-allowed", caretColor: "transparent", height: "40px" },
};

const projectInitialOptions = [
  { label: "Open", value: "open" },
  { label: "Submitted", value: "submitted" },
  { label: "In Discussion", value: "in_discussion" },
  { label: "Closed", value: "closed" },
];

const tabNameByValueObj = {
  0: "progressTab",
  1: "qhseTab",
  2: "applicableDocsTab",
  3: "timeAnalysisTab",
  4: "personnelListTab",
  5: "equipmentTab",
  6: "commentAndSignaturesTab",
};

const dprKeyNamesForSyncTime = {
  0: "progressData",
  1: "qhse",
  2: "applicableDocuments",
  3: "timeAnalysis",
  4: "personnelList",
  5: "equipmentList",
  // 6: "commentAndSignaturesTab", // Not required for now
};

function DprDetails() {
  const dispatch = useDispatch();

  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { dprData, isLatestDataApiCompleted, updatedPersonnelList } = useSelector(
    (state) => state.dprs
  );

  const currProjectStatus = dprData?.status || projectStatus;

  const [dprTabValues, setDprTabValues] = useState(0);
  const [tabTextColor, setTabTextColor] = useState({});
  const [dprStatus, setDprStatus] = useState({
    currentStatus: currProjectStatus,
    options: projectInitialOptions,
  });
  const [dprTabsOptionsArr, setDprTabsOptionsArr] = useState(DropdownOptions.DPR_TABS);
  const [count, setCount] = useState(0);

  // Progress Tab Variables
  const [isDetailedProgressChecked, setIsDetailedProgressChecked] = useState(false);

  const fontSize = FontComponent({ sizes: ButtonBreakPoint.smallTitleBreakPoint });

  const dropdownIcon = () => <KeyboardArrowDownIcon fontSize="medium" sx={{ color: "#667085" }} />;

  // Get Next Version Func
  const getNextVersion = (currentVersion) => {
    const match = currentVersion.match(/v(\d+)$/);
    if (!match) return "v1"; // Default if no valid version is found

    const nextVersion = parseInt(match[1], 10) + 1;
    return `v${nextVersion}`;
  };

  // Get DPR Version Func For Updating Version for tab btns only
  const getDPRVersionFunc = () => {
    if (dprData?.status === Common.DPR_STATUS_OPEN) {
      return dprData?.prevVersion;
    }
    return getNextVersion(dprData?.prevVersion);
  };

  // Get Dpr Date Func
  const getDprFormattedDateFunc = (dprDateStr) => {
    if (dprDateStr && dprDateStr?.includes("T")) {
      return moment(dprDateStr).utc().format(defaultData.WEB_DATE_FORMAT);
    }
    return dprDateStr;
  };

  // Reload Dpr and update sync time then update dpr data
  const reloadDpr = async () => {
    const reloadDateTime = moment(new Date()).format(defaultData.WEB_24_HOURS_FORMAT);
    dispatch(
      updateSyncUpTimeForTabs({
        dprKeyName: dprKeyNamesForSyncTime[dprTabValues],
        syncTime: reloadDateTime,
      })
    );

    const tabKeyName = tabNameByValueObj?.[dprTabValues];
    dispatch(updateDisplayedDprTabsObjAtReload({ tabKeyName, value: 1 }));

    setTabTextColor((prev) => ({ ...prev, [dprTabValues]: { updateSave: true } }));
    dispatch(updateTabTextColor({ ...tabTextColor, [dprTabValues]: { updateSave: true } }));
  };

  const handleChange = async (value) => {
    const newTabTextColor = { ...tabTextColor };

    if (
      dprTabValues === 4 &&
      Array.isArray(updatedPersonnelList) &&
      updatedPersonnelList.length > 0
    ) {
      dispatch(
        updateWorkingStatus({
          body: { personnelListData: updatedPersonnelList },
        })
      );
    }

    const body = {
      version: dprData?.version,
      dprData: { ...dprData },
      status: dprData?.currentStatus?.toLowerCase() || Common.DPR_STATUS_OPEN,
      project: searchParams.get("projectId"),
    };

    // Set the color for the current tab
    if (value.toLowerCase() === Common.COMPLETE_IN_SMALL_CASE) {
      if (dprTabValues !== dprTabsOptionsArr.length - 1) {
        setDprTabValues(dprTabValues + 1);
        dispatch(resetDisplayedDprTabsObj());
      }
      newTabTextColor[dprTabValues] = { complete: true }; // Keep green when Save & Update Clicked
    } else if (value.toLowerCase() === Common.SAVE_IN_SMALL_CASE) {
      newTabTextColor[dprTabValues] = { save: true };
    }
    if (dprTabValues !== dprTabsOptionsArr.length - 1) {
      dispatch(updateTabTextColor(newTabTextColor));
    } else if (dprData?.commentsAndSignatures?.namesAndSignature?.signReynardRepresentative) {
      dispatch(updateTabTextColor(newTabTextColor));
    }
    // Goes to this condition only when in open status and on last tab
    if (
      value.toLowerCase() === Common.COMPLETE_IN_SMALL_CASE &&
      dprTabValues === dprTabsOptionsArr.length - 1
    ) {
      const allTabsComplete = dprTabsOptionsArr
        .slice(0, -1)
        .every((_, index) => tabTextColor[index]?.complete === true);

      if (!allTabsComplete) {
        const incompleteTabs = dprTabsOptionsArr
          .slice(0, -1)
          .filter((_, index) => !tabTextColor[index]?.complete);
        dispatch(
          openSnackbar({
            message: `Please complete the following tabs: ${incompleteTabs.join(", ")}`,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        return;
      }

      if (!dprData?.commentsAndSignatures?.namesAndSignature?.signReynardRepresentative) {
        dispatch(
          openSnackbar({
            message: Constants.PROVIDE_REYNARD_SIGN,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        return;
      }

      // When open we keep the version same and only change the status
      body.status = Common.DPR_STATUS_SUBMITTED;
      body.version = dprData?.version;
      body.prevVersion = dprData?.prevVersion;
      body.dprData.version = dprData?.version;
      body.dprData.tabTextColor = { ...newTabTextColor };
      await dispatch(updateDprData({ dprId: id, body }));
      navigate("/client/dpr");
      return;
    }

    if (dprData.status.toLowerCase() === Common.DPR_STATUS_REJECTED) {
      body.version = getVersion(dprData.version);
    }

    body.status = dprData?.status;
    body.version = getDPRVersionFunc();
    body.dprData.version = getDPRVersionFunc();
    body.prevVersion = dprData?.prevVersion;
    body.dprData.tabTextColor = { ...newTabTextColor };
    await dispatch(updateDprData({ dprId: id, body }));

    setCount((prevCount) => prevCount + 1);
    setTabTextColor({ ...newTabTextColor });
  };

  const handleTabChange = (tabValue) => {
    setDprTabValues(tabValue);
    dispatch(resetDisplayedDprTabsObj());
  };

  const getTabStyles = (tabIndex) => {
    const currentTab = tabTextColor[tabIndex] || {};
    const isSave = currentTab.save;
    const isSaveAndComplete = currentTab.complete;
    const isUpdateSave = currentTab.updateSave;

    if (tabIndex === dprTabValues) {
      return {
        backgroundColor: Colors.PRIMARY,
        color: "white !important",
        fontWeight: 600,
      };
    }

    if (isSaveAndComplete) {
      return {
        backgroundColor: "#DCF5E9",
        color: "#029E3B !important",
        fontWeight: 600,
      };
    }

    if (isSave) {
      return {
        backgroundColor: "#FDF1E8",
        color: "#E46A11 !important",
        fontWeight: 600,
      };
    }

    if (isUpdateSave) {
      return {
        backgroundColor: "#E8F4FD",
        color: "#1976D2 !important",
        fontWeight: 600,
      };
    }

    return {
      icon: null,
    };
  };

  const getClipPath = (isFirst, isLast) => {
    if (isFirst) return "polygon(0% 0%, 92% 0%, 100% 50%, 92% 100%, 0% 100%, 8% 50%)"; // Left flat, right pointer
    if (isLast) return "polygon(0% 0%, 92% 0%, 100% 50%, 92% 100%, 0% 100%, 8% 50%)"; // Inward cut at back ✅ FIXED
    return "polygon(0% 0%, 92% 0%, 100% 50%, 92% 100%, 0% 100%, 8% 50%)"; // Middle tabs
  };

  const TabBox = styled("div")(({ isFirst, isLast }) => ({
    flex: "1",
    minWidth: "130px",
    height: "50px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontWeight: 600,
    fontSize: "clamp(12px, 1vw, 14px)", // Dynamic font size
    padding: "0 10px",
    textAlign: "center",
    clipPath: getClipPath(isFirst, isLast), // ✅ Clean function call
    cursor: "pointer",
    transition: "background 0.3s, color 0.3s",
    backgroundColor: "white",
    "&:hover": {
      backgroundColor: Colors.PRIMARY_LIGHT,
    },
  }));

  const handleUpdate = async () => {
    const allTabsComplete = dprTabsOptionsArr
      .slice(0, -1)
      .every((_, index) => tabTextColor[index]?.complete === true);

    if (!allTabsComplete) {
      const incompleteTabs = dprTabsOptionsArr
        .slice(0, -1)
        .filter((_, index) => !tabTextColor[index]?.complete);
      dispatch(
        openSnackbar({
          message: `Please complete the following tabs: ${incompleteTabs.join(", ")}`,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }

    const clonedDprData = { ...dprData };
    clonedDprData.version = getVersion(clonedDprData.version);

    const body = {
      version: getNextVersion(dprData?.prevVersion),
      prevVersion: getNextVersion(dprData?.prevVersion),
      dprData: {
        ...clonedDprData,
        version: getNextVersion(dprData?.prevVersion),
        prevVersion: getNextVersion(dprData?.prevVersion),
      },
      status: dprData?.currentStatus?.toLowerCase() || Common.DPR_STATUS_SUBMITTED,
      project: searchParams.get("projectId"),
    };

    // When clicked on update to next version btn clicked
    const res = await dispatch(updateDprData({ dprId: id, body }));
    if (res?.payload?.status === true) {
      dispatch(
        openSnackbar({
          message: Constants.DPR_UPDATE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    }
    navigate("/client/dpr");
  };

  const handleDprStatus = async (e) => {
    // resetting the tabs values whenever the status is changed
    dispatch(resetDisplayedDprTabsObj());

    const newStatus = e.target.value;
    const data = {
      dprId: id,
      body: {
        status: newStatus,
        version: dprData.prevVersion,
        prevVersion: dprData.prevVersion,
        dprData: { ...dprData, version: dprData.prevVersion },
        project: searchParams.get("projectId"),
      },
    };

    dispatch(setEditDprStatus(newStatus));

    const res = await dispatch(updateDprData(data));

    if (res.payload.status === 200) {
      navigate(`${location.pathname}${location.search}`, {
        replace: true,
        state: { ...location.state, projectStatus: newStatus },
      });

      setCount(0);
      setDprStatus({
        ...dprStatus,
        currentStatus: newStatus,
      });

      await dispatch(
        openSnackbar({
          message: Constants.STATUS_UPDATE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    }
  };

  const updateDprDataFunc = async () => {
    const data = {
      dprId: id,
      body: {
        status: currProjectStatus,
        version: getDPRVersionFunc(),
        prevVersion: dprData.prevVersion,
        dprData: { ...dprData, version: getDPRVersionFunc() },
        project: searchParams.get("projectId"),
      },
    };
    try {
      await dispatch(updateDprData(data));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    dispatch(resetDPR());
    dispatch(getDprDetails(id));
  }, [id]);

  useEffect(() => {
    if (dprData && dprData.status) {
      if (dprData?.equipmentList?.equipmentList?.length > 0) {
        setDprTabsOptionsArr(DropdownOptions.DPR_TABS); // Ensure Equipment tab is included
      } else {
        setDprTabsOptionsArr(DropdownOptions.DPR_TABS.filter((tab) => tab !== "Equipment"));
      }
      if (dprData?.tabTextColor) {
        setTabTextColor(dprData?.tabTextColor);
      } else if (
        !Object.values(tabTextColor).some((tab) => tab.updateSave) &&
        dprData?.status?.toLowerCase() !== Common.DPR_STATUS_OPEN
      ) {
        const newTabTextColor = {};
        dprTabsOptionsArr.forEach((_, index) => {
          newTabTextColor[index] = { complete: true };
        });
        setTabTextColor(newTabTextColor);
      } else if (dprData?.status?.toLowerCase() === Common.DPR_STATUS_OPEN && count === 0) {
        const newTabTextColor = {};
        dprTabsOptionsArr.forEach((_, index) => {
          newTabTextColor[index] = { complete: false };
        });
        setTabTextColor(newTabTextColor);
      }
    }
  }, [dprData, dprStatus?.currentStatus]);

  useEffect(() => {
    if (isLatestDataApiCompleted) {
      updateDprDataFunc();
    }
    dispatch(updateIsLatestDataApiCompleted(false));
  }, [isLatestDataApiCompleted]);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Feature name={FeatureTags.DPR_DETAILS}>
        <MDBox
          display="flex"
          flexDirection={{ md: "row", sm: "column" }}
          justifyContent={{ md: "space-between" }}
          alignItems={{ lg: "space-between", sm: "center" }}
        >
          <PageTitle title={PageTitles.DPR} />

          <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
            {dprData?.[dprKeyNamesForSyncTime[dprTabValues]]?.lastSyncTime &&
              dprTabValues !== dprTabsOptionsArr.length - 1 && (
                <>
                  <ReloadDateTime
                    style={{ marginTop: "10px" }}
                    title={Constants.RELOAD_DATE_TIME}
                    label={dprData?.[dprKeyNamesForSyncTime[dprTabValues]]?.lastSyncTime}
                  />
                  <Divider
                    orientation="vertical"
                    sx={{
                      backgroundColor: "var(--gray-300, #D0D5DD)",
                      height: "auto",
                      marginLeft: pxToRem(16),
                      marginRight: 0,
                    }}
                  />
                </>
              )}

            {dprData?.status.toLowerCase() !== Common.DPR_STATUS_OPEN &&
              dprTabValues !== dprTabsOptionsArr.length - 1 && (
                <BasicButton
                  icon={Icons.RELOAD}
                  background={Colors.WHITE}
                  action={reloadDpr}
                  border
                  color={Colors.BLACK}
                  style={{ width: "10%" }}
                />
              )}
          </MDBox>
        </MDBox>
        <Divider sx={{ marginTop: 2 }} />

        <MDBox
          sx={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
            flexWrap: "wrap",
          }}
        >
          <MDBox sx={{ p: 1 }}>
            <MDTypography variant="h6" fontWeight="medium" width="60%">
              Status
            </MDTypography>
            <MDBox lineHeight={1} sx={{ backgroundColor: "white !important" }}>
              <FormControl sx={{ minWidth: 200, fontSize }}>
                <Select
                  displayEmpty
                  // disabled={!permission?.update}
                  labelId="demo-select-small"
                  id="demo-select-small"
                  name="Select Status"
                  sx={{ height: 40, textTransform: "capitalize" }}
                  value={dprStatus?.currentStatus?.replace(/ /g, "_")}
                  onChange={handleDprStatus}
                  IconComponent={dropdownIcon}
                >
                  {dprStatus.options.map((item) => (
                    <MenuItem
                      key={item}
                      value={item?.value}
                      sx={{ textTransform: "capitalize", fontSize }}
                    >
                      {item?.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </MDBox>
          </MDBox>
          <MDBox>
            <FTextField
              label="Date"
              type="text"
              readonly
              value={getDprFormattedDateFunc(dprData?.date)}
              InputProps={removeCursorFromInput}
            />
          </MDBox>
          <MDBox>
            <FTextField
              label="DPR No."
              readonly
              type="text"
              value={dprData?.dprNumber}
              InputProps={removeCursorFromInput}
            />
          </MDBox>
          <MDBox>
            <FTextField
              label="Client"
              readonly
              type="text"
              value={dprData?.client}
              InputProps={removeCursorFromInput}
            />
          </MDBox>
          <MDBox>
            <FTextField
              label="Version"
              type="text"
              readonly
              value={dprData?.prevVersion || dprData?.version}
              InputProps={removeCursorFromInput}
            />
          </MDBox>
          <MDBox item xs={2}>
            <FTextField
              label="Project"
              readonly
              type="text"
              value={dprData?.project}
              InputProps={removeCursorFromInput}
            />
          </MDBox>
        </MDBox>
        <MDBox sx={{ paddingTop: "10px" }} width="100%">
          <TabContainer>
            {dprTabsOptionsArr.map((label, index) => (
              <TabBox
                key={label}
                selected={index === dprTabValues}
                isFirst={index === 0}
                isLast={index === dprTabsOptionsArr.length - 1}
                onClick={() => handleTabChange(index)}
                sx={{ ...getTabStyles(index) }}
              >
                {label}
              </TabBox>
            ))}
          </TabContainer>
        </MDBox>
        <MDBox width="100%">
          {dprTabValues === 0 && (
            <ProgressTab
              isDetailedProgressChecked={isDetailedProgressChecked}
              setIsDetailedProgressChecked={setIsDetailedProgressChecked}
            />
          )}
          {dprTabValues === 1 && (
            <MDBox>
              <QhseTab />
            </MDBox>
          )}
          {dprTabValues === 2 && (
            <MDBox>
              <ApplicableDocumentsTab />
            </MDBox>
          )}
          {dprTabValues === 3 && (
            <MDBox>
              <TimeAnalysisTab />
            </MDBox>
          )}
          {dprTabValues === 4 && (
            <MDBox>
              <PersonnelListTab />
            </MDBox>
          )}
          {dprTabsOptionsArr?.includes("Equipment") && dprTabValues === 5 && (
            <MDBox>
              <EquipmentTab />
            </MDBox>
          )}
          {dprTabValues === dprTabsOptionsArr.length - 1 && (
            <MDBox>
              <CommentAndSignaturesTab tabTextColor={tabTextColor} />
            </MDBox>
          )}
        </MDBox>

        <MDBox
          sx={{
            marginTop: 1,
            height: "71px",
            display: "flex",
            justifyContent: "flex-end",
            width: "100%",
          }}
        >
          <MDBox sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center" }}>
            {dprTabValues !== dprTabsOptionsArr.length - 1 && (
              <MDButton
                variant="outlined"
                color="info"
                style={{ textTransform: "none", boxShadow: "none" }}
                onClick={() => handleChange(Common.SAVE_IN_SMALL_CASE)}
                sx={{ marginRight: "16px" }}
              >
                <span style={{ fontSize }}>Save</span>
              </MDButton>
            )}

            {dprTabValues !== dprTabsOptionsArr.length - 1 && (
              <MDButton
                variant="contained"
                color="info"
                style={{ textTransform: "none", boxShadow: "none" }}
                onClick={() => handleChange(Common.COMPLETE_IN_SMALL_CASE)}
              >
                <span style={{ fontSize }}>Save & Complete</span>
              </MDButton>
            )}

            {dprTabValues === dprTabsOptionsArr.length - 1 &&
              dprData?.status?.toLowerCase() === Common.DPR_STATUS_OPEN && (
                <MDButton
                  variant="contained"
                  color="info"
                  style={{ textTransform: "none", boxShadow: "none" }}
                  onClick={() => handleChange(Common.COMPLETE_IN_SMALL_CASE)}
                >
                  <span style={{ fontSize }}>Submit</span>
                </MDButton>
              )}

            {dprData?.status?.toLowerCase() !== Common.DPR_STATUS_OPEN &&
              dprTabValues === dprTabsOptionsArr.length - 1 && (
                <MDButton
                  variant="contained"
                  color="info"
                  style={{ textTransform: "none", boxShadow: "none" }}
                  sx={{ marginRight: "16px" }}
                  onClick={handleUpdate}
                >
                  <span style={{ fontSize }}>
                    Update to{" "}
                    {dprData.prevVersion
                      ? getVersion(dprData.prevVersion)
                      : getVersion(dprData.version)}
                  </span>
                </MDButton>
              )}
          </MDBox>
        </MDBox>
      </Feature>
    </DashboardLayout>
  );
}

export default ExportHOC(DprDetails);

import MDBox from "components/MDBox";
import { useEffect, useState } from "react";
import Constants, { Icons } from "utils/Constants";
import { IconButton } from "@mui/material";
import Author from "components/Table/Author";

export default function CENormData(
  CENormList,
  handleOpenNewModal,
  setModalType,
  editLists,
  setEditLists,
  handleDelete
) {
  const [ceNormRows, setCENormRows] = useState([]);
  const handleEdit = (item) => {
    setModalType("Update");
    setEditLists({ ...editLists, ceNorms: item });
    handleOpenNewModal("CE Norm");
  };

  useEffect(() => {
    if (CENormList) {
      const list = CENormList?.map((item) => {
        const temp = {
          name: <Author name={item?.name} />,
          month: <Author name={item?.month} />,
          action: (
            <MDBox>
              <IconButton
                color="secondary"
                fontSize="medium"
                onClick={() => handleEdit(item)}
                sx={{ cursor: "pointer" }}
              >
                {Icons.EDIT}
              </IconButton>{" "}
              &nbsp;
              <IconButton
                color="secondary"
                fontSize="medium"
                sx={{ cursor: "pointer" }}
                onClick={() => handleDelete("ceNorms", item[Constants.MONGOOSE_ID])}
              >
                {Icons.DELETE}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setCENormRows([...list]);
    }
  }, [CENormList]);

  return {
    ceNormColumns: [
      { Header: "Name", accessor: "name", width: "40%", align: "left" },
      { Header: "Month", accessor: "month", width: "40%", align: "left" },
      { Header: "Action", accessor: "action", width: "10%", align: "center" },
    ],
    ceNormRows,
  };
}

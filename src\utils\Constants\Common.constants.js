// color constants
const Common = {
  SMALL_SIGNATURE: "signature",
  SMALL_IMAGE: "image",
  ALL: "all",
  DELETED: "Deleted",

  // File Constants
  IMAGE_JPEG: "image/jpeg",
  IMAGE_JPG: "image/jpg",
  IMAGE_PNG: "image/png",
  IMAGE_SVG: "image/svg+xml",
  PDF_FILE: "application/pdf",
  PDF: "pdf",
  FRAME: "Frame",
  UPDATE: "Update",
  NEW: "New",

  // Label Constants - Certificate
  CERTIFICATE_UPLOAD_LABEL: "Certificate (PDF, Image)*",
  CERTIFICATE_TYPE_LABEL: "Certificate Type*",
  CERTIFICATE_START_END_DATE_LABEL: "Certificate Start and End Date",
  USERS_LABEL: "Select User*",

  START_DATE_LABEL: "Start Date",
  END_DATE_LABEL: "End Date",
  DATE_FORMAT_MM_DD_YYYY: "mm/dd/yyyy",

  SERIAL_NUMBER: "serialNumber",
  UNIQUE: "unique",
  EQUIPMENT_TYPE: "equipmentType",
  WEIGHT_FORM: "weightForm",
  QUANTITY_TYPE: "quantityType",
  PRICE: "price",
  EQUIPMENT_CATEGORY: "equipmentCategory",
  HS_CODE: "hsCode",
  RENTAL: "rental",
  WEIGHT: "weight",
  WAREHOUSE: "warehouse",
  QUANTITY: "quantity",
  CONDITION: "condition",

  SELECT_PROJECT_STATUS_HINT_TEXT: "Select Project Status",
  SELECTED_PROJECT_STATUS_LIST: "projectStatusList",
  CLOSED_PROJECT_STATUS: "closed",

  // API Network Status Code
  API_STATUS_200: 200,
  API_STATUS_201: 201,
  API_STATUS_401: 401,
  API_STATUS_422: 422,
  API_STATUS_404: 404,
  API_STATUS_400: 400,
  API_STATUS_500: 500,

  // Keyboard Keys Name
  ARROW_DOWN_KEY: "ArrowDown",
  ENTER_KEY: "Enter",

  // Start And End for Dates
  START: "start",
  START_DATE: "startDate",
  END: "end",
  END_DATE: "endDate",
  CURRENT_TIME_ZONE: Intl.DateTimeFormat().resolvedOptions().timeZone,
  DEFAULT_TIME_ZONE: "Europe/London",
  CURRENT_TIME_ZONE_FORMAT: "YYYY-MM-DD HH:mm:ss",
  CURRENT_TIME_ZONE_T_FORMAT: "YYYY-MM-DD[T]HH:mm:ss",
  YEARS: "years",

  // Employment Type
  SELF_EMPLOYED: "self-employed",
  INTERNAL_EMPLOYEE: "employee",
  EXTERNAL_EMPLOYEE: "external/umbrella Company",

  INTERNAL_EMPLOYEE_API: "internal-employee",
  EXTERNAL_EMPLOYEE_API: "external-employee",

  // Setting page's card keys names
  ROLES_AND_PERMISSION: "roles-permission",
  PROJECT_STATUS: "project-status",

  // Role Management ==> Role create Modal
  ASSIGNED_PROJECT_LABEL: "Assign Projects",
  ASSIGNED_PROJECT_NAME: "isAssignAllProjects",
  ACCESS_TYPE_LABEL: "Access Type",
  ACCESS_TYPE_NAME: "accessType",

  OWN_KEY: "own",
  ALL_KEY: "all",
  BOTH_KEY: "both",

  // DPR
  COMPLETE_IN_SMALL_CASE: "complete",
  SAVE_IN_SMALL_CASE: "save",
  SELECT_CONDITION: "Select Condition",
  DPR_STATUS_OPEN: "open",
  DPR_STATUS_SUBMITTED: "submitted",
  DPR_STATUS_CLOSED: "closed",
  DPR_STATUS_REJECTED: "rejected",
  DPR_STATUS_APPROVED: "approved",
  DPR_STATUS_CANCELLED: "cancelled",
  DPR_STATUS_IN_PROGRESS: "in-progress",
  DPR_STATUS_COMPLETED: "completed",

  // Project Status Confirm Modal
  PROJECT_STATUS_CONFIRM_MODAL_TITLE_MESSAGE: "Are you sure you want to proceed ?",
  PROJECT_STATUS_CONFIRM_MODAL_BODY_MESSAGE:
    "You are currently only showing Closed projects, please change the settings to see the new submission.",
  // Label Constants - DPR
  CONTACT_ON_DPR: "Contact On DPR",
  SHOW_DETAILED_PROGRESS_LABEL: "Show Detailed Progress",

  // Project Orders Request Screen Variables
  TEMPORARY_ITEMS_TYPE_TITLE_TEXT: "TMP",
  TEMPORARY_ITEMS_TYPES_TEXT: "temporaryItemsType",
  REQUESTED_ITEMS_TYPES_TEXT_KEY: "equipmentOrders",
  TEMPORARY_ITEMS_TYPES_TEXT_KEY: "tempOrders",
  PROJECT_TEXT: "project",
  EQUIPMENT_TEXT: "equipment",
  USER_TEXT: "user",

  PROJECT_ORDER_PROJECT_LABEL: "Project",
  PROJECT_ORDER_PROJECT_NAME: "project",
  PROJECT_ORDER_EQUIPMENT_TYPE_LABEL: "Equipment Type",
  PROJECT_ORDER_EQUIPMENT_TYPE_NAME: "equipmentType",
  PROJECT_ORDER_QUANTITY_LABEL: "Requested Qty",
  PROJECT_ORDER_QUANTITY_NAME: "engineerRequestedQuantity",
  PROJECT_ORDER_COMMENTS_LABEL: "Comments",
  PROJECT_ORDER_COMMENTS_NAME: "engineerComment",

  PROJECT_ORDER_TEMPORARY_PRODUCT_LABEL: "Equipment Name",
  PROJECT_ORDER_TEMPORARY_PRODUCT_NAME: "productName",
  PROJECT_ORDER_TEMPORARY_QUANTITY_LABEL: "Quantity",
  PROJECT_ORDER_TEMPORARY_QUANTITY_NAME: "engineerRequestedQuantity",

  // Project Order History Status
  PROJECT_ORDER_HISTORY_APPROVED_STATUS: "approved",
  PROJECT_ORDER_HISTORY_REJECTED_STATUS: "rejected",
  PROJECT_ORDER_HISTORY_CHECK_IN_STATUS: "check-in",

  // Project Order Shopping Cart Modal Form Variables
  PROJECT_ORDER_SHOPPING_CART_TITLE_LABEL: "Title",
  PROJECT_ORDER_SHOPPING_CART_TITLE_NAME: "title",
  PROJECT_ORDER_SHOPPING_CART_PROJECT_LABEL: "Project Name",
  PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME: "project",
  PROJECT_ORDER_SHOPPING_CART_FROM_DATE_LABEL: "From Date",
  PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME: "fromDate",
  PROJECT_ORDER_SHOPPING_CART_TO_DATE_LABEL: "To Date",
  PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME: "toDate",

  PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_LABEL: "Equipment",
  PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME: "equipmentType",
  PROJECT_ORDER_SHOPPING_CART_QUANTITY_LABEL: "Quantity",
  PROJECT_ORDER_SHOPPING_CART_QUANTITY_NAME: "engineerRequestedQuantity",
  PROJECT_ORDDER_SHOPPING_CART_COMMENTS_LABEL: "Comments",
  PROJECT_ORDER_SHOPPING_CART_COMMENTS_NAME: "engineerComment",

  PROJECT_ORDER_QUEUE_SINGLE: "singleItemCart",

  // Inventory
  TEMP_CURRENCY: "TMP",

  // Status Constants
  Open: "Open",
  Closed: "Closed",
  Completed: "Completed",

  // Profile Fields
  RESOURCE_NUMBER: "Resource number",

  // QHSE Dates
  CUSTOM_RANGE: "Custom Range",
  CUSTOM_RANGE_ID: "custom range",

  // Profile(Personal Tab) Screen Labels
  USUAL_FIRST_NAME_LABEL: "Usual First Name",
  FIRST_NAME_LABEL: "First Name",
  LAST_NAME_LABEL: "Last Name",
  MAIN_LANGUAGE_LABEL: "Main Language",
  ADDRESS_LABEL: "Address",
  EMAIL_LABEL: "Email",
  RESOURCE_NUMBER_LABEL: "Resource Number",
  COUNTRY_LABEL: "Country",
  CITY_LABEL: "City",
  STREET_LABEL: "Street/Building",
  AREA_LABEL: "Area",
  ZIP_CODE_LABEL: "Zip Code",
  STATE_LABEL: "State",
  NATIONALITY_LABEL: "Nationality",
  CONTACT_NUMBER_LABEL: "Contact Number",
  EMERGENCY_CONTACT_NUMBER_LABEL: "Emergency Contact Number",
  ROLE_NAME_LABEL: "Role Name",
  PROFILE_FUNCTION_LABEL: "Profile Function",
  AS_PER_PASSPORT_LABEL: "(As Per Passport)",
  PREF_AIRPORT_LABEL: "Preferred Airport Of Departure",
  OTHER_PREF_AIRPORT_LABEL: "Other Preferred Airport Of Departure",
  TRAVEL_TIME_TO_AIRPORT_LABEL: "Travel Time To Airport",
  SECOND_PREF_AIRPORT_LABEL: "Second Pref. Airport Of Departure",
  OTHER_SECOND_PREF_AIRPORT_LABEL: "Other Second Pref. Airport Of Departure",
  TRAVEL_TIME_TO_SECOND_AIRPORT_LABEL: "Travel Time To 2nd Airport",
  CLOTHES_SIZE_LABEL: "Clothes Size",
  SHOE_SIZE_LABEL: "Shoe Size",
  WINDA_ID_LABEL: "WINDA ID",
  CV_LABEL: "Curriculum Vitae (CV)",
  NEXT_OF_KIN_LABEL: "Next Of Kin",

  // Contractual Tab
  PASSPORT_ID_LABEL: "Passport/ID",
  PASSPORT_ID_DOCUMENT_LABEL: "Passport/ID Document",
  PASSPORT_ISSUE_DATE_LABEL: "Passport Issue Date",
  PASSPORT_EXPIRY_DATE_LABEL: "Passport Expiry Date",
  SECOND_PASSPORT_ID_LABEL: "Second Passport/ID",
  SECOND_PASSPORT_ID_DOCUMENT_LABEL: "Second Passport/ID Document",
  SECOND_PASSPORT_ISSUE_DATE_LABEL: "Second Passport Issue Date",
  SECOND_PASSPORT_EXPIRY_DATE_LABEL: "Second Passport Expiry Date",
  NATIONAL_ID_LABEL: "National Identification Number (BSN/NINO/SSN etc.)",
  DRIVERS_LICENSE_LABEL: "Driver's License",
  DRIVING_LICENSE_NUMBER_LABEL: "Driving License Number",
  DRIVING_LICENSE_ISSUE_DATE_LABEL: "Driving License Issue Date",
  DRIVING_LICENSE_EXPIRY_DATE_LABEL: "Driving License Expiry Date",
  SEAMANS_BOOKLET_LABEL: "Seamans Booklet",
  SEAMANS_BOOKLET_ISSUE_DATE_LABEL: "Seamans Booklet Issue Date",
  SEAMANS_BOOKLET_EXPIRY_DATE_LABEL: "Seamans Booklet Expiry Date",

  DATE_OF_BIRTH_LABEL: "Date Of Birth",
  PLACE_OF_BIRTH_LABEL: "Passport Place Of Birth",
  EMPLOYMENT_TYPE_LABEL: "Employment Type",

  LIABILITY_INSURANCE_LABEL: "Liability Insurance Document",
  LIABILITY_INSURANCE_ID_LABEL: "Liability Insurance/ID",
  LIABILITY_INSURANCE_ISSUE_DATE_LABEL: "Liability Insurance Issue Date",
  LIABILITY_INSURANCE_EXPIRY_DATE_LABEL: "Liability Insurance Expiry Date",
  HEALTH_INSURANCE_LABEL: "Health Insurance Incl. Repatriation Document",
  HEALTH_INSURANCE_ID_LABEL: "Health Insurance/ID",
  HEALTH_INSURANCE_ISSUE_DATE_LABEL: "Health Insurance Issue Date",
  HEALTH_INSURANCE_EXPIRY_DATE_LABEL: "Health Insurance Expiry Date",

  COMPANY_NAME_LABEL: "Company Name",
  COMPANY_REGISTRATION_NR_LABEL: "Company Registration Nr.",
  COMPANY_VAT_NR_LABEL: "Company VAT Nr.",
  COMPANY_ADDRESS_LABEL: "Company Address",

  BANK_NAME_LABEL: "Bank Name",
  ACCOUNT_HOLDER_LABEL: "Account Name Holder",
  BANK_ACCOUNT_NR_LABEL: "Bank Account Nr (IBAN)",
  BIC_SWIFT_LABEL: "BIC/SWIFT",

  // QHSE General Constants
  DOCUMENT_NAME: "Document Name*",
  UPLOAD_DOCUMENT: "Upload Document*",
  UPDATE_DOCUMENT: "Update Document*",

  // Setup Report Constants
  ASSET_PER_LOCATION: "Asset per location",
  MULTIPLE_ASSETS: "Multiple assets",
  LOCATION: "Location",
};

export default Common;

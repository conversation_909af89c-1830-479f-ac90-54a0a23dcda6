import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useLocation } from "react-router-dom";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import FormTextArea from "components/Form/FTextArea";

// Constants
import Constants, { defaultData, Common } from "utils/Constants";

// Slice
import { setEquipmentRemarks, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";
import { openSnackbar } from "redux/Slice/Notification";

// Thunk
import { getDprEquipmentData } from "redux/Thunks/Dpr";

// Data
import EquipmentDprData from "layouts/dpr/data/equipmentData";

function EquipmentTab() {
  const dispatch = useDispatch();
  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { id } = useParams();

  const { dprData, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  const getEquipmentData = async () => {
    try {
      await dispatch(getDprEquipmentData(id));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const { equipmentColumns, equipmentRows } = EquipmentDprData(
    dprData?.equipmentList?.equipmentList || []
  );

  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getEquipmentData();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  useEffect(() => {
    if (displayedDprTabsObj?.equipmentTab > 0) {
      getEquipmentData()
        .then(() => dispatch(updateIsLatestDataApiCompleted(true)))
        .catch(() => dispatch(updateIsLatestDataApiCompleted(false)));
    }
  }, [displayedDprTabsObj?.equipmentTab]);

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Equipment
          </MDTypography>
        </MDBox>
        <MDBox>
          <Card>
            <MDBox>
              <DataTable
                table={{ columns: equipmentColumns, rows: equipmentRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={Constants.FULFILLED}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.equipmentList?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setEquipmentRemarks(e.target.value));
            }}
          />
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default EquipmentTab;

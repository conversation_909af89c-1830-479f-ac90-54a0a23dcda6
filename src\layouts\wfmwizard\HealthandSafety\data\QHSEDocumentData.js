import { useEffect, useState } from "react";

// Material Components
import MDBox from "components/MDBox";
import Icon from "@mui/material/Icon";
import { Tooltip } from "@mui/material";

// Common Components
import Author from "components/Table/Author";
import MDTypography from "components/MDTypography";

// Assets
import Pdf from "assets/images/pdf.svg";
import Frame from "assets/images/Frame.svg";

// Utils
import Constants, { defaultData, Icons } from "utils/Constants";

// 3rd party Libraries
import moment from "moment";

// Methods
import { downloadFile } from "utils/methods/methods";

export default function QHSEDocumentData(
  documentList,
  handleOpenUploadModal,
  modalType,
  editData,
  handleEditDocument,
  handleOpenDeleteModal,
  handleViewImage
) {
  const [rows, setRows] = useState([]);
  const [loadingStatus, setLoadingStatus] = useState({});
  const today = new Date();
  const todaytDateFormat = moment(today).format(defaultData.WEB_DATE_FORMAT);
  const handleEdit = (item) => {
    handleEditDocument(item);
  };

  useEffect(() => {
    if (documentList) {
      const list = documentList?.map((item) => {
        const temp = {
          documentName: <Author name={item.documentName} style={{ textTransform: "capitalize" }} />,

          document: (
            <MDBox
              display="flex"
              sx={{ cursor: "pointer" }}
              onClick={() => handleViewImage(item.documentUrl)}
            >
              <img
                src={item.documentUrl?.includes(".pdf") ? Pdf : item.documentUrl || Frame}
                alt="Preview"
                height="60px"
                width="60px"
                style={{
                  border: "1px solid #D0D5DD",
                  borderRadius: "8px",
                  marginTop: "5px",
                }}
              />

              <MDBox ml={1}>
                {item?.documentName &&
                item?.documentName?.length > defaultData.MEDIUM_CONTENT_LENGTH ? (
                  <MDTypography
                    display="block"
                    variant="caption"
                    sx={{ textTransform: "capitalize", marginTop: "5px" }}
                  >
                    <Tooltip title={item?.documentName}>
                      <span>{`${item?.documentName.slice(
                        0,
                        defaultData.MEDIUM_CONTENT_LENGTH
                      )}...`}</span>
                    </Tooltip>
                  </MDTypography>
                ) : (
                  <MDTypography
                    display="block"
                    variant="caption"
                    sx={{ textTransform: "capitalize", marginTop: "5px" }}
                  >
                    {item?.documentName}
                  </MDTypography>
                )}
                {item?.createdAt && (
                  <MDTypography display="block" variant="caption" color="text">
                    {`Upload Date: ${moment(item?.createdAt).format(defaultData.WEB_DATE_FORMAT)}`}
                  </MDTypography>
                )}
              </MDBox>
            </MDBox>
          ),
          action: (
            <MDBox>
              <Icon
                fontSize="medium"
                sx={{
                  cursor: "pointer",
                  opacity: loadingStatus[item.documentUrl] ? 0.7 : 1,
                  pointerEvents: loadingStatus[item.documentUrl] ? "none" : "auto",
                }}
                onClick={async (event) => {
                  try {
                    setLoadingStatus((prev) => ({ ...prev, [item.documentUrl]: true }));
                    const fileExt = item?.documentUrl?.split(".").pop();
                    let docName = item?.documentName;
                    if (docName.length > defaultData.MEDIUM_CONTENT_LENGTH) {
                      docName = `${docName.slice(0, defaultData.MEDIUM_CONTENT_LENGTH)}...`;
                    }
                    const fileName = `${docName}_${todaytDateFormat}.${fileExt}`;
                    await downloadFile(item?.documentUrl, event, fileName);
                  } catch (error) {
                    console.error("Download failed:", error);
                  } finally {
                    setLoadingStatus((prev) => ({ ...prev, [item.documentUrl]: false }));
                  }
                }}
              >
                {loadingStatus[item.documentUrl] ? Icons.LOADING : Icons.DOWNLOAD}
              </Icon>
              &nbsp;
              <Icon
                color="secondary"
                fontSize="medium"
                onClick={() => handleEdit(item)}
                sx={{ cursor: "pointer" }}
              >
                {Icons.EDIT}
              </Icon>{" "}
              &nbsp;
              <Icon
                color="secondary"
                fontSize="medium"
                sx={{ cursor: "pointer" }}
                onClick={() => handleOpenDeleteModal(item[Constants.MONGOOSE_ID])}
              >
                {Icons.DELETE}
              </Icon>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [documentList, loadingStatus]);

  return {
    QHSEDocumentColumns: [
      { Header: "Document", accessor: "document", width: "25%", align: "left" },
      { Header: "Document Name", accessor: "documentName", width: "20%", align: "left" },
      { Header: "Action", accessor: "action", width: "5%", align: "center" },
    ],
    QHSEDocumentRows: rows,
  };
}

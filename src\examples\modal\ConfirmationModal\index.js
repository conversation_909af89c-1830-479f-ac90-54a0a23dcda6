import { useEffect, useState } from "react";

// 3rd party library
import PropTypes from "prop-types";

// MUI components
import { Grid, Icon, Modal, TextField } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import MDTypography from "components/MDTypography";
import ModalTitle from "examples/NewDesign/ModalTitle";

// Styles
import style from "assets/style/Modal";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Constants
import { ButtonTitles, Icons } from "utils/Constants";

function ConfirmationModal({ open, handleClose, title, handleAction, isBtnDisabled }) {
  const [reason, setReason] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(false);
  }, [open]);

  const handleApproveReject = () => {
    setLoading(true);
    const data = {
      action: title === "Approve Permission" ? "approve" : "reject",
      reason,
    };
    handleAction(data);
  };
  return (
    <Modal
      open={open}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <MDBox sx={style}>
        <MDBox
          key="box"
          bgColor="info"
          p={3}
          mb={1}
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          borderRadius="lg"
          sx={{ borderBottomRightRadius: 0, borderBottomLeftRadius: 0, height: pxToRem(72) }}
        >
          <ModalTitle title={title} color="white" />
          <Icon sx={{ cursor: "pointer", color: "beige" }} fontSize="medium" onClick={handleClose}>
            {Icons.CROSS}
          </Icon>
        </MDBox>
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          px={3}
          py={2}
          sx={{ maxHeight: 500 }}
        >
          <MDTypography>
            {title === "Approve Permission"
              ? "Are you sure you want to approve permission?"
              : "Are you sure you want to reject permission?"}
          </MDTypography>
          {title === "Reject Permission" ? (
            <TextField
              name="reason"
              label="Comment"
              multiline
              rows={4}
              onChange={(e) => setReason(e.target.value)}
            />
          ) : null}
        </MDBox>
        <MDBox px={2} mb={2}>
          <Grid container direction="row" justifyContent="flex-end" alignItems="center">
            <Grid item xs={2}>
              {title === "Approve Permission" ? (
                <MDButton
                  variant="contained"
                  color="info"
                  onClick={handleApproveReject}
                  disabled={isBtnDisabled}
                >
                  {!loading ? ButtonTitles.APPROVE_LICENSE : ButtonTitles.LOADING}
                </MDButton>
              ) : (
                <MDButton
                  variant="contained"
                  color="info"
                  onClick={handleApproveReject}
                  disabled={isBtnDisabled}
                >
                  {!loading ? ButtonTitles.REJECT_LICENSE : ButtonTitles.LOADING}
                </MDButton>
              )}
            </Grid>
          </Grid>
        </MDBox>
      </MDBox>
    </Modal>
  );
}

ConfirmationModal.defaultProps = {
  title: "",
  open: false,
  handleClose: () => {},
  handleAction: () => {},
  isBtnDisabled: false,
};

ConfirmationModal.propTypes = {
  title: PropTypes.string,
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  handleAction: PropTypes.func,
  isBtnDisabled: PropTypes.bool,
};

export default ConfirmationModal;

import React, { useMemo } from "react";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import CustomImage from "components/Table/GroupImage";

// Utils
import Constants from "utils/Constants";

export default function ProjectOrderHistoryEquipmentData({ dataList = [] }) {
  const equipmentTypeRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      equipmentName: (
        <MDBox
          display="flex"
          flexDirection="row"
          justifyContent="flex-start"
          alignItems="center"
          gap={1}
        >
          <CustomImage item={item?.equipment?.equipmentImage?.[0]?.url} />
          <Author name={item?.equipment?.name} />
        </MDBox>
      ),
      serialNumber: <Author name={item?.equipment?.serialNumber || Constants.NA} />,
      status: <Author name={item?.status} />,
      linkedQuantity: <Author name={item.wmDispatchQuantity} />,
    }));
  }, [dataList]);

  const equipmentTypeColumn = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Equipment Name", accessor: "equipmentName" },
    { Header: "Serial Number", accessor: "serialNumber" },
    { Header: "Status", accessor: "status" },
    { Header: "Linked Quantity", accessor: "linkedQuantity" },
  ];

  const tableData = {
    equipmentTypeColumn,
    equipmentTypeRows,
  };

  return tableData;
}

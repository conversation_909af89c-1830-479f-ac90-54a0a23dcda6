const mongooseId = "_id";

const filtersModuleName = {
  // All Filters Module Name OR Titles
  PROJECT: "Project",
  TITLE: "Title",
  LOCATION: "Location",
  TEAM: "Team",
  STATUS: "Status",
  SEARCH: "Search",
  COUNTRY: "Country",
  NATIONALITY: "Nationality",
  PROFILE_FUNCTION: "Profile Function",
  CREATED: "Date",
  NAME: "Name",
  FUNCTION: "Function",
  MISSING: "Missing",
  CONDITION: "Condition",
  ASSET: "Asset",

  // All Filter Modules Title Object
  SHIFT_DETAILS_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },
  REPORT_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },
  TOOLBOX_TALK_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },
  USER_MANAGEMENT_FILTERS_TITLE_OBJ: { [mongooseId]: "All", title: "All" },
  DPR_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },
  PROJECT_TRACKER_FILTERS_TITLE_OBJ: { [mongooseId]: "select", title: "Select" },
  QHSE_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },
  PROJECT_ORDERS_FILTERS_TITLE_OBJ: { [mongooseId]: "all", title: "All" },

  SHIFT_DETAILS_CREATED_FILTER_OPTIONS: [
    "All",
    "Today",
    "Yesterday",
    "This Week",
    "This Month",
    "Last Month",
    "This Year",
  ],
  SHIFT_DETAILS_STATUS_FILTER_OPTIONS: [
    "All",
    "Open",
    "Submitted",
    "Checked",
    "In Discussion",
    "Closed",
  ],

  // All Filter Modules Selected Value And Error Messages
  ALL_IN_SMALL_CASE: "all",
  ALL_WITH_FIRST_LETTER_CAPITAL: "All",
  NO_DATA_FOUND: "No data found",
  SELECT_IN_SMALL_CASE: "select",
};

export default filtersModuleName;

import React, { useEffect, useState } from "react";

// MUI Components
import { Grid, Icon } from "@mui/material";
import pxToRem from "assets/theme/functions/pxToRem";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";

// Custom components
import CustomButton from "examples/NewDesign/CustomButton";
import ResetFilterButton from "components/Buttons/ResetButton";
import CustomAutoComeplete from "components/Dropdown/CustomAutoComeplete";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import BasicModal from "examples/modal/BasicModal/BasicModal";
import FormTextArea from "components/Form/FTextArea";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { logbookListThunk, createLogNote, deleteUserLogNote } from "redux/Thunks/UserManagement";
import { projectListThunk } from "redux/Thunks/Filter";
import { openSnackbar } from "redux/Slice/Notification";

// Utils
import Constants, {
  ButtonTitles,
  Colors,
  Icons,
  defaultData,
  FiltersModuleName,
  ModalContent,
  Common,
} from "utils/Constants";

// 3rd party library
import moment from "moment";
import { paramCreater } from "utils/methods/methods";

const logbookFiltersArr = [
  {
    inputLabel: FiltersModuleName.PROJECT,
    list: [FiltersModuleName.TOOLBOX_TALK_FILTERS_TITLE_OBJ],
    selectedValue: "",
  },
];

function logBook({ userId, permission }) {
  const dispatch = useDispatch();
  const { config } = useSelector((state) => state.config);
  const { userDetails } = useSelector((state) => state.users);

  const [filters, setFilters] = useState(logbookFiltersArr);
  const [shouldUpdateState, setShouldUpdateState] = useState(false);
  const [loading, setLoading] = useState(Constants.PENDING);
  const [logbookNotes, setLogbookNotes] = useState([]);
  const [deleteNoteData, setDeleteNoteData] = useState({
    id: "",
    open: false,
  });

  // Add Note Modal States
  const [openAddNoteModal, setOpenAddNoteModal] = useState(false);
  const [addNoteForm, setAddNoteForm] = useState({
    content: "",
    project: "",
  });
  const [addNoteErrors, setAddNoteErrors] = useState({});
  const [addNoteLoading, setAddNoteLoading] = useState(false);
  const [projectList, setProjectList] = useState([]);

  // Get Logbook List Func
  const getLogbookData = async (filterParams) => {
    const updatedFilterParams = paramCreater(filterParams);
    const body = {
      user: userId || userDetails[Constants.MONGOOSE_ID],
      params: updatedFilterParams,
    };
    setLoading(Constants.PENDING);

    const res = await dispatch(logbookListThunk(body));
    if (res.payload.status === Common.API_STATUS_200) {
      setLogbookNotes(res?.payload?.data?.data);
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setLoading(Constants.FULFILLED);
  };

  // Get Project List Func for dropdown
  const getProjectListFunc = async () => {
    try {
      const projectLists = await dispatch(projectListThunk());
      if (projectLists?.payload?.status) {
        const projects = projectLists.payload.data;
        setProjectList(projects);

        setFilters((prev) => {
          const updatedFilters = prev.map((filter) => {
            if (filter.inputLabel === FiltersModuleName.PROJECT) {
              return {
                ...filter,
                selectedValue: projectLists?.payload?.data?.some(
                  (project) => project[Constants.MONGOOSE_ID] === filters[0]?.selectedValue
                )
                  ? filters[0]?.selectedValue
                  : FiltersModuleName.ALL_IN_SMALL_CASE,
                list: [FiltersModuleName.TOOLBOX_TALK_FILTERS_TITLE_OBJ, ...projects],
              };
            }
            return filter;
          });
          return updatedFilters;
        });
        setShouldUpdateState((prev) => !prev);
      }
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleResetFilter = () => {
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => ({
        ...filter,
        selectedValue: filter.list[0][Constants.MONGOOSE_ID] || filter.list[0],
      }));
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  const handleFilterChange = async (e) => {
    const { value, name } = e.target;
    setLoading(Constants.PENDING);

    setFilters((prevFilters) => {
      const updatedFilters = prevFilters.map((filter) => {
        if (filter.inputLabel === name) {
          return { ...filter, selectedValue: value };
        }
        return filter;
      });
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  const handleOpenDeleteModal = (deleteId) => {
    setDeleteNoteData({
      id: deleteId,
      open: true,
    });
  };

  const handleDeleteNote = async () => {
    const res = await dispatch(deleteUserLogNote(deleteNoteData.id));
    if (res?.payload?.status === Common.API_STATUS_200) {
      const updatedNotes = logbookNotes.filter(
        (note) => note[Constants.MONGOOSE_ID] !== deleteNoteData.id
      );
      setLogbookNotes(updatedNotes);
      dispatch(
        openSnackbar({
          message: Constants.NOTE_DELETED_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      setDeleteNoteData({ id: "", open: false });
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  // Add Note Modal Functions
  const handleOpenAddNoteModal = () => {
    setOpenAddNoteModal(true);
    setAddNoteForm({
      content: "",
      project: "",
    });
    setAddNoteErrors({});
  };

  const handleCloseAddNoteModal = () => {
    setOpenAddNoteModal(false);
    setAddNoteForm({
      content: "",
      project: "",
    });
    setAddNoteErrors({});
    setAddNoteLoading(false);
  };

  const handleAddNoteFormChange = (name, value) => {
    setAddNoteForm((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (addNoteErrors[name]) {
      setAddNoteErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateAddNoteForm = () => {
    const errors = {};

    if (!addNoteForm.content.trim()) {
      errors.content = Constants.NOTE_DESCRIPTION_REQUIRED;
    }

    setAddNoteErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddNote = async () => {
    if (!validateAddNoteForm()) {
      return;
    }

    setAddNoteLoading(true);
    const data = {
      ...(addNoteForm.project ? { project: addNoteForm.project } : {}),
      description: addNoteForm.content,
      user: userId,
    };

    const body = {
      data,
    };
    const addNoteRes = await dispatch(createLogNote(body));
    if (addNoteRes?.payload?.status === 201) {
      setShouldUpdateState((prev) => !prev);
      dispatch(
        openSnackbar({
          message: Constants.NOTE_CREATED_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      handleCloseAddNoteModal();
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }

    setAddNoteLoading(false);
  };

  useEffect(() => {
    getProjectListFunc();
    setLoading(Constants.FULFILLED);
  }, []);

  useEffect(() => {
    const filterParams = {
      project:
        filters[0].selectedValue !== FiltersModuleName.ALL_IN_SMALL_CASE
          ? filters[0].selectedValue
          : "",
    };
    getLogbookData(filterParams);
  }, [shouldUpdateState, filters]);

  return (
    <MDBox p={2}>
      {/* Header */}
      <MDBox display="flex" justifyContent="space-between" mb={2}>
        <MDTypography variant="h5" fontWeight="medium" color="text" textTransform="capitalize">
          Logbook
        </MDTypography>
        {permission?.create &&
          config[0]?.id !== userId &&
          config[0]?.id !== userDetails[Constants.MONGOOSE_ID] && (
            <CustomButton
              title={ButtonTitles.ADD_NOTE}
              icon={Icons.NEW}
              background={Colors.PRIMARY}
              color={Colors.WHITE}
              openModal={handleOpenAddNoteModal}
            />
          )}
      </MDBox>

      {/* Filter Section */}
      {permission?.read && (
        <MDBox display="flex" justifyContent="space-between" mx={0} mb={3}>
          <MDBox
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "start",
              alignItems: "flex-end",
            }}
            mx={0}
          >
            {filters.map((val) => (
              <MDBox width="300px" mr={2} key={val.inputLabel}>
                <CustomAutoComeplete
                  label={val.inputLabel}
                  id={val.inputLabel}
                  name={val.inputLabel}
                  hint={`Select ${val.inputLabel}`}
                  valueStyle={{
                    height: pxToRem(40),
                    padding: pxToRem(1),
                    backgroundColor: Colors.WHITE,
                  }}
                  labelStyle={{
                    fontSize: pxToRem(14),
                    fontWeight: 600,
                    color: Colors.BLACK,
                  }}
                  handleChange={handleFilterChange}
                  menu={val.list}
                  getOptionLabel={(option) => option.title || ""}
                  value={
                    val.selectedValue && val.selectedValue !== ""
                      ? {
                          title:
                            val.list?.find(
                              (option) => option[Constants.MONGOOSE_ID] === val.selectedValue
                            )?.title || "",
                        }
                      : null
                  }
                />
              </MDBox>
            ))}
            <ResetFilterButton handleReset={handleResetFilter} style={{ marginLeft: "1rem" }} />
          </MDBox>
        </MDBox>
      )}

      {/* Notes List */}
      <MDBox>
        <MDTypography variant="h6" fontWeight="medium" display="none" color="text" mb={2}>
          Notes ({logbookNotes.length})
        </MDTypography>

        <Grid
          container
          spacing={2}
          sx={{
            maxHeight: "60vh",
            overflow: "auto",
            "::-webkit-scrollbar": {
              width: "5px",
            },
            "::-webkit-scrollbar-thumb": {
              background: Colors.LIGHT_GRAY,
            },
            scrollbarWidth: "thin",
            scrollbarColor: "gray transparent",
          }}
        >
          {permission?.read &&
            loading !== Constants.PENDING &&
            logbookNotes.length > 0 &&
            logbookNotes.map((note) => (
              <Grid item xs={12} lg={10} key={note[Constants.MONGOOSE_ID]}>
                <MDBox
                  display="flex"
                  border={`1px solid ${Colors.LIGHT_GRAY}`}
                  borderRadius="8px"
                  p={2}
                  position="relative"
                  mb={1}
                  sx={{ backgroundColor: Colors.BOX_BG }}
                >
                  <MDBox sx={{ position: "relative" }}>
                    {note?.createdBy?.profileImage !== "" &&
                    note?.createdBy?.profileImage !== null ? (
                      <img
                        src={note?.createdBy?.profileImage}
                        alt={note?.createdBy?.firstName}
                        style={{
                          width: pxToRem(50),
                          height: pxToRem(50),
                          borderRadius: "50%",
                          objectFit: "cover",
                        }}
                      />
                    ) : (
                      <MDBox
                        sx={{
                          backgroundColor: Colors.PRIMARY,
                          borderRadius: "50%",
                          width: pxToRem(50),
                          height: pxToRem(50),
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <MDTypography
                          sx={{
                            color: Colors.WHITE,
                            fontSize: pxToRem(16),
                            fontWeight: "500",
                            backgroundColor: Colors.PRIMARY,
                          }}
                        >
                          {note?.createdBy?.callingName
                            ? note?.createdBy?.callingName?.charAt(0)?.toUpperCase()
                            : note?.createdBy?.firstName?.charAt(0)?.toUpperCase()}
                        </MDTypography>
                      </MDBox>
                    )}
                  </MDBox>

                  <MDBox display="flex" flexDirection="column" flex={1} ml={2}>
                    <MDBox mt={0}>
                      <MDTypography
                        display="block"
                        variant="caption"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "600",
                          fontSize: pxToRem(14),
                        }}
                      >
                        {`${
                          note?.createdBy?.callingName
                            ? note?.createdBy?.callingName
                            : note?.createdBy?.firstName
                        } ${note?.createdBy?.lastName}`}
                      </MDTypography>

                      <MDBox display="flex" alignItems="center" gap={1} mt={0.5}>
                        <MDTypography display="inline" variant="caption" color="text">
                          {moment(note?.createdAt).format(defaultData.WEB_DATE_FORMAT)}
                        </MDTypography>

                        {note?.project?.title && (
                          <MDBox
                            sx={{
                              backgroundColor: "#F1F1F1",
                              borderRadius: pxToRem(16),
                              px: pxToRem(8),
                              py: pxToRem(2),
                              height: pxToRem(32),
                              display: "inline-flex",
                              alignItems: "center",
                              textTransform: "uppercase",
                            }}
                          >
                            <MDTypography
                              sx={{
                                color: "#191D31",
                                fontSize: pxToRem(13),
                                fontWeight: "500",
                                lineHeight: 1,
                              }}
                            >
                              {note?.project?.title}
                            </MDTypography>
                          </MDBox>
                        )}
                      </MDBox>
                    </MDBox>

                    <MDBox mt={1}>
                      <MDTypography
                        display="block"
                        variant="caption"
                        fontWeight="400"
                        sx={{
                          color: "#424866",
                          whiteSpace: "pre-line",
                          textAlign: "justify",
                          wordBreak: "break-word",
                          overflowWrap: "break-word",
                          fontSize: pxToRem(14),
                          lineHeight: 1.4,
                        }}
                      >
                        {note?.description}
                      </MDTypography>
                    </MDBox>
                  </MDBox>

                  {(config[0]?.id === note?.createdBy[Constants.MONGOOSE_ID] ||
                    config[0]?.role === defaultData.ADMIN_ROLE) &&
                    permission?.delete && (
                      <MDBox position="absolute" top={-10} right={-10} zIndex={2}>
                        <Icon
                          sx={{
                            color: "white",
                            width: 30,
                            height: 30,
                            cursor: "pointer",
                            zIndex: 1,
                          }}
                          onClick={() => {
                            handleOpenDeleteModal(note[Constants.MONGOOSE_ID]);
                          }}
                        >
                          {Icons.CROSS2}
                        </Icon>
                      </MDBox>
                    )}
                </MDBox>
              </Grid>
            ))}

          {loading !== Constants.PENDING && logbookNotes.length === 0 && permission?.read && (
            <Grid item xs={12}>
              <MDBox textAlign="center" py={4}>
                <MDTypography
                  variant="h6"
                  fontWeight="medium"
                  color="text"
                  textTransform="capitalize"
                >
                  No Notes found
                </MDTypography>
              </MDBox>
            </Grid>
          )}
          {loading !== Constants.PENDING && !permission?.read && (
            <Grid item xs={12}>
              <MDBox textAlign="center" py={4}>
                <MDTypography variant="h6" fontWeight="medium" color="text">
                  You dont have read access. Please contact your admin.
                </MDTypography>
              </MDBox>
            </Grid>
          )}
        </Grid>
        {loading === Constants.PENDING && (
          <MDBox
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
            display="flex"
            justifyContent="center"
            alignItems="center"
          >
            {Icons.LOADING2}
          </MDBox>
        )}
      </MDBox>

      {/* Add Note Modal */}
      <BasicModal
        open={openAddNoteModal}
        handleClose={handleCloseAddNoteModal}
        title="Add Note"
        actionButton={addNoteLoading ? ButtonTitles.SUBMIT_LOADING : ButtonTitles.SUBMIT}
        handleAction={handleAddNote}
        disabled={addNoteLoading}
        minWidth={600}
      >
        <Grid container spacing={2}>
          {/* Project Field */}
          <Grid item xs={12}>
            <CustomAutoComeplete
              label="Project"
              id="project"
              name="project"
              hint="Select Project"
              valueStyle={{
                height: pxToRem(40),
                padding: pxToRem(1),
                backgroundColor: Colors.WHITE,
              }}
              labelStyle={{
                fontSize: pxToRem(14),
                fontWeight: 500,
                color: Colors.BLACK,
              }}
              handleChange={(e) => handleAddNoteFormChange("project", e.target.value)}
              menu={projectList}
              getOptionLabel={(option) => option.title || ""}
              value={
                addNoteForm.project && addNoteForm.project !== ""
                  ? {
                      title:
                        projectList?.find(
                          (option) => option[Constants.MONGOOSE_ID] === addNoteForm.project
                        )?.title || "",
                    }
                  : null
              }
            />
          </Grid>

          {/* Note Field */}
          <Grid item xs={12}>
            <MDTypography
              sx={{
                fontSize: pxToRem(16),
                fontWeight: 500,
                color: "#344054",
                lineHeight: "20px",
              }}
            >
              Add Note*
            </MDTypography>
            <FormTextArea
              name="content"
              placeholder="Enter your note here..."
              value={addNoteForm.content}
              error={Boolean(addNoteErrors.content)}
              helperText={addNoteErrors.content}
              handleChange={(e) => handleAddNoteFormChange("content", e.target.value)}
              backgroundColor={Colors.WHITE}
            />
          </Grid>
        </Grid>
      </BasicModal>

      {/* Delete Modal */}
      <DeleteModal
        open={deleteNoteData.open}
        title={ModalContent.DELETE_NOTE}
        message={ModalContent.DELETE_NOTE_MESSAGE}
        handleClose={() => {
          setDeleteNoteData({ ...deleteNoteData, open: false });
        }}
        handleDelete={handleDeleteNote}
      />
    </MDBox>
  );
}

export default logBook;

import React, { useMemo } from "react";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import CustomImage from "components/Table/GroupImage";
import Status from "components/Table/Status";
import { IconButton } from "@mui/material";
import { Icons } from "utils/Constants";

export default function ProjectReturnOrderEquipmentData({ dataList = [], openRemarkModal }) {
  const equipmentTypeRows = useMemo(() => {
    if (dataList?.length === 0) return [];

    return dataList?.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      equipmentType: (
        <MDBox display="flex" justifyContent="start" alignItems="center">
          <CustomImage
            item={item?.equipment?.equipmentTypeImage?.[0]?.url}
            width={30}
            height={30}
          />
          <MDBox
            style={{
              flex: 1,
              width: "100%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            <Author name={item?.equipment?.equipmentType?.type} />
          </MDBox>
        </MDBox>
      ),
      equipmentCategory: <Author name={item?.equipment?.equipmentType?.equipmentCategory?.name} />,
      returnQuantity: <Author name={item?.pmDispatchQuantity} />,
      status: <Status title={item?.status ? `${item?.status?.replace("-", " ")}` : ""} />,
      wmremark: (
        <MDBox>
          <IconButton
            aria-label="fingerprint"
            color="info"
            disabled={item?.wmComment?.length === 0 || item?.wmComment === ""}
            onClick={() => openRemarkModal("user", item?.wmComment)}
          >
            {item?.wmComment?.length === 0 || item?.wmComment === ""
              ? Icons.COMMENT_DISABLE
              : Icons.COMMENT}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList]);

  const equipmentTypeColumn = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Equipment Type", accessor: "equipmentType" },
    { Header: "Equipment Category", accessor: "equipmentCategory" },
    { Header: "Return Quantity", accessor: "returnQuantity" },
    { Header: "Status", accessor: "status" },
    { Header: "WM Remark", accessor: "wmremark" },
  ];

  const tableData = {
    equipmentTypeColumn,
    equipmentTypeRows,
  };

  return tableData;
}

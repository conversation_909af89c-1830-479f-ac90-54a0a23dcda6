import { useMemo, useEffect, useState } from "react";

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// react-table components
import { useTable, usePagination, useGlobalFilter, useAsyncDebounce, useSortBy } from "react-table";

// @mui material components
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Autocomplete from "@mui/material/Autocomplete";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDInput from "components/MDInput";
import QuickJumperPagination from "examples/Tables/DataTable/QuickJumperPagination";
import bgIm from "assets/images/t2CCNu53AN.gif";

// Material Dashboard 2 React example components
import DataTableHeadCell from "examples/Tables/DataTable/DataTableHeadCell";
import { Box, Pagination, TableCell } from "@mui/material";

import Constants, { defaultData } from "utils/Constants";
import { useSelector } from "react-redux";
import Session from "utils/Sessions";
import jwtDecode from "jwt-decode";

function InvenotryDataTable({
  entriesPerPage,
  canSearch,
  showTotalEntries,
  table,
  isSorted,
  // noEndBorder,
  loading,
  licenseRequired,
  // currentPage, handleTablePagination, handleCurrentPage are use when need data size is more
  // Use this parameter to retrieve additional data when a user visits the last page
  currentPage,
  handleTablePagination,
  handleCurrentPage,
  backgroundColor, // New prop to specify the background color
  textColor,
  extraContent,
  isGotoVisisble,
  totalRecords,
}) {
  const defaultValue = entriesPerPage.defaultValue ? entriesPerPage.defaultValue : 10;
  const entries = entriesPerPage.entries
    ? entriesPerPage.entries.map((el) => el.toString())
    : ["25"];
  const [status, setStatus] = useState(Constants.PENDING);
  const ConfigData = useSelector((state) => state.config);

  const columns = useMemo(() => table.columns, [table]);
  const data = useMemo(() => table.rows, [table]) || [];

  const [visibleData, setVisibleData] = useState([]);

  useEffect(() => {
    if (data?.length > 0) {
      setVisibleData(data.slice(0, 25));
    }
  }, [data]);

  useEffect(() => {
    const interval = setInterval(() => {
      setVisibleData((prev) => {
        if (prev.length >= data.length) {
          clearInterval(interval);
          return prev;
        }
        const newData = data.slice(prev.length, prev.length + 25);
        return [...prev, ...newData];
      });
    }, 100);

    return () => clearInterval(interval);
  }, [data]);

  const tableInstance = useTable(
    { columns, data, initialState: { pageIndex: currentPage } },
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    page,
    pageOptions,
    gotoPage,
    setPageSize,
    setGlobalFilter,
    state: { pageIndex, pageSize, globalFilter },
  } = tableInstance;

  // Set the default value for the entries per page when component mounts
  useEffect(() => setPageSize(defaultValue || 10), [defaultValue]);

  // update the status on role change, when superadmin login as admin and data changes
  useEffect(() => {
    const token = jwtDecode(Session.userToken);
    const { role } = token;

    const setPending = () => setStatus(Constants.PENDING);
    const setRejected = () => setStatus(Constants.REJECTED);
    const setFulfilled = () => setStatus(Constants.FULFILLED);
    const setNoData = () => setStatus("noData");

    const isSuperAdmin =
      role === defaultData.SUPER_ADMIN_ROLE && !Session.isSuperAdminViewingAdminPanel;
    const isPending = ConfigData.loading === Constants.PENDING || loading === Constants.PENDING;
    const isRejected = ConfigData.loading === Constants.REJECTED || loading === Constants.REJECTED;
    const isFulfilledWithData =
      loading === Constants.FULFILLED && rows.length > 0 && pageOptions.length > 0;

    const isPending2 =
      loading === Constants.FULFILLED && table?.rows?.length > 0 && rows.length === 0;

    const isFulfilledNoData =
      loading === Constants.FULFILLED && rows.length === 0 && pageOptions.length === 0;
    const isConfigFulfilled = ConfigData.loading === "fulfilled";

    // for superadmin
    if (isSuperAdmin || !licenseRequired) {
      if (loading === Constants.PENDING || isPending2) setPending();
      else if (isRejected) setRejected();
      else if (isFulfilledWithData) setFulfilled();
      else if (isFulfilledNoData) setNoData();
    }
    // for admin
    else if (isPending || isPending2) setPending();
    else if (isRejected) setRejected();
    else if (isConfigFulfilled && isFulfilledWithData) setFulfilled();
    else if (isConfigFulfilled && isFulfilledNoData) setNoData();
  }, [
    Session.userToken,
    Session.isSuperAdminViewingAdminPanel,
    ConfigData.loading,
    table,
    visibleData,
  ]);

  // set current page to last page when the current page has no data
  // Works when some data is deleted from the last page
  useEffect(() => {
    if (pageOptions.length > 0 && pageOptions.length <= currentPage) {
      const lastPage = pageOptions.length - 1;
      gotoPage(lastPage);
      handleCurrentPage(lastPage);
    } else if (data.length === 0) {
      gotoPage(0);
      handleCurrentPage(0);
    }
  }, [pageOptions, data, currentPage]);
  // Set the entries per page value based on the select value
  const setEntriesPerPage = (value) => setPageSize(value);

  // Search input value state
  const [search, setSearch] = useState(globalFilter);

  // Search input state handle
  const onSearchChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 100);

  // A function that sets the sorted value for the table
  const setSortedValue = (column) => {
    let sortedValue;

    if (isSorted && column.isSorted) {
      sortedValue = column.isSortedDesc ? "desc" : "asce";
    } else if (isSorted) {
      sortedValue = "none";
    } else {
      sortedValue = false;
    }

    return sortedValue;
  };

  const handlePageChange = (event, value) => {
    const newPageIndex = value - 1; // Convert 1-based index to 0-based index

    if (newPageIndex >= 0 && newPageIndex < pageOptions.length) {
      gotoPage(newPageIndex);
      handleCurrentPage(newPageIndex);
      handleTablePagination(newPageIndex);
    } else {
      // Reset to the first page if the page index is invalid
      gotoPage(0);
      handleCurrentPage(0);
    }
  };

  return (
    <MDBox
      sx={{
        backgroundColor: "White",
        borderRadius: "10px",
        border: "1px solid #E0E6F5",
        width: "100%",
      }}
    >
      <TableContainer sx={{ boxShadow: "none" }}>
        {entriesPerPage.entries || canSearch ? (
          <MDBox
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            p={3}
            backgroundColor={backgroundColor}
            color={textColor}
          >
            {entriesPerPage && (
              <MDBox display="flex" alignItems="center">
                <Autocomplete
                  disableClearable
                  value={pageSize.toString()}
                  options={entries}
                  onChange={(event, newValue) => {
                    setEntriesPerPage(parseInt(newValue, 10));
                  }}
                  size="small"
                  sx={{ width: "5rem" }}
                  renderInput={(params) => <MDInput {...params} />}
                />
                <MDTypography variant="caption" color="secondary">
                  &nbsp;&nbsp;entries per page
                </MDTypography>
              </MDBox>
            )}
            {canSearch && (
              <MDBox width="12rem" ml="auto">
                <MDInput
                  placeholder="Search..."
                  value={search}
                  size="small"
                  fullWidth
                  onChange={({ currentTarget }) => {
                    setSearch(search);
                    onSearchChange(currentTarget.value);
                  }}
                />
              </MDBox>
            )}
          </MDBox>
        ) : null}

        <Table {...getTableProps()}>
          <MDBox component="thead">
            {headerGroups.map((headerGroup) => (
              <TableRow {...headerGroup.getHeaderGroupProps()}>
                {headerGroup.headers.map((column) => (
                  <DataTableHeadCell
                    {...column.getHeaderProps(isSorted && column.getSortByToggleProps())}
                    width={column.width ? column.width : "auto"}
                    align={column.align ? column.align : "left"}
                    sorted={setSortedValue(column)}
                    backgroundColor={backgroundColor}
                    textColor={textColor}
                  >
                    {column.render("Header")}
                  </DataTableHeadCell>
                ))}
              </TableRow>
            ))}
          </MDBox>

          <TableBody {...getTableBodyProps()}>
            {(() => {
              switch (status) {
                case Constants.PENDING:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDBox py={5} display="flex" justifyContent="center" alignItems="center">
                          <Box
                            component="img"
                            src={bgIm}
                            alt="Wind Turbine"
                            sx={{
                              objectFit: "cover",
                            }}
                          />
                        </MDBox>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.FULFILLED:
                  return page.map((row, key) => {
                    prepareRow(row);
                    return (
                      <TableRow
                        {...row.getRowProps()}
                        sx={{ background: key % 2 !== 0 ? "#f6f7ff" : "transparent" }}
                      >
                        {row.cells.map((cell) => (
                          <TableCell
                            key={cell.column.id}
                            align={cell.column.align ? cell.column.align : "left"}
                            {...cell.getCellProps()}
                            sx={{
                              backgroundColor:
                                cell?.value?.props?.cellColor && cell?.value?.props?.cellColor,
                              whiteSpace: "nowrap",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              maxWidth: "200px",
                              border: "1px solid #E0E6F5",
                              padding: "4px 24px",
                            }}
                          >
                            {cell.render("Cell")}
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                  });

                case "noData":
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.NO_DATA_FOUND}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.REJECTED:
                default:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.SOMETHING_WENT_WRONG}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );
              }
            })()}
          </TableBody>
        </Table>
      </TableContainer>

      {status === Constants.FULFILLED &&
        page.length > 0 &&
        (pageIndex > 0 || totalRecords > pageSize) && (
          <MDBox
            sx={{
              color: "#f6f7ff",
              backgroundColor: "#f6f7ff",
              position: "sticky",
              bottom: 0,
              left: 0,
              right: 0,
              height: "60px",
              border: "1px solid #E0E6F5",
            }}
            display="flex"
            flexDirection={{ xs: "column", sm: "row" }}
            justifyContent="center"
            alignItems={{ xs: "flex-start", sm: "center" }}
            p={!showTotalEntries && pageOptions.length === 1 ? 0 : 3}
          >
            <Pagination
              count={Math.ceil(totalRecords / pageSize)}
              page={currentPage + 1}
              onChange={(event, value) => handlePageChange(event, value)}
              variant="outlined"
              shape="rounded"
              sx={{
                "& .Mui-selected:hover": {
                  backgroundColor: "#f6f7ff",
                },
                "& .Mui-selected": {
                  backgroundColor: "#e0e1f5",
                },
                ".MuiPaginationItem-root": {
                  borderRadius: "50%",
                  border: "none",
                },
              }}
            />
            {isGotoVisisble && (
              <QuickJumperPagination
                totalPages={Math.ceil(totalRecords / pageSize)}
                onPageChange={handlePageChange}
              />
            )}
          </MDBox>
        )}
      {extraContent}
    </MDBox>
  );
}

// Setting default values for the props of DataTable
InvenotryDataTable.defaultProps = {
  entriesPerPage: { defaultValue: 10, entries: [5, 10, 15, 20, 25] },
  canSearch: false,
  showTotalEntries: true,
  pagination: { variant: "gradient", color: "info" },
  isSorted: true,
  noEndBorder: false,
  currentPage: 0,
  loading: Constants.PENDING,
  licenseRequired: false,
  handleTablePagination: () => {},
  handleCurrentPage: () => {},
  backgroundColor: "", // Add a default value for backgroundColor
  textColor: "",
  extraContent: null,
  isGotoVisisble: false,
  totalRecords: 250,
};

// Typechecking props for the DataTable
InvenotryDataTable.propTypes = {
  entriesPerPage: PropTypes.oneOfType([
    PropTypes.shape({
      defaultValue: PropTypes.number,
      entries: PropTypes.arrayOf(PropTypes.number),
    }),
    PropTypes.bool,
  ]),
  canSearch: PropTypes.bool,
  showTotalEntries: PropTypes.bool,
  table: PropTypes.objectOf(PropTypes.array).isRequired,
  pagination: PropTypes.shape({
    variant: PropTypes.oneOf(["contained", "gradient"]),
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "dark",
      "light",
    ]),
  }),
  isSorted: PropTypes.bool,
  noEndBorder: PropTypes.bool,
  loading: PropTypes.string,
  licenseRequired: PropTypes.bool,
  handleTablePagination: PropTypes.func,
  currentPage: PropTypes.number,
  handleCurrentPage: PropTypes.func,
  backgroundColor: PropTypes.string,
  textColor: PropTypes.string,
  extraContent: PropTypes.node,
  isGotoVisisble: PropTypes.bool,
  totalRecords: PropTypes.number,
};

export default InvenotryDataTable;

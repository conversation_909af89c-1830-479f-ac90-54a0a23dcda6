import { useEffect, useState } from "react";
import moment from "moment";

// Common Components
import Author from "components/Table/Author";
import MDTypography from "components/MDTypography";

import { defaultData } from "utils/Constants";
import { getFormattedCallingFirstName } from "utils/methods/methods";

export default function AcknowledgeMemberData(acknowledgeMemberList) {
  const [rows, setRows] = useState([]);
  useEffect(() => {
    if (acknowledgeMemberList) {
      const list = acknowledgeMemberList?.map((item, index) => {
        const temp = {
          srno: <Author name={index + 1} />,
          memberName: <Author name={getFormattedCallingFirstName(item?.user)} />,
          acknowledgeDate: (
            <MDTypography variant="caption" sx={{ cursor: "pointer" }}>
              {item?.acknowledgedAt
                ? moment(item?.acknowledgedAt).format(
                    defaultData.WEB_24_HOURS_FORMAT_WITHOUT_SECOND
                  )
                : "---"}
            </MDTypography>
          ),
          isAcknowledged: (
            <Author
              name={item?.isAcknowledged ? "Yes" : "No"}
              style={{ textTransform: "capitalize" }}
            />
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [acknowledgeMemberList]);

  return {
    acknowledgeMemberColumns: [
      { Header: "No.", accessor: "srno", width: "10%", align: "left" },
      { Header: "Member Name", accessor: "memberName", width: "50%", align: "left" },
      { Header: "Acknowledged At", accessor: "acknowledgeDate", width: "30%", align: "left" },
      { Header: "Status", accessor: "isAcknowledged", width: "10%", align: "left" },
    ],
    acknowledgeMemberRows: rows,
  };
}

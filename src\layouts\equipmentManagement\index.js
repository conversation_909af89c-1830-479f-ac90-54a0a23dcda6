import React from "react";

// MUI Components
import { Card, Divider } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";

// Assets
import bgImage from "assets/images/NewScreen.png";

function EquipmentManagement() {
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <PageTitle title="Equipment Management" />
      <Divider sx={{ marginTop: 2 }} />
      <Card sx={{ marginTop: 3, height: "80vh" }}>
        <MDBox
          mt={5}
          mb={1}
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{ height: "100vh" }}
        >
          <img
            src={bgImage}
            alt="Preview"
            style={{
              height: "490px",
              width: "420px",
              objectFit: "cover",
              opacity: "10%",
            }}
          />
        </MDBox>
      </Card>
    </DashboardLayout>
  );
}

export default EquipmentManagement;

import React, { useEffect, useState } from "react";

// MUI Components
import MDBox from "components/MDBox";
import { Card, Divider } from "@mui/material";
import pxToRem from "assets/theme/functions/pxToRem";
import MDTypography from "components/MDTypography";

// Custom Components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import DoughnutPieChart from "components/Charts/doughnutPieChart";
import ResetFilterButton from "components/Buttons/ResetButton";
import CustomAutoComeplete from "components/Dropdown/CustomAutoComeplete";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";

// Constant
import Constants, { Colors, Icons, Common, FiltersModuleName } from "utils/Constants";

// Methods
import { capitalizeFirstLetter } from "utils/methods/methods";

// Redux Component
import getQhseDashboardData, {
  getRiskOfIncidents,
  getTypeOfIncidents,
} from "redux/Thunks/qhseDashboard";
import { useDispatch } from "react-redux";
import { openSnackbar } from "redux/Slice/Notification";
import { projectListThunk } from "redux/Thunks/Filter";
import FTextField from "components/Form/FTextField";

const qhseCardStatuses = ["In discussion", "Open", "Closed", "Submitted", "Archived"];
const qhseCardTypes = ["Safe", "Unsafe", "NCR", "Incident"];
const riskChartTypes = ["Low", "Medium", "High"];
const incidentTypes = [
  "Near Miss",
  "Injury / Illness",
  "Damage / Breakdown / Loss",
  "Spill / Pollution / Emission",
  "Breach of law / Contract",
  "Other non-work events",
];

const chartColors = (title) => {
  let colors;
  switch (title.toLowerCase()) {
    case "open":
    case "unsafe":
    case "high":
      colors = {
        backgroundColor: Colors.LIGHT_BROWN,
        fontColor: Colors.DARK_BROWN,
      };
      break;

    case "submitted":
    case "incident":
      colors = {
        backgroundColor: Colors.LIGHT_ORANGE2,
        fontColor: Colors.DARK_ORANGE,
      };
      break;

    case "closed":
    case "safe":
    case "low":
      colors = {
        backgroundColor: Colors.LIGHT_GREEN2,
        fontColor: Colors.DARK_GREEN2,
      };
      break;

    case "in_discussion":
    case "medium":
      colors = {
        backgroundColor: Colors.LIGHT_YELLOW2,
        fontColor: Colors.DARK_YELLOW,
      };
      break;

    case "ncr":
      colors = {
        backgroundColor: Colors.LIGHT_BLUE,
        fontColor: Colors.DARK_BLUE,
      };
      break;

    default:
      colors = {
        backgroundColor: Colors.LIGHT_GRAY,
        fontColor: Colors.DARK_GREY,
      };
      break;
  }
  return colors;
};

const chartTypeColors = (title) => {
  let colors;
  switch (title.toLowerCase()) {
    case "near-miss":
      colors = {
        backgroundColor: "#CBD7E5",
        fontColor: "#637D98",
      };
      break;
    case "injury / illness":
      colors = {
        backgroundColor: "#F6CDCE",
        fontColor: "#BF6F71",
      };
      break;
    case "damage / breakdown / loss":
      colors = {
        backgroundColor: "#F7BB80",
        fontColor: "#C08A54",
      };
      break;

    case "spill / pollution / emission":
      colors = {
        backgroundColor: "#ADD4D1",
        fontColor: "#7FA7A4",
      };
      break;
    case "breach of law / contract":
      colors = {
        backgroundColor: "#9BC795",
        fontColor: "#698F63",
      };
      break;
    case "other non-work events":
      colors = {
        backgroundColor: "#F4DF91",
        fontColor: "#B6A460",
      };
      break;

    default:
      colors = {
        backgroundColor: Colors.LIGHT_GRAY,
        fontColor: Colors.DARK_GREY,
      };
      break;
  }
  return colors;
};

function QhseManagement() {
  const mongooseId = "_id";
  const dispatch = useDispatch();
  // Initialize state variables
  const [cardStatusCount, setCardStatusCount] = useState([]);
  const [cardTypeCount, setCardTypeCount] = useState([]);
  const [riskTypeCount, setRiskTypeCount] = useState([]);
  const [typeOfIncidentCount, setTypeOfIncidentCount] = useState([]);
  const [combinedCountList, setCombinedCountList] = useState([]);
  const [filters, setFilters] = useState([
    {
      inputLabel: "Project",
      list: [
        {
          [Constants.MONGOOSE_ID]: FiltersModuleName.ALL_IN_SMALL_CASE,
          title: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
        },
      ],
      selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
    },
    {
      inputLabel: "Created",
      list: [
        FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
        "Today",
        "Yesterday",
        "This Week",
        "This Month",
        "This Year",
        Common.CUSTOM_RANGE,
      ].map((item) => ({
        [Constants.MONGOOSE_ID]:
          item === FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL
            ? FiltersModuleName.ALL_IN_SMALL_CASE
            : item.toLowerCase(),
        title: item,
      })),
      selectedValue: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
    },
  ]);
  const [totalCount, setTotalCount] = useState(0);
  const [riskTotalCount, setRiskTotalCount] = useState(0);
  const [totalIncidentCount, setTotalIncidentCount] = useState(0);
  const [loading, setLoading] = useState(true);

  // Fetch and process dashboard data
  const qhseDashboardData = async (params) => {
    try {
      if (params === null) {
        setLoading(false);
        return;
      }

      const [response, riskResponse, typeResponse] = await Promise.all([
        dispatch(getQhseDashboardData(params)),
        dispatch(getRiskOfIncidents(params)),
        dispatch(getTypeOfIncidents(params)),
      ]);

      // Handle main dashboard data
      const { payload } = response;
      if (payload.status === Common.API_STATUS_200) {
        const statusCountList = payload.data.data?.cardStatusCount || [];
        const typeCountList = payload.data.data?.cardTypeCounts || [];

        // Map status counts
        let cardStatusCountList = statusCountList.map((item) => {
          const { backgroundColor, fontColor } = chartColors(item.status);
          return {
            name:
              item.status === "in_discussion"
                ? "In discussion"
                : capitalizeFirstLetter(item.status),
            value: item.count,
            color: backgroundColor,
            textColor: fontColor,
          };
        });

        // Add missing statuses
        cardStatusCountList = [
          ...cardStatusCountList,
          ...qhseCardStatuses
            .filter((item) => !cardStatusCountList.some((card) => card.name === item))
            .map((item) => {
              const { backgroundColor, fontColor } = chartColors(
                item !== "In discussion" ? item : "in_discussion"
              );
              return {
                name: item,
                value: 0,
                color: backgroundColor,
                textColor: fontColor,
              };
            }),
        ];

        // Map type counts
        let cardTypeCountList = typeCountList.map((item) => {
          const { backgroundColor, fontColor } = chartColors(item.cardType);
          return {
            name: item.cardType === "ncr" ? "NCR" : capitalizeFirstLetter(item.cardType),
            value: item.count,
            color: backgroundColor,
            textColor: fontColor,
          };
        });

        // Add missing types
        cardTypeCountList = [
          ...cardTypeCountList,
          ...qhseCardTypes
            .filter((item) => !cardTypeCountList.some((card) => card.name === item))
            .map((item) => {
              const { backgroundColor, fontColor } = chartColors(item);
              return {
                name: item,
                value: 0,
                color: backgroundColor,
                textColor: fontColor,
              };
            }),
        ];

        setCardStatusCount(cardStatusCountList);
        setCardTypeCount(cardTypeCountList);
        setCombinedCountList([...cardStatusCountList, ...cardTypeCountList]);
        setTotalCount(payload.data.data?.totalCardCount || 0);
      } else {
        await dispatch(
          openSnackbar({ message: Constants.SOMETHING_WENT_WRONG, notificationType: "error" })
        );
      }

      // Handle risk data
      if (riskResponse.payload.status === Common.API_STATUS_200) {
        const typeRiskCountList = riskResponse.payload.data.data[0]?.riskOfIncidentCount || [];
        let riskTypeCountList = typeRiskCountList.map((item) => {
          const { backgroundColor, fontColor } = chartColors(item.cardType);
          return {
            name: capitalizeFirstLetter(item.cardType),
            value: item.count,
            color: backgroundColor,
            textColor: fontColor,
          };
        });

        // Add missing risk types
        riskTypeCountList = [
          ...riskTypeCountList,
          ...riskChartTypes
            .filter((item) => !riskTypeCountList.some((risk) => risk.name === item))
            .map((item) => {
              const { backgroundColor, fontColor } = chartColors(item);
              return {
                name: item,
                value: 0,
                color: backgroundColor,
                textColor: fontColor,
              };
            }),
        ];

        setRiskTypeCount(riskTypeCountList);
        setRiskTotalCount(riskResponse.payload.data.data[0]?.totalCardCount || 0);
      } else {
        await dispatch(
          openSnackbar({ message: Constants.SOMETHING_WENT_WRONG, notificationType: "error" })
        );
      }

      // Handle incident type data
      if (typeResponse.payload.status === Common.API_STATUS_200) {
        const typeCountList = typeResponse.payload.data.data.data || [];
        let incidentTypeCountList = typeCountList.map((item) => {
          const { backgroundColor, fontColor } = chartTypeColors(item.type);
          return {
            name: capitalizeFirstLetter(item.type),
            value: item.count,
            color: backgroundColor,
            textColor: fontColor,
          };
        });

        // Add missing incident types
        incidentTypeCountList = [
          ...incidentTypeCountList,
          ...incidentTypes
            .filter((item) => !incidentTypeCountList.some((incident) => incident.name === item))
            .map((item) => {
              const { backgroundColor, fontColor } = chartColors(item);
              return {
                name: item,
                value: 0,
                color: backgroundColor,
                textColor: fontColor,
              };
            }),
        ];

        setTypeOfIncidentCount(incidentTypeCountList);
        setTotalIncidentCount(typeResponse.payload.data.data.total || 0);
      }
    } catch (error) {
      setLoading(false);
      await dispatch(
        openSnackbar({ message: Constants.SOMETHING_WENT_WRONG, notificationType: "error" })
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch and process filters
  const fetchFilters = async () => {
    const projectList = await dispatch(projectListThunk());
    if (projectList.payload.status) {
      const temp = [...filters];
      temp[0].list = [
        {
          [mongooseId]: FiltersModuleName.ALL_IN_SMALL_CASE,
          title: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
        },
        ...projectList.payload.data,
      ];
      setFilters(temp);
    }
  };

  // Effect to load data on component mount
  useEffect(() => {
    setLoading(true);
    qhseDashboardData({
      project: FiltersModuleName.ALL_IN_SMALL_CASE,
      created: FiltersModuleName.ALL_IN_SMALL_CASE,
    });
    fetchFilters();
  }, []);

  const constructApiParams = (currentFilters) => {
    const projectFilter = currentFilters.find((f) => f.inputLabel === "Project");
    const createdFilter = currentFilters.find((f) => f.inputLabel === "Created");

    if (createdFilter?.selectedValue === Common.CUSTOM_RANGE_ID) {
      // Skip if custom is selected but either date is missing
      if (!createdFilter.customFrom || !createdFilter.customTo) {
        return null;
      }

      return {
        project: projectFilter?.selectedValue || FiltersModuleName.ALL_IN_SMALL_CASE,
        created: "custom",
        fromDate: createdFilter.customFrom || null,
        toDate: createdFilter.customTo || null,
      };
    }

    return {
      project: projectFilter?.selectedValue || FiltersModuleName.ALL_IN_SMALL_CASE,
      created:
        createdFilter?.selectedValue?.toLowerCase().replace(/ /g, "_") ||
        FiltersModuleName.ALL_IN_SMALL_CASE,
    };
  };

  const handleFilterChange = async (e) => {
    const { name, value } = e.target;
    const updatedFilters = filters.map((filter) => {
      if (filter.inputLabel === name) {
        return { ...filter, selectedValue: value[Constants.MONGOOSE_ID] || value };
      }
      return filter;
    });

    setFilters(updatedFilters);

    // If selecting Custom for date filter, only proceed with filter if both dates are already set
    const createdFilter = updatedFilters.find((filter) => filter.inputLabel === "Created");
    if (name === "Created" && (value[Constants.MONGOOSE_ID] || value) === Common.CUSTOM_RANGE_ID) {
      if (!createdFilter.customFrom || !createdFilter.customTo) {
        return; // Skip API call until both dates are selected
      }
    }

    await qhseDashboardData(constructApiParams(updatedFilters));
  };

  const handleCustomDateChange = async (field, value) => {
    const updatedFilters = filters.map((filter) => {
      if (filter.inputLabel === "Created") {
        return { ...filter, [field]: value };
      }
      return filter;
    });

    setFilters(updatedFilters);

    // Get the updated Created filter
    const createdFilter = updatedFilters.find((filter) => filter.inputLabel === "Created");

    // Only trigger API call if both dates are selected
    if (
      createdFilter &&
      createdFilter.selectedValue === Common.CUSTOM_RANGE_ID &&
      createdFilter.customFrom &&
      createdFilter.customTo
    ) {
      await qhseDashboardData(constructApiParams(updatedFilters));
    }
  };

  const handleReset = async () => {
    const resetFilters = filters.map((filter) => ({
      ...filter,
      selectedValue:
        filter.inputLabel === "Created"
          ? FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL
          : FiltersModuleName.ALL_IN_SMALL_CASE,
      customFrom: null,
      customTo: null,
    }));
    setFilters(resetFilters);
    await qhseDashboardData(constructApiParams(resetFilters));
  };
  const handleReload = async () => {
    setLoading(true);
    await qhseDashboardData(constructApiParams(filters));
    setLoading(false);
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox sx={{ display: "flex", justifyContent: "space-between" }}>
        <PageTitle title="QHSE Management" />
        <BasicButton
          icon={Icons.RELOAD}
          background={Colors.WHITE}
          border
          color={Colors.BLACK}
          action={handleReload}
        />
      </MDBox>
      <Divider sx={{ marginTop: 2 }} />
      <MDBox
        sx={{
          paddingBottom: 3,
        }}
      >
        {!loading ? (
          <Card sx={{ marginTop: 3, height: "fit-content", paddingBottom: 3 }}>
            <MDBox>
              <MDTypography
                sx={{
                  fontWeight: "600",
                  fontSize: pxToRem(26),
                  lineHeight: pxToRem(38),
                  textAlign: "center",
                  backgroundColor: "#191A51",
                  color: "#ffffff",
                  paddingX: pxToRem(10),
                  paddingY: pxToRem(16),
                  borderRadius: "0.75rem 0.75rem 0 0",
                }}
              >
                Overview of QHSE Cards
              </MDTypography>
              <MDBox sx={{ display: "flex", justifyContent: "center" }}>
                <MDBox
                  sx={{
                    padding: pxToRem(20),
                    marginTop: pxToRem(15),
                    marginRight: pxToRem(20),
                    display: "flex",
                    flexDirection: { lg: "row", md: "row", sm: "row", xs: "column" },
                    gap: pxToRem(20),
                    flexWrap: "wrap",
                    width: "fit-content",
                    height: "fit-content",
                  }}
                >
                  {combinedCountList.map((item) => {
                    const displayOrder = ["Open", "Unsafe", "Incident"];
                    if (displayOrder.includes(item.name)) {
                      return (
                        <MDBox
                          key={item.name}
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            width: pxToRem(180),
                            height: pxToRem(130),
                            backgroundColor: item.color,
                            borderRadius: pxToRem(16),
                            gap: pxToRem(1),
                            order: displayOrder.indexOf(item.name), // Set order based on index in displayOrder array
                          }}
                        >
                          <MDTypography
                            sx={{
                              fontSize: pxToRem(40),
                              fontWeight: "bold",
                              color: item.textColor,
                            }}
                          >
                            {item.value}
                          </MDTypography>
                          <MDTypography
                            sx={{
                              fontSize: pxToRem(20),
                              fontWeight: "bold",
                              color: item.textColor,
                            }}
                          >
                            {item.name}
                          </MDTypography>
                        </MDBox>
                      );
                    }
                    return null;
                  })}
                </MDBox>
                <MDBox
                  sx={{
                    marginTop: pxToRem(25),
                  }}
                >
                  <MDBox
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "flex-end",
                      gap: pxToRem(16),
                      marginTop: pxToRem(25),
                    }}
                  >
                    <MDBox
                      sx={{
                        display: "flex",
                        flexDirection: "row",
                        gap: pxToRem(16),
                        flexWrap: "wrap",
                      }}
                    >
                      {filters?.map((val) => (
                        <MDBox width={pxToRem(250)} key={val.inputLabel}>
                          <CustomAutoComeplete
                            key={val.inputLabel}
                            label={val.inputLabel === "Created" ? "Date" : val.inputLabel}
                            id={val.inputLabel}
                            name={val.inputLabel}
                            hint={`Select ${val.inputLabel}`}
                            valueStyle={{
                              height: pxToRem(40),
                              padding: pxToRem(1),
                              backgroundColor: Colors.WHITE,
                            }}
                            labelStyle={{
                              fontSize: pxToRem(14),
                              fontWeight: 600,
                              color: Colors.BLACK,
                            }}
                            handleChange={handleFilterChange}
                            menu={val.list}
                            getOptionLabel={(option) => option.title || option}
                            value={
                              val.selectedValue
                                ? {
                                    title:
                                      val.list?.find(
                                        (option) =>
                                          (option[Constants.MONGOOSE_ID] || option) ===
                                          val.selectedValue
                                      )?.title || val.selectedValue,
                                  }
                                : null
                            }
                          />
                          {/* Render date fields if "Custom" is selected */}
                          {val.inputLabel === "Created" &&
                            val.selectedValue === Common.CUSTOM_RANGE_ID && (
                              <MDBox display="flex" flexDirection="row" gap={1} mt={1}>
                                <FTextField
                                  label="Start Date"
                                  name="customFrom"
                                  type="date"
                                  value={val.customFrom || ""}
                                  error={!val.customFrom && val.customTo}
                                  helperText={!val.customFrom && val.customTo ? "Required" : ""}
                                  handleChange={(e) =>
                                    handleCustomDateChange("customFrom", e.target.value)
                                  }
                                  InputProps={{
                                    inputProps: {
                                      max: val.customTo || undefined,
                                      style: { textTransform: "uppercase" },
                                    },
                                  }}
                                  onKeyPress={(e) => {
                                    // Prevent manual typing, only allow date picker selection
                                    e.preventDefault();
                                  }}
                                />
                                <FTextField
                                  label="End Date"
                                  name="customTo"
                                  type="date"
                                  value={val.customTo || ""}
                                  error={val.customFrom && !val.customTo}
                                  helperText={val.customFrom && !val.customTo ? "Required" : ""}
                                  handleChange={(e) =>
                                    handleCustomDateChange("customTo", e.target.value)
                                  }
                                  InputProps={{
                                    inputProps: {
                                      min: val.customFrom || undefined,
                                      style: { textTransform: "uppercase" },
                                    },
                                  }}
                                  onKeyPress={(e) => {
                                    // Prevent manual typing, only allow date picker selection
                                    e.preventDefault();
                                  }}
                                />
                              </MDBox>
                            )}
                        </MDBox>
                      ))}
                      <MDBox mt={-1.5}>
                        <ResetFilterButton handleReset={handleReset} />
                      </MDBox>
                    </MDBox>
                  </MDBox>
                </MDBox>
              </MDBox>
              <MDBox
                sx={{
                  display: "flex",
                  justifyContent: {
                    lg: "center",
                    md: "center",
                    sm: "center",
                    xs: "center",
                  },
                  flexDirection: "row",
                  flexWrap: "wrap",
                  alignItems: "stretch",
                  paddingX: { lg: pxToRem(30), md: pxToRem(20), sm: pxToRem(15), xs: pxToRem(10) },
                  paddingY: pxToRem(20),
                  gap: pxToRem(20),
                  marginTop: pxToRem(20),
                }}
              >
                <MDBox
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    flex: "1 1 300px",
                    minWidth: { xs: "100%", sm: "300px", md: "300px", lg: "300px" },
                    gap: pxToRem(20),
                  }}
                >
                  <DoughnutPieChart
                    data={cardTypeCount}
                    title="Cards by Type"
                    totalCount={totalCount}
                  />
                  <DoughnutPieChart
                    data={typeOfIncidentCount}
                    title="Cards by Incident Type"
                    totalCount={totalIncidentCount}
                  />
                </MDBox>
                <MDBox
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    flex: "1 1 300px",
                    minWidth: { xs: "100%", sm: "300px", md: "300px", lg: "300px" },
                    gap: pxToRem(20),
                  }}
                >
                  <DoughnutPieChart
                    data={cardStatusCount}
                    title="Cards by Status"
                    totalCount={totalCount}
                  />
                  <DoughnutPieChart
                    data={riskTypeCount}
                    title="Cards by Risk Level"
                    totalCount={riskTotalCount}
                    isRiskChart
                  />
                </MDBox>
              </MDBox>
            </MDBox>
          </Card>
        ) : (
          <MDBox
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
            display="flex"
            justifyContent="center"
            alignItems="center"
          >
            {Icons.LOADING2}
          </MDBox>
        )}
      </MDBox>
    </DashboardLayout>
  );
}

export default QhseManagement;

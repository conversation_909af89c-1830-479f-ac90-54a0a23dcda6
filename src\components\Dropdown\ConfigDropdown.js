import { useState } from "react";

// MUI Components
import { FormControl, FormHelperText, IconButton, MenuItem, Select } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

// Custom Components
import FormControlErrorStyles from "assets/style/Component";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts";
import Ellipsis from "components/Table/Ellipsis";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Constants
import Constants, { defaultData } from "utils/Constants";

// 3rd party
import PropTypes from "prop-types";

export default function ConfigDropdown({
  width,
  label,
  id,
  name,
  value,
  defaultValue,
  hint,
  handleChange,
  menu,
  error,
  helperText,
  marginBottom,
  disabled,
  maxWidth,
  startAdornment = "",
}) {
  const [open, setOpen] = useState(false);
  const fontSize = FontComponent({ sizes: {} });

  return (
    <FormControl
      sx={{
        mr: 2,
        ml: 0,
        mt: pxToRem(8),
        minWidth: "100%",
        width,
        marginBottom,
        maxHeight: 400,
        ...FormControlErrorStyles,
        marginTop: 0.5,
      }}
      error={error}
      size="small"
    >
      <MDTypography variant="caption" mb={1} sx={{ fontSize, fontWeight: 500, color: "#344054" }}>
        {label}
      </MDTypography>
      <MDBox
        sx={{
          display: "flex",
          alignItems: "center",
          position: "relative",
        }}
      >
        <Select
          displayEmpty
          id={id}
          name={name}
          {...(!value && { defaultValue })}
          {...(!defaultValue && { value })}
          disabled={disabled}
          placeholder={hint}
          open={open}
          onClose={() => setOpen(false)}
          onOpen={() => setOpen(true)}
          sx={{
            height: 50,
            minWidth: "100%",
            "& .MuiInputBase-input": {
              fontSize: pxToRem(16),
              fontWeight: 400,
              color: "#667085",
            },
            textTransform: "capitalize",
            backgroundColor: "black",
            paddingY: "0.65rem",
            paddingRight: "0.55rem",
            maxHeight: 100,
            cursor: "pointer",
          }}
          startAdornment={startAdornment}
          MenuProps={{
            anchorOrigin: {
              vertical: 34,
              horizontal: "left",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "left",
            },
            PaperProps: {
              style: {
                maxHeight: 200,
                maxWidth,
                opacity: 1,
                transform: "none",
                border: "1px solid #D0D5DD",
              },
            },
          }}
          onChange={(e) => handleChange(name, e.target.value, id)}
          renderValue={(selected) => {
            const val = menu.find((opt) => opt?.[Constants.MONGOOSE_ID] === selected);
            const displayValue = val?.title || defaultValue || value || "Select";
            return (
              <Ellipsis
                title={displayValue}
                maxWidth="100%"
                style={{ textTransform: "capitalize" }}
              >
                {displayValue}
              </Ellipsis>
            );
          }}
        >
          <MenuItem disabled value="">
            Select
          </MenuItem>
          {menu.length > 0 ? (
            menu.map((item) => (
              <MenuItem
                value={item[Constants.MONGOOSE_ID] || item}
                id={item[Constants.MONGOOSE_ID] || item}
                sx={{
                  textTransform: "capitalize",
                  maxHeight: 400,
                  fontSize: pxToRem(16),
                  fontWeight: 400,
                  marginTop: "4px",
                  color: "#667085",
                }}
                key={item[Constants.MONGOOSE_ID] || item}
              >
                <Ellipsis
                  title={item.title || item}
                  maxWidth="100%"
                  style={{ textTransform: "capitalize", color: "#667085" }}
                >
                  {item.title || item}
                </Ellipsis>
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>No data available</MenuItem>
          )}
        </Select>
        <IconButton
          onClick={() => !disabled && setOpen(!open)}
          sx={{
            position: "absolute",
            top: "50%",
            right: 0,
            transform: "translateY(-50%)",
            zIndex: 1,
          }}
        >
          <KeyboardArrowDownIcon />
        </IconButton>
      </MDBox>
      <FormHelperText sx={{ marginLeft: 0 }}>{helperText}</FormHelperText>
    </FormControl>
  );
}

ConfigDropdown.defaultProps = {
  id: "",
  name: "",
  label: "",
  hint: "",
  width: "",
  error: false,
  helperText: "",
  marginBottom: "0",
  disabled: false,
  maxWidth: "100%",
  maxContent: defaultData.SMALLER_CONTENT_LENGTH,
  startAdornment: "",
  value: "",
  defaultValue: "",
};

ConfigDropdown.propTypes = {
  value: PropTypes.string,
  defaultValue: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  hint: PropTypes.string,
  width: PropTypes.string,
  error: PropTypes.bool,
  helperText: PropTypes.string,
  marginBottom: PropTypes.string,
  disabled: PropTypes.bool,
  maxWidth: PropTypes.string,
  maxContent: PropTypes.number,
  startAdornment: PropTypes.string,
  handleChange: PropTypes.func.isRequired,
  menu: PropTypes.arrayOf(PropTypes.object).isRequired,
};

import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import ProductListThunk, { EquipmentTypeThunk, equipmentUpdateThunk } from "redux/Thunks/Equipment";
import Constants from "utils/Constants";

const initialState = {
  loading: Constants.IDLE,
  list: [],
  stockData: [],
  inventoryListTotalRecords: 0,
  equipmentTypeList: [],
  isUpdateApiCalled: false,
  pagination: {
    allRecordsCount: 0,
    completed: [],
  },
};

export const productSlice = createSlice({
  name: "productSlice",
  initialState,
  reducers: {
    reloadData(state) {
      state.loading = Constants.PENDING;
    },

    resetListState(state) {
      if (state.list.length === 0) {
        state.loading = Constants.PENDING;
      }
      state.list = [];
      state.stockData = [];
    },

    updateStockCheckbox(state, action) {
      const { equipmentData, checked } = action.payload;

      if (checked) {
        // Add equipmentData to the stockData array when checked
        state.stockData.push(equipmentData);
      } else {
        // Remove equipmentData from the stockData array when unchecked
        state.stockData = state.stockData.filter(
          (item) => item[Constants.MONGOOSE_ID] !== equipmentData[Constants.MONGOOSE_ID]
        );
      }
    },

    updateQuantity(state, action) {
      const { stockQuantity, equipmentId } = action.payload;

      const updatedStockDataList = state.stockData.map((item) => {
        if (equipmentId !== item[Constants.MONGOOSE_ID]) return item;

        const totalStock = stockQuantity + parseInt(item.quantity, 10) || 0;

        return { ...item, stockQuantity, totalStock };
      });

      state.stockData = updatedStockDataList;
    },

    deleteStock(state, action) {
      const { deleteId } = action.payload;
      const filteredList = state.stockData.filter(
        (item) => item[Constants.MONGOOSE_ID] !== deleteId
      );
      state.stockData = filteredList;
    },
  },

  extraReducers: {
    [ProductListThunk.pending]: (state) => {
      if (state.isUpdateApiCalled === true) return;
      state.loading = Constants.PENDING;
    },

    [ProductListThunk.fulfilled]: (state, { payload, meta }) => {
      state.loading = Constants.FULFILLED;

      // Destructure data for easier access
      const { inventoryData, allRecordsCount } = payload?.data?.data || {};

      // Get pagination info from the meta argument
      const params = new URLSearchParams(meta.arg);
      const page = parseInt(params.get("page"), 10);
      const perPage = parseInt(params.get("perPage"), 10);

      const invenotryList =
        page === 0 ? Array.from({ length: allRecordsCount }, () => ({})) : [...state.list];

      // Calculate start index for pagination and replace elements in the array
      const startIndex = page * perPage;
      invenotryList.splice(startIndex, perPage, ...inventoryData);

      // Update state with new data
      state.list = invenotryList;
      state.inventoryListTotalRecords = allRecordsCount;
      state.pagination.completed = page === 0 ? [page] : [...state.pagination.completed, page];
    },
    [ProductListThunk.rejected]: (state, { payload }) => {
      const { canceled } = payload;
      if (!canceled) state.loading = Constants.REJECTED;
    },

    // Checks if the update API is called or not also checks the status of the API is success or not
    [equipmentUpdateThunk.pending]: (state) => {
      state.isUpdateApiCalled = false;
    },

    [equipmentUpdateThunk.fulfilled]: (state) => {
      state.isUpdateApiCalled = true;
    },

    [equipmentUpdateThunk.rejected]: (state) => {
      state.isUpdateApiCalled = false;
    },

    [resetStateThunk.fulfilled]: (state) => {
      state.loading = Constants.IDLE;
      state.list = [];
    },

    [EquipmentTypeThunk.fulfilled]: (state, action) => {
      const { payload } = action;
      state.equipmentTypeList = payload?.data?.data ?? [];
    },
  },
});

export const { reloadData, resetListState, updateQuantity, updateStockCheckbox, deleteStock } =
  productSlice.actions;
export default productSlice.reducer;

import React, { useState } from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box } from "@mui/material";

// Material Dashboard React example components
import ProfileInfoCard from "examples/Cards/InfoCards/ProfileInfoCard";

// Custom components
import ContractualDetailsDrawer from "examples/Drawers/profile/ContractualDetailsDrawer";

// 3rd party library
import moment from "moment";

// Constants from Utils
import Constants, { defaultData, Common } from "utils/Constants";
import { useSelector } from "react-redux";

function ContractualDetailsTab({ profile = {}, tabValue, onUpdate }) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens[11]?.screensInfo?.agreement;
  const { contractualDetail = {} } = profile;

  const handleDrawerOpen = () => {
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
    if (onUpdate) {
      onUpdate();
    }
  };

  const handleInsuranceInContractualDetails = (insuranceObj, docName) => {
    const fromDate = insuranceObj?.fromDate
      ? moment(insuranceObj?.fromDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;
    const toDate = insuranceObj?.toDate
      ? moment(insuranceObj?.toDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;

    return {
      name: `${docName || ""} (${fromDate} - ${toDate})`,
      url: insuranceObj?.url,
      [Constants.MONGOOSE_ID]: insuranceObj?.[Constants.MONGOOSE_ID],
    };
  };

  const formatDrivingLicence = (licenseObj) => {
    const expiryDate = licenseObj?.expiryDate
      ? moment(licenseObj?.expiryDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;
    const issueDate = licenseObj?.issueDate
      ? moment(licenseObj?.issueDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;

    if (licenseObj?.licenseNumber) {
      return `${licenseObj?.licenseNumber} (${issueDate} - ${expiryDate})`;
    }
    return Constants.NA;
  };

  const formatSeamansBook = (seamansBookObj) => {
    const fromDate = seamansBookObj?.fromDate
      ? moment(seamansBookObj?.fromDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;
    const toDate = seamansBookObj?.toDate
      ? moment(seamansBookObj?.toDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA;

    return {
      name: `${seamansBookObj?.name} (${fromDate} - ${toDate})`,
      url: seamansBookObj?.url,
      [Constants.MONGOOSE_ID]: seamansBookObj?.[Constants.MONGOOSE_ID],
    };
  };

  const formatPassportData = (passportType, passportNumber) => {
    const passportData = contractualDetail?.identityProof?.filter((item) => item[passportType]);

    if (!passportData || passportData.length === 0) {
      return Constants.NA;
    }

    let issueDate = passportData[0]?.fromDate;
    let expiryDate = passportData[0]?.toDate;

    const formattedData = passportData.map((item, index) => ({
      name: `Page ${index + 1}`,
      url: item.url,
      issueDate: item.fromDate,
      expiryDate: item.toDate,
      [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
    }));

    expiryDate = expiryDate ? moment(expiryDate).format(defaultData.WEB_DATE_FORMAT) : Constants.NA;
    issueDate = issueDate ? moment(issueDate).format(defaultData.WEB_DATE_FORMAT) : Constants.NA;

    return {
      name: contractualDetail[passportNumber]
        ? `${contractualDetail[passportNumber]} (${issueDate} - ${expiryDate})`
        : Constants.NA,
      pages: formattedData,
    };
  };

  const getEmploymentTypeLabel = (type) => {
    if (type === Common.INTERNAL_EMPLOYEE_API) return Common.INTERNAL_EMPLOYEE;
    if (type === Common.EXTERNAL_EMPLOYEE_API) return Common.EXTERNAL_EMPLOYEE;
    return type || Constants.NA;
  };

  const infoData = {
    [Common.PASSPORT_ID_LABEL]: formatPassportData("isPrimary", "passport"),
    [Common.SECOND_PASSPORT_ID_LABEL]: formatPassportData("isSecondary", "secondaryPassport"),
    [Common.NATIONAL_ID_LABEL]: contractualDetail?.nationalIdentificationNumber || Constants.NA,
    [Common.DRIVERS_LICENSE_LABEL]: profile?.drivingLicence
      ? formatDrivingLicence(profile?.drivingLicence?.[0])
      : "No",
    [Common.SEAMANS_BOOKLET_LABEL]:
      profile?.seamansBook?.length > 0 ? formatSeamansBook(profile?.seamansBook[0]) : Constants.NA,

    [Common.DATE_OF_BIRTH_LABEL]: contractualDetail?.birthDate
      ? moment(contractualDetail?.birthDate).format(defaultData.WEB_DATE_FORMAT)
      : Constants.NA,
    [Common.PLACE_OF_BIRTH_LABEL]: contractualDetail?.birthPlace || Constants.NA,
    [Common.EMPLOYMENT_TYPE_LABEL]: getEmploymentTypeLabel(contractualDetail?.employmentType),

    ...(contractualDetail?.employmentType === Common.SELF_EMPLOYED && {
      [Common.LIABILITY_INSURANCE_LABEL]:
        contractualDetail?.liabilityInsurance?.length > 0
          ? handleInsuranceInContractualDetails(
              contractualDetail?.liabilityInsurance?.[0],
              contractualDetail?.liabilityInsuranceId
            )
          : Constants.NA,
      [Common.HEALTH_INSURANCE_LABEL]:
        contractualDetail?.healthInsurance?.length > 0
          ? handleInsuranceInContractualDetails(
              contractualDetail?.healthInsurance?.[0],
              contractualDetail?.healthInsuranceId
            )
          : Constants.NA,
    }),

    ...(contractualDetail?.employmentType !== Common.INTERNAL_EMPLOYEE_API && {
      [Common.COMPANY_NAME_LABEL]: contractualDetail?.companyName || Constants.NA,
      [Common.COMPANY_REGISTRATION_NR_LABEL]:
        contractualDetail?.companyRegistrationNumber || Constants.NA,
      [Common.COMPANY_VAT_NR_LABEL]: contractualDetail?.companyVATNumber || Constants.NA,
      [Common.COMPANY_ADDRESS_LABEL]: contractualDetail?.companyAddress || Constants.NA,
    }),

    [Common.BANK_NAME_LABEL]: contractualDetail?.bankName || Constants.NA,
    [Common.ACCOUNT_HOLDER_LABEL]: contractualDetail?.accountHolderName || Constants.NA,
    [Common.BANK_ACCOUNT_NR_LABEL]: contractualDetail?.bankAccount || Constants.NA,
    [Common.BIC_SWIFT_LABEL]: contractualDetail?.bicSwift || Constants.NA,
  };

  return (
    <Box>
      <ProfileInfoCard
        key={profile?.[Constants.MONGOOSE_ID]}
        tabChangeValue={tabValue}
        title="Contractual Details"
        info={infoData}
        action={{ route: "", tooltip: "Edit Profile" }}
        shadow={false}
        showEditIcon={!!permission?.update}
        onEditClick={handleDrawerOpen}
      />

      <ContractualDetailsDrawer
        open={isDrawerOpen}
        onClose={handleDrawerClose}
        data={{
          ...contractualDetail,
          drivingLicence: profile?.drivingLicence,
          seamansBook: profile?.seamansBook,
        }}
        contractualDetailId={contractualDetail?.[Constants.MONGOOSE_ID]}
        userId={profile?.[Constants.MONGOOSE_ID]}
      />
    </Box>
  );
}

ContractualDetailsTab.propTypes = {
  profile: PropTypes.objectOf(PropTypes.any).isRequired,
  tabValue: PropTypes.number.isRequired,
  onUpdate: PropTypes.func,
};

ContractualDetailsTab.defaultProps = {
  onUpdate: null,
};

export default ContractualDetailsTab;

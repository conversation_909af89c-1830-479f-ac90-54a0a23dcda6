/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";

// MUI Components
import { IconButton } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import Author from "components/Table/Author";

// 3rd party library
import moment from "moment";

// Redux
import { useSelector } from "react-redux";

// Constants
import { Icons, defaultData } from "utils/Constants";

export default function ActivityData(
  handleOpenDeleteActivity,
  activityList,
  handleOpenEditActivty,
  startTime,
  handleDuration
) {
  const [rows, setRows] = useState([]);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[5]?.screensInfo?.agreement;
  const mongooseId = "_id";

  useEffect(() => {
    // sort activity list by end time
    const sortedAcivityList = activityList.sort(
      (a, b) => new Date(a.endTime) - new Date(b.endTime)
    );
    if (sortedAcivityList && permission?.read) {
      const list = sortedAcivityList.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          location: <Author name={item?.location?.title} />,
          activity: <Author name={item?.activity?.name} />,
          endTime: (
            <Author
              name={moment(item?.endTime).format(defaultData.WEB_24_HOURS_FORMAT_WITHOUT_SECOND)}
            />
          ),
          duration: (
            <Author
              name={
                index === 0
                  ? handleDuration([startTime?.split(".")[0], item?.endTime?.split(".")[0]])
                  : handleDuration([
                      sortedAcivityList[index - 1]?.endTime?.split(".")[0],
                      item?.endTime?.split(".")[0],
                    ])
              }
            />
          ),
          comments: <Author name={item?.comments} />,
          action: (
            <MDBox>
              {permission?.update && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  onClick={() => handleOpenEditActivty(item, "update")}
                >
                  {Icons.EDIT}
                </IconButton>
              )}
              &nbsp;
              {permission?.delete && (
                <IconButton
                  aria-label="fingerprint"
                  color="error"
                  onClick={() => handleOpenDeleteActivity(item[mongooseId])}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [activityList, ConfigData]);

  const activityColumns = [
    { Header: "No.", accessor: "srNo", width: "2%" },
    { Header: "Location", accessor: "location", align: "left", width: "18%" },
    { Header: "Activity", accessor: "activity", align: "left", width: "18%" },
    { Header: "End Time", accessor: "endTime", align: "left", width: "14%" },
    { Header: "Duration", accessor: "duration", align: "left", width: "7%" },
    { Header: "Descriptions", accessor: "comments", align: "left" },
  ];
  if (permission?.update || permission?.delete) {
    activityColumns.push({ Header: "Action", accessor: "action", width: "10%", align: "left" });
  }

  const tableData = {
    activityColumns,
    activityRows: rows,
  };

  return tableData;
}

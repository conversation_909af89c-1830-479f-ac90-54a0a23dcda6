import Constants, { DropdownOptions, Common } from "utils/Constants";

// Remove key for values
export function filterObj(obj, values) {
  const newObj = { ...obj };
  Object.keys(newObj).forEach((key) => {
    if (newObj[key] === values) {
      delete newObj[key];
    }
  });

  return newObj;
}

// param creater from object
export function paramCreater(obj) {
  // Don't change this as many component depends on this.
  const filteredObject = filterObj(obj, "");
  const param = new URLSearchParams(filteredObject);
  return param;
}

export function generateCertificateList(data) {
  const certificateList = [];

  data?.forEach((item) => {
    const functionObject = {
      id: item?.function[Constants.MONGOOSE_ID],
      functionName: item?.function?.functionName,
    };

    item?.certificates?.forEach((certificate) => {
      const certificateObject = {
        id: certificate[Constants.MONGOOSE_ID],
        name: certificate?.name,
      };

      certificateList.push({
        function: functionObject,
        certificate: certificateObject,
      });
    });
  });

  return certificateList;
}

export function generateTrainingMatrixCertificateList(data) {
  const certificateList = [];

  data?.forEach((element) => {
    const key = Object.keys(element)[0];
    const value = Object.values(element)[0];
    const functionObject = {
      id: value.id,
      functionName: key,
    };

    if (Array.isArray(value.data)) {
      value.data.forEach((user) => {
        const userCertificates = {
          id: user[Constants.MONGOOSE_ID],
          name: user.name,
          certificates: user.userCertificates,
        };

        certificateList.push({
          function: functionObject,
          certificate: userCertificates,
        });
      });
    }
  });

  return certificateList;
}

const getFileNameFromUrl = (url, name) => {
  const parts = url.split("/");
  const fileName = parts[parts.length - 1];
  return name ? `${name}` : fileName;
};

export const downloadFile = (imageUrl, event, name) => {
  event.stopPropagation();
  return fetch(imageUrl)
    .then((response) => response.blob())
    .then((blob) => {
      const fileName = getFileNameFromUrl(imageUrl, name);
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);
    });
};

export const numericFieldValidation = (e) => {
  if (e.key === "Backspace" || e.key === "ArrowLeft" || e.key === "ArrowRight") {
    return;
  }

  // Prevent default for non-numeric keys
  if (!/(?=.*?\d)/.test(e.key)) {
    e.preventDefault();
  }
};

export function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export const handlePdfExport = async (fileName, res, dateFormat = true) => {
  let name = fileName;
  if (dateFormat) {
    const currentDate = new Date();
    name = `Reynard_${fileName}_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.pdf`;
  }
  if (res.error !== undefined) return res;

  const url = window.URL.createObjectURL(res.payload);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", name);
  document.body.appendChild(link);
  link.click();
  link.remove();
  return res;
};

/**
 * @param {number} currentPage - The current page number.
 * @param {number} perPage - Number of records per page.
 * @param {number} totalRecords - Total number of records
 * (If the totalRecords is not actual record count + 1 then,
 *  you should be passed as actual record count + 1).
 * @returns {number} - The updated page number.
 */
export const findPageNumberAfterRecordDelete = (currentPage, perPage, totalRecords) => {
  try {
    const divValue = Math.floor(totalRecords / perPage);
    const modValue = totalRecords % perPage;

    let newPage = currentPage;
    if (modValue === 1 && divValue === currentPage && currentPage !== 0) {
      newPage -= 1;
    }
    return newPage;
  } catch (error) {
    return currentPage;
  }
};

export const handleEmptyRequestBody = (reqData) => {
  const updatedAsset = Object.fromEntries(
    Object.entries(reqData.asset).map(([key, value]) => [key, value === "" ? null : value])
  );

  return updatedAsset;
};

export const filterProjectStatusFunc = () => {
  const filterList = JSON.parse(localStorage.getItem(Common.SELECTED_PROJECT_STATUS_LIST)) || [];

  if (
    filterList.length === 0 ||
    filterList.length === DropdownOptions.PROJECT_STATUS_OPTIONS.length
  ) {
    return "";

    // When no status is selected or all status is selected sending empty string.
    // Which then will be removed in next step, so it will not be added to URL params
  }
  return filterList.join(",");
};

export const normalizeParamsAndAddValues = (params = {}, keyValueObj = {}) => {
  let queryObject = {};

  // Normalizing params to an object if it's a URLSearchParams instance
  if (params instanceof URLSearchParams) {
    queryObject = Object.fromEntries(params.entries());
  } else if (typeof params === "object" && params !== null) {
    queryObject = { ...params };
  }

  const updatedParams = { ...queryObject, ...keyValueObj };
  const newParams = paramCreater(updatedParams);
  return newParams;
};

export const getVersion = (version) => {
  if (version === 1) {
    return "v1";
  }
  const newVersion = version.split("v")[1];
  return `v${parseInt(newVersion, 10) + 1}`;
};

export const formatKeyName = (key) =>
  key
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Convert camelCase to space-separated
    .replace(/_/g, " ") // Replace underscores with spaces
    .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word

// Func for Calling and First Name
export const getFormattedCallingFirstName = (createdBy) => {
  // This func will receive an object which directly containe the name
  if (!createdBy) return Constants.NA;

  const firstName = createdBy?.callingName || createdBy?.firstName || "";
  const lastName = createdBy?.lastName || "";

  const fullName = `${firstName} ${lastName}`.trim();

  return fullName || Constants.NA;
};

export const formatTime = (time) => {
  const [hour, minute] = time.split(":");
  return `${hour.padStart(2, "0")}:${minute}`;
};

export const checkArrayType = (arr) =>
  arr?.length && typeof arr[0] === "object" && !Array?.isArray(arr[0]);

export const getFormattedProjectName = (project) => {
  const activeProject = project;
  if (!activeProject) return Constants.NA;

  const projectNumber = activeProject.projectNumber || "";
  const projectName = activeProject.title || "";

  if (!projectNumber && !projectName) return Constants.NA;
  if (!projectNumber) return projectName;
  if (!projectName) return projectNumber;

  return `${projectNumber} - ${projectName}`;
};

export const generateTimeIntervals = (length = 20, intervalMinutes = 15) =>
  Array.from({ length }, (_, i) => {
    const totalMinutes = (i + 1) * intervalMinutes;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    let title = "";

    if (hours > 0 && minutes > 0) {
      title = `${hours}hr ${minutes}min`;
    } else if (hours > 0) {
      title = `${hours}hr`;
    } else {
      title = `${minutes}min`;
    }

    return {
      id: (i + 1).toString(),
      title,
    };
  });

export const formattedTitle = (title) =>
  title
    ?.split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

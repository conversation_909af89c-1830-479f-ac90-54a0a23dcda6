import pxToRem from "assets/theme/functions/pxToRem";

const ModalBreakPoint = {
  baseTitleBreakPoint: {
    xs: pxToRem(18),
    sm: pxToRem(18),
    md: pxToRem(20),
    lg: pxToRem(20),
    xl: pxToRem(20),
    xxl: pxToRem(20),
    fhd: pxToRem(22),
    uhd: pxToRem(24),
  },
  extraSmallTitleBreakPoint: {
    xs: pxToRem(12),
    sm: pxToRem(12),
    md: pxToRem(12),
    lg: pxToRem(12),
    xl: pxToRem(12),
    xxl: pxToRem(12),
    fhd: pxToRem(14),
    uhd: pxToRem(15),
  },
};

export default ModalBreakPoint;

import React from "react";

// 3rd party library
import PropTypes from "prop-types";

// Custom components
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts"; // Import FontComponent

// Styles
import PageTitle from "../style/styles";

function PageTitleComponent({ title }) {
  const fontSize = FontComponent({ sizes: {} }); // Get the dynamic font size

  return (
    <MDTypography
      sx={(theme) => ({
        color: "#101828",
        textTransform: "capitalize",
        fontSize, // Apply dynamic font size
        ...PageTitle(theme),
      })}
    >
      {title}
    </MDTypography>
  );
}

PageTitleComponent.propTypes = {
  title: PropTypes.string,
};

PageTitleComponent.defaultProps = {
  title: "",
};

export default PageTitleComponent;

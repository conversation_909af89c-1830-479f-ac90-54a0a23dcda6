import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import ApiService from "../ApiService/ApiService";

const getAllQHSEDocuments = createAsyncThunk("qhse-documents/list", async (params) => {
  const res = await ApiService.get(`general-qhsc-documents?${params}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((error) => error.response);
  return res;
});

export const createQHSEDocument = createAsyncThunk("qhse-documents/create", async (body) => {
  const res = await ApiService.post(
    `general-qhsc-documents`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateQHSEDocument = createAsyncThunk("qhse-documents/update", async (body) => {
  const res = await ApiService.patch(
    `general-qhsc-documents/${body.id}`,
    { ...body.body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const deleteQHSEDocument = createAsyncThunk("qhse-documents/delete", async (id) => {
  const res = await ApiService.delete(`general-qhsc-documents/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getQHSEDocumentById = createAsyncThunk("qhse-documents/getById", async (id) => {
  const res = await ApiService.get(`general-qhsc-documents/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((error) => error.response);
  return res;
});

export default getAllQHSEDocuments;

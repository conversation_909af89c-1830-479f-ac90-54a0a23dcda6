import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import { useLocation, useParams } from "react-router-dom";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import FormTextArea from "components/Form/FTextArea";

// Constants
import Constants, { defaultData, Common } from "utils/Constants";

// Thunk
import { getQHSEDprData } from "redux/Thunks/Dpr";

// Slice
import { setQhseRemarks, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";
import { openSnackbar } from "redux/Slice/Notification";

// Data
import QhseDPRData from "../data/qhseData";

function QhseTab() {
  const dispatch = useDispatch();

  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { id } = useParams();

  const { dprData, loading, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  // API calls starts here
  const getQHSEData = async () => {
    const response = await dispatch(getQHSEDprData(id));

    // Display Error message if API call fails
    if (!response?.payload?.data) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const { qhseColumns, qhseRows } = QhseDPRData(dprData?.qhse?.qhseSummary || []);

  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getQHSEData();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  useEffect(() => {
    if (displayedDprTabsObj?.qhseTab > 0) {
      getQHSEData()
        .then(() => dispatch(updateIsLatestDataApiCompleted(true)))
        .catch(() => dispatch(updateIsLatestDataApiCompleted(true)));
    }
  }, [displayedDprTabsObj?.qhseTab]);

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            QHSE
          </MDTypography>
        </MDBox>
        <MDBox>
          <Card>
            <MDBox>
              <DataTable
                table={{ columns: qhseColumns, rows: qhseRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={loading || Constants.FULFILLED}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.qhse?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setQhseRemarks(e.target.value));
            }}
          />
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default QhseTab;

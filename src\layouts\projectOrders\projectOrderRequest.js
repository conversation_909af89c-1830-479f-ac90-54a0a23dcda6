import React, { useEffect } from "react";

// Common Components
import MDBox from "components/MDBox";

// Data Table
import DataTable from "examples/Tables/DataTable";
import ProjectOrderData from "layouts/projectOrders/data/projectOrderData";

// Redux
import { useDispatch, useSelector } from "react-redux";
import projectOrderRequestListing, {
  projectOrderShoppingListing,
} from "redux/Thunks/EquipmentRequest";
import { reloadData, storeCurrentStatus, reloadShoppingData } from "redux/Slice/EquipmentRequest";

// Utils
import Constants, { defaultData, FiltersModuleName, Common } from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

function projectOrderRequest({ filters, setFilters, shouldUpdateState }) {
  const dispatch = useDispatch();
  const {
    list = [],
    loading,
    shoppingList = [],
    shoppingLoading,
  } = useSelector((state) => state.equipmentRequest);

  const createParamObj = () => {
    const paramData = {
      search: filters[0].selectedValue,
      project: filters[1].selectedValue,
      status: filters[2].selectedValue,
    };

    Object.keys(paramData).forEach((key) => {
      if (paramData[key] === "" || paramData[key] === "all" || paramData[key] === null) {
        delete paramData[key];
      }
    });

    return paramData;
  };

  const filtersSearchLoadingFunc = () => {
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === FiltersModuleName.SEARCH) {
          return {
            ...filter,
            isLoading: false,
          };
        }
        return filter;
      });
      return updatedFilters;
    });
  };

  // Function to get listing of project orders requests
  const getRequestListing = async () => {
    const params = createParamObj();

    await dispatch(reloadData());
    await dispatch(storeCurrentStatus(params.status));
    const res = await dispatch(projectOrderRequestListing(paramCreater(params)));
    if (res?.payload?.status === Common.API_STATUS_200) {
      filtersSearchLoadingFunc();
    }
  };

  const getShoppingListing = async () => {
    const params = createParamObj();

    await dispatch(reloadShoppingData());
    await dispatch(storeCurrentStatus(params.status));
    const res = await dispatch(projectOrderShoppingListing(paramCreater(params)));
    if (res?.payload?.status === Common.API_STATUS_200) {
      filtersSearchLoadingFunc();
    }
  };

  useEffect(() => {
    if (filters[2]?.selectedValue === Constants.QUEUE) {
      getShoppingListing();
    } else {
      getRequestListing();
    }
  }, [shouldUpdateState]);

  const { projectColumns, projectrows } = ProjectOrderData({
    dataList: filters[2]?.selectedValue === Constants.QUEUE ? shoppingList : list,
    status: filters[2]?.selectedValue,
  });

  return (
    <MDBox mt={3} mb={5}>
      <DataTable
        table={{ columns: projectColumns, rows: projectrows }}
        isSorted={false}
        entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
        showTotalEntries={false}
        pagination={{ variant: "gradient", color: "info" }}
        loading={filters[2]?.selectedValue === Constants.QUEUE ? shoppingLoading : loading}
        licenseRequired
      />
    </MDBox>
  );
}

export default projectOrderRequest;

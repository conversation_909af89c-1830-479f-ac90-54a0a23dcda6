import { useEffect, useState } from "react";

// Material Components
import { IconButton, Popover, Typography } from "@mui/material";

// Common Components
import Status from "components/Table/Status";
import Author from "components/Table/Author";
import MDBox from "components/MDBox";

// Redux
import { storeReportId } from "redux/Slice/Report";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";

// Utils
import Constants, { Icons, defaultData, Common } from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";

// 3rd party library
import PropTypes from "prop-types";

// Methods
import { getFormattedProjectName } from "utils/methods/methods";

export function ReportStatus({ statusList }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const checkPriorityStatus = (status) => {
    if (status.includes("checked")) return "checked";
    if (status.includes("in-discussion")) return "in-discussion";
    if (status.includes("closed")) return "closed";
    if (status.includes("submitted")) return "submitted";
    return "open";
  };

  const mainStatus = checkPriorityStatus(statusList);

  return (
    <MDBox display="flex" alignItems="center">
      <Status title={mainStatus.replace("-", " ")} />
      {!statusList.every((status) => status.includes(mainStatus)) && (
        <IconButton aria-label="report-info-status" color="info" onClick={handleClick}>
          {Icons.INFO}
        </IconButton>
      )}
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <Typography sx={{ p: 1, backgroundColor: "#f5f5f5", fontSize: pxToRem(16) }}>
          Open: {statusList.filter((status) => status.includes("open")).length}
        </Typography>
        <Typography sx={{ p: 1, backgroundColor: "#f5f5f5", fontSize: pxToRem(16) }}>
          Submitted: {statusList.filter((status) => status.includes("submitted")).length}
        </Typography>
        <Typography sx={{ p: 1, backgroundColor: "#f5f5f5", fontSize: pxToRem(16) }}>
          Closed: {statusList.filter((status) => status.includes("closed")).length}
        </Typography>
        <Typography sx={{ p: 1, backgroundColor: "#f5f5f5", fontSize: pxToRem(16) }}>
          Checked: {statusList.filter((status) => status.includes("checked")).length}
        </Typography>
        <Typography sx={{ p: 1, backgroundColor: "#f5f5f5", fontSize: pxToRem(16) }}>
          In Discussion: {statusList.filter((status) => status.includes("in-discussion")).length}
        </Typography>
      </Popover>
    </MDBox>
  );
}

ReportStatus.propTypes = {
  statusList: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default function ReportData(ReportDetailsList, permission) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [rows, setRows] = useState([]);

  const handleView = async (data) => {
    const userReportIds = data?.id;
    const userProjectReportId = data?.userProjectReport;
    await dispatch(storeReportId(userReportIds));
    navigate(`/client/report/report-details/${userProjectReportId}`, {
      state: { reportId: userReportIds, fetchData: true, type: "reportDetail" },
    });
  };

  useEffect(() => {
    if (ReportDetailsList && permission?.read) {
      const list = ReportDetailsList?.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          project: (
            <Author
              name={getFormattedProjectName(item?.project)}
              maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
              style={{ textTransform: "none" }}
            />
          ),
          reportTitle: (
            <Author
              style={{ textTransform: "none" }}
              name={item?.report?.title}
              maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
            />
          ),
          location: (
            <Author
              style={{ textTransform: "none" }}
              name={
                item?.location?.deletedBy
                  ? `(${Common.DELETED}) ${item?.location?.title}`
                  : item?.location?.title
              }
              maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
            />
          ),
          assets:
            item.asset?.length > 0 ? (
              item.asset.map((aname, i) => (
                <Author
                  style={{ textTransform: "none" }}
                  key={aname[Constants.MONGOOSE_ID]}
                  name={`${aname.cableName} ${i < item.asset.length - 1 ? "," : ""}`}
                />
              ))
            ) : (
              <Author name={Constants.NA} />
            ),
          totalReports: <Author name={item?.id?.length} />,
          progress: <Author name={`${item?.totalAnsweredQuestions} / ${item?.totalQuestions}`} />,
          status: <ReportStatus statusList={item?.status} />,
          action: (
            <MDBox>
              <IconButton
                aria-label="report-parameter"
                color="info"
                onClick={() => handleView(item)}
              >
                {Icons.VIEW}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [ReportDetailsList, permission]);
  return {
    columns: [
      { Header: "No.", accessor: "srNo", width: "3%" },
      { Header: "Project", accessor: "project", align: "left", width: "25%" },
      { Header: "Title", accessor: "reportTitle", align: "left", width: "25%" },
      { Header: "Location", accessor: "location", align: "left", width: "25%" },
      { Header: "Assets", accessor: "assets", width: "13%", align: "left" },
      { Header: "Total Report", accessor: "totalReports", align: "center" },
      { Header: "Progress", accessor: "progress", align: "center" },
      { Header: "Status", accessor: "status", align: "left" },
      { Header: "Action", accessor: "action", width: "13%", align: "center" },
    ],
    rows,
  };
}

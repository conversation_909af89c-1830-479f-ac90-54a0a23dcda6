import React, { useMemo } from "react";

// 3rd Party libraries
import { IconButton } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";

// Utils
import { Icons } from "utils/Constants";
import { getFormattedCallingFirstName } from "utils/methods/methods";

export default function ProjectOrderHistoryOrderByData({ dataList = [] }) {
  const navigate = useNavigate();

  const location = useLocation();
  const { projectId = null } = location.state;

  const navigateToAnotherPageFunc = (orderNumber) => {
    navigate("/client/project-orders/orderby/details", {
      state: { projectId, orderNumber, projectName: location?.state?.projectName },
    });
  };

  const projectHistoryByIdRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      orderNumber: <Author name={item?.orderNumber} />,
      title: <Author name={item?.shoppingCartTitle} />,
      orderBy: <Author name={getFormattedCallingFirstName(item?.orderBy)} />,
      totalItems: <Author name={item.totalApprovedItems} />,
      totalRequestedQuantity: <Author name={item.totalRequestedQuantity} />,
      action: (
        <MDBox>
          <IconButton
            aria-label="view"
            color="error"
            onClick={() => navigateToAnotherPageFunc(item?.orderNumber)}
          >
            {Icons.VIEW}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList]);

  const projectHistoryByIdColumns = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Order No.", accessor: "orderNumber" },
    { Header: "Title", accessor: "title" },
    { Header: "Order By", accessor: "orderBy" },
    { Header: "Total Items", accessor: "totalItems", align: "center" },
    { Header: "Total Requested Qty", accessor: "totalRequestedQuantity", align: "center" },
    { Header: "Action", accessor: "action" },
  ];

  const tableData = {
    projectHistoryByIdColumns,
    projectHistoryByIdRows,
  };

  return tableData;
}

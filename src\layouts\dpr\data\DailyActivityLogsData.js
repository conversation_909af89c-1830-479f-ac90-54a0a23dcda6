import { useEffect, useState } from "react";

// Common components
import Author from "components/Table/Author";
import moment from "moment";
import MDTypography from "components/MDTypography";

// Utils
import { Colors, defaultData } from "utils/Constants";
import { formatTime } from "utils/methods/methods";

export default function DailyActivityLogs(dailyActivityLogs) {
  const [dailyActivityRows, setDailyActivityRows] = useState([]);

  useEffect(() => {
    if (dailyActivityLogs) {
      const list = dailyActivityLogs.flatMap((logs) => [
        {
          no: <Author name="" cellColor={Colors.TABLE_ROW_COLOR} />,
          startTime: (
            <MDTypography cellColor={Colors.TABLE_ROW_COLOR}>
              <Author
                name={logs?.team}
                style={{ fontWeight: "bold", textTransform: "capitalize", fontSize: "20px" }}
              />
            </MDTypography>
          ),
          endTime: <Author name="" cellColor={Colors.TABLE_ROW_COLOR} />,
          manHours: (
            <MDTypography cellColor={Colors.TABLE_ROW_COLOR}>
              <Author
                name={formatTime(logs?.totalManHours) || "00:00"}
                style={{ fontWeight: "bold", textTransform: "capitalize", fontSize: "20px" }}
              />
            </MDTypography>
          ),
          teamHours: (
            <MDTypography cellColor={Colors.TABLE_ROW_COLOR}>
              <Author
                name={formatTime(logs?.totalTeamHours) || "00:00"}
                style={{ fontWeight: "bold", textTransform: "capitalize", fontSize: "20px" }}
              />
            </MDTypography>
          ),
          activity: (
            <MDTypography cellColor={Colors.TABLE_ROW_COLOR}>
              <Author
                name={`${logs?.teamSize} Members`}
                style={{ fontWeight: "bold", textTransform: "capitalize", fontSize: "20px" }}
              />
            </MDTypography>
          ),
          location: <Author name="" cellColor={Colors.TABLE_ROW_COLOR} />,
          remarks: <Author name="" cellColor={Colors.TABLE_ROW_COLOR} />,
        },
        ...logs.shiftActivities.map((item, subIndex) => ({
          no: <Author name={`${subIndex + 1}`} />,
          startTime: <Author name={moment(item?.startTime).format(defaultData.TIME_FORMAT)} />,
          endTime: <Author name={moment(item?.endTime).format(defaultData.TIME_FORMAT)} />,
          manHours: <Author name={formatTime(item?.manHours)} />,
          teamHours: <Author name={formatTime(item?.teamHours)} />,
          activity: <Author name={item?.activityName} />,
          location: <Author name={item?.location} />,
          remarks: <Author name={item?.remarks} />,
        })),
      ]);

      setDailyActivityRows([...list]);
    }
  }, [dailyActivityLogs]);

  return {
    dailyActivityLogColumns: [
      { Header: "No.", accessor: "no", width: "5%" },
      { Header: "Start Time", accessor: "startTime", width: "13%" },
      { Header: "End Time", accessor: "endTime", align: "left", width: "5%" },
      { Header: "Man Hours", accessor: "manHours", align: "left", width: "5%" },
      { Header: "Team Hours", accessor: "teamHours", align: "left", width: "5%" },
      { Header: "Activity", accessor: "activity", align: "left", width: "20%" },
      { Header: "Location", accessor: "location", align: "left", width: "12%" },
      { Header: "Remarks", accessor: "remarks", align: "left", width: "50%" },
    ],
    dailyActivityRows,
  };
}

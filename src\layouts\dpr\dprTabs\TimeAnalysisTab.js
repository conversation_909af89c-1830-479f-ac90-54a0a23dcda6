import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import AnalysisDataTable from "examples/Tables/DataTable/DetailTimeAnalysisTable";
import FormTextArea from "components/Form/FTextArea";
import DetailTimeAnalysisTable from "layouts/dpr/data/detailedTimeAnalysisData";
import DailyActivityLogsData from "layouts/dpr/data/DailyActivityLogsData";

// Constants
import Constants, { defaultData, Common } from "utils/Constants";

// Thunk
import { getDetailedTimeAnalysisData, getdailyActivityLogsData } from "redux/Thunks/Dpr";

// Slice
import { setTimeAnalysisRemarks, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";
import { openSnackbar } from "redux/Slice/Notification";

function TimeAnalysisTab() {
  const dispatch = useDispatch();
  const { id } = useParams();

  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { dprData, loading, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  const getDetailedTimeAnalysis = async () => {
    try {
      // API call to get detailed time analysis
      await dispatch(getDetailedTimeAnalysisData(id));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const getDailyActivityLogs = async () => {
    try {
      // API call to get detailed time analysis
      await dispatch(getdailyActivityLogsData(id));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const { timeAnalysisColumns, timeAnalysisRows } = DetailTimeAnalysisTable(
    dprData?.timeAnalysis?.detailedTimeAnalysis
  );

  const { dailyActivityLogColumns, dailyActivityRows } = DailyActivityLogsData(
    dprData?.timeAnalysis?.dailyActivityLog
  );

  const fetchAllLatestData = async () => {
    try {
      await Promise.all([getDetailedTimeAnalysis(), getDailyActivityLogs()]);
      dispatch(updateIsLatestDataApiCompleted(true)); // Set true only if all APIs succeed
    } catch (error) {
      dispatch(updateIsLatestDataApiCompleted(false)); // In case of error, keep it false
    }
  };

  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getDetailedTimeAnalysis();
      getDailyActivityLogs();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  useEffect(() => {
    if (displayedDprTabsObj?.timeAnalysisTab > 0) {
      fetchAllLatestData();
    }
  }, [displayedDprTabsObj?.timeAnalysisTab]);

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Detailed Time Analysis
          </MDTypography>
        </MDBox>
        <MDBox>
          <Card>
            <MDBox>
              <AnalysisDataTable
                table={{ columns: timeAnalysisColumns, rows: timeAnalysisRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={loading || Constants.FULFILLED}
                licenseRequired
                analystData={dprData?.timeAnalysis?.detailedTimeAnalysis}
              />
            </MDBox>
          </Card>
        </MDBox>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>
          <FormTextArea
            value={dprData?.timeAnalysis?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setTimeAnalysisRemarks(e.target.value));
            }}
          />
        </MDBox>
      </Card>
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Daily Activity Logs
          </MDTypography>
        </MDBox>

        <MDBox mb={2}>
          <Card>
            <MDBox>
              <AnalysisDataTable
                table={{ columns: dailyActivityLogColumns, rows: dailyActivityRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={Constants.FULFILLED}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default TimeAnalysisTab;

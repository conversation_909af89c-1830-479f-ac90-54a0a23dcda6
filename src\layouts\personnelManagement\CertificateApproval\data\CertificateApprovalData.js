/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";

// Custom components
import MDAvatar from "components/MDAvatar";
import MDBox from "components/MDBox";
import Author from "components/Table/Author";

// MUI Components
import { IconButton } from "@mui/material";

// Constants
import Constants, { Icons, defaultData } from "utils/Constants";

// 3rd party library
import moment from "moment";
import { useSelector } from "react-redux";

export default function CertificateApproval(
  userList,
  handleCertificateOpen,
  handleCertificateView
) {
  const [rows, setRows] = useState([]);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[16]?.screensInfo?.agreement;

  useEffect(() => {
    if (userList) {
      const tempRows = userList?.map((element) => {
        const temp = {
          name: (
            <MDBox display="flex" alignItems="center" gap={1}>
              <MDAvatar
                size="xs"
                bgColor="primary"
                fontSize={2}
                alt="User Avatar"
                src={element?.user?.profileImage}
              />
              <MDBox
                style={{
                  flex: 1,
                  width: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                <Author
                  name={`${
                    element?.user?.callingName
                      ? element?.user?.callingName
                      : element?.user?.firstName
                  } ${element?.user?.lastName}`}
                  style={{ color: "#757575", fontSize: "14px" }}
                />
              </MDBox>
            </MDBox>
          ),
          email: <Author name={element?.user?.email} style={{ textTransform: "inherit" }} />,
          certificates: (
            <MDBox display="flex" justifyContent="space-between" alignItems="center">
              <MDBox
                style={{
                  flex: 1,
                  width: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                <Author name={element?.certificateType?.name} maxContent={12} />
              </MDBox>
              <IconButton
                aria-label="Edit Safety Card"
                color="info"
                onClick={() =>
                  handleCertificateView({
                    url: element?.link,
                    name: element?.name,
                    uploader: `${
                      element?.user?.callingName
                        ? element?.user?.callingName
                        : element?.user?.firstName
                    } ${element?.user?.lastName}`,
                    startDate: element?.startDate,
                    endDate: element?.endDate,
                  })
                }
              >
                {Icons.VIEW}
              </IconButton>
            </MDBox>
          ),
          fromDate: (
            <Author
              name={
                element?.startDate
                  ? moment(element?.startDate).format(defaultData.WEB_DATE_FORMAT)
                  : Constants.NA
              }
            />
          ),
          toDate: (
            <Author
              name={
                element?.endDate
                  ? moment(element?.endDate).format(defaultData.WEB_DATE_FORMAT)
                  : Constants.NA
              }
            />
          ),
          action: (
            <MDBox justifyContent="center">
              <IconButton
                onClick={() => handleCertificateOpen("rejected", element[Constants.MONGOOSE_ID])}
              >
                {Icons.CLOSE_OUTLINE}
              </IconButton>
              <IconButton
                onClick={() => handleCertificateOpen("approved", element[Constants.MONGOOSE_ID])}
              >
                {Icons.CORRECT_OUTLINE}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...tempRows]);
    }
  }, [userList]);
  return {
    columns: [
      {
        Header: "Name",
        accessor: "name",
      },
      {
        Header: "Email",
        accessor: "email",
      },
      {
        Header: "Certificates",
        accessor: "certificates",
      },
      {
        Header: "Start Date",
        accessor: "fromDate",
      },
      {
        Header: "End Date",
        accessor: "toDate",
      },
      ...(permission?.update
        ? [
            {
              Header: "Action",
              accessor: "action",
              align: "center",
            },
          ]
        : []),
    ],
    rows,
  };
}

import React, { useMemo, useEffect, useState, Fragment } from "react";
import PropTypes from "prop-types";
import { useTable, usePagination, useGlobalFilter, useAsyncDebounce, useSortBy } from "react-table";

import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDInput from "components/MDInput";

import Constants, { Icons, defaultData } from "utils/Constants";

import { useSelector } from "react-redux";
import Session from "utils/Sessions";
import jwtDecode from "jwt-decode";

import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";

import DataTableHeadCell from "examples/Tables/DataTable/DataTableHeadCell";
import DataTableBodyCell from "examples/Tables/DataTable/DataTableBodyCell";
import { Autocomplete, Pagination, TableCell } from "@mui/material";
import Author from "components/Table/Author";

function DataTable({
  entriesPerPage,
  canSearch,
  showTotalEntries,
  table,
  isSorted,
  noEndBorder,
  loading,
  licenseRequired,
  // currentPage, handleTablePagination, handleCurrentPage are use when need data size is more
  // Use this parameter to retrieve additional data when a user visits the last page
  currentPage,
  handleTablePagination,
  handleCurrentPage,
  backgroundColor, // New prop to specify the background color
  textColor,
  extraContent,
  groupedData,
  keyField,
  keyFieldHeader,
}) {
  const defaultValue = entriesPerPage.defaultValue ? entriesPerPage.defaultValue : 10;
  const entries = entriesPerPage.entries
    ? entriesPerPage.entries.map((el) => el.toString())
    : ["25"];
  const [status, setStatus] = useState(Constants.PENDING);
  const ConfigData = useSelector((state) => state.config);
  const columns = useMemo(() => table.columns, [table]);
  const data1 = useMemo(() => table.rows, [table]);
  const tableInstance = useTable(
    { columns, data: data1, initialState: { pageIndex: currentPage } },
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const {
    getTableProps,
    headerGroups,
    prepareRow,
    rows,
    page,
    pageOptions,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    setGlobalFilter,
    state: { pageIndex, pageSize, globalFilter },
  } = tableInstance;

  // Set the default value for the entries per page when component mounts
  useEffect(() => setPageSize(defaultValue || 10), [defaultValue]);

  // update the status on role change, when superadmin login as admin and data changes
  useEffect(() => {
    const token = jwtDecode(Session.userToken);
    const { role } = token;

    const setPending = () => setStatus(Constants.PENDING);
    const setRejected = () => setStatus(Constants.REJECTED);
    const setFulfilled = () => setStatus(Constants.FULFILLED);
    const setNoData = () => setStatus("noData");

    const isSuperAdmin =
      role === defaultData.SUPER_ADMIN_ROLE && !Session.isSuperAdminViewingAdminPanel;
    const isPending = ConfigData.loading === Constants.PENDING || loading === Constants.PENDING;
    const isRejected = ConfigData.loading === Constants.REJECTED || loading === Constants.REJECTED;
    const isFulfilledWithData =
      loading === Constants.FULFILLED && rows.length > 0 && pageOptions.length > 0;
    const isFulfilledNoData =
      loading === Constants.FULFILLED && rows.length === 0 && pageOptions.length === 0;
    const isConfigFulfilled = ConfigData.loading === "fulfilled";

    // for superadmin
    if (isSuperAdmin || !licenseRequired) {
      if (loading === Constants.PENDING) setPending();
      else if (isRejected) setRejected();
      else if (isFulfilledWithData) setFulfilled();
      else if (isFulfilledNoData) setNoData();
    }
    // for admin
    else if (isPending) setPending();
    else if (isRejected) setRejected();
    else if (isConfigFulfilled && isFulfilledWithData) setFulfilled();
    else if (isConfigFulfilled && isFulfilledNoData) setNoData();
  }, [Session.userToken, Session.isSuperAdminViewingAdminPanel, ConfigData.loading, table]);
  // set current page to last page when the current page has no data
  // Works when some data is deleted from the last page
  useEffect(() => {
    if (pageOptions.length > 0 && pageOptions.length <= currentPage) {
      const lastPage = pageOptions.length - 1;
      gotoPage(lastPage);
      handleCurrentPage(lastPage);
    }
  }, [pageOptions]);
  // Set the entries per page value based on the select value
  const setEntriesPerPage = (value) => setPageSize(value);

  // Search input value state
  const [search, setSearch] = useState(globalFilter);

  // Search input state handle
  const onSearchChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 100);

  // A function that sets the sorted value for the table
  const setSortedValue = (column) => {
    let sortedValue;

    if (isSorted && column.isSorted) {
      sortedValue = column.isSortedDesc ? "desc" : "asce";
    } else if (isSorted) {
      sortedValue = "none";
    } else {
      sortedValue = false;
    }

    return sortedValue;
  };

  const handlePageChange = (event, value) => {
    if (pageOptions.length === value) handleTablePagination(value);
    if (event.target.innerText === "Previous") {
      previousPage();
    } else if (event.target.innerText === "Next") {
      nextPage();
    } else {
      gotoPage(value - 1);
      handleCurrentPage(value - 1);
    }
  };
  const functionPagination = page.map((item) => item?.values[keyField]?.props?.name);

  return (
    <MDBox
      sx={{
        backgroundColor: "White",
        borderRadius: "10px",
        border: "1px solid #E0E6F5",
      }}
      id="table-grouped-rows"
    >
      <TableContainer sx={{ boxShadow: "none" }}>
        {entriesPerPage.entries || canSearch ? (
          <MDBox
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            p={3}
            backgroundColor={backgroundColor}
            color={textColor}
          >
            {entriesPerPage && (
              <MDBox display="flex" alignItems="center">
                <Autocomplete
                  disableClearable
                  value={pageSize.toString()}
                  options={entries}
                  onChange={(event, newValue) => {
                    setEntriesPerPage(parseInt(newValue, 10));
                  }}
                  size="small"
                  sx={{ width: "5rem" }}
                  renderInput={(params) => <MDInput {...params} />}
                />
                <MDTypography variant="caption" color="secondary">
                  &nbsp;&nbsp;entries per page
                </MDTypography>
              </MDBox>
            )}
            {canSearch && (
              <MDBox width="12rem" ml="auto">
                <MDInput
                  placeholder="Search..."
                  value={search}
                  size="small"
                  fullWidth
                  onChange={({ currentTarget }) => {
                    setSearch(search);
                    onSearchChange(currentTarget.value);
                  }}
                />
              </MDBox>
            )}
          </MDBox>
        ) : null}
        <Table {...getTableProps()}>
          <MDBox component="thead">
            {headerGroups?.map((headerGroup) =>
              page.length > 0 ? (
                <TableRow>
                  {headerGroup?.headers?.map(
                    (column) =>
                      column?.Header === keyFieldHeader && (
                        <DataTableHeadCell
                          {...column.getHeaderProps(isSorted && column.getSortByToggleProps())}
                          width={column?.width ? column?.width : "auto"}
                          align={column?.align ? column?.align : "left"}
                          sorted={setSortedValue(column)}
                          backgroundColor={backgroundColor}
                          textColor={textColor}
                          isSticky={column?.isSticky}
                          index={column?.index}
                        >
                          <MDBox
                            style={{
                              width: column?.width ? column?.width : "auto",
                              color: textColor !== "" ? textColor : "white",
                            }}
                          >
                            {column.render("Header")}
                          </MDBox>
                        </DataTableHeadCell>
                      )
                  )}
                  <TableRow {...headerGroup.getHeaderGroupProps()}>
                    {headerGroup?.headers?.map(
                      (column) =>
                        column?.Header !== keyFieldHeader && (
                          <DataTableHeadCell
                            {...column.getHeaderProps(isSorted && column.getSortByToggleProps())}
                            width={column?.width ? column?.width : "auto"}
                            align={column?.align ? column?.align : "left"}
                            sorted={setSortedValue(column)}
                            backgroundColor={backgroundColor}
                            textColor={textColor}
                            isSticky={column?.isSticky}
                            index={column?.index}
                          >
                            <MDBox
                              style={{
                                width: column?.width ? column?.width : "auto",
                                color: textColor !== "" ? textColor : "white",
                              }}
                            >
                              {column.render("Header")}
                            </MDBox>
                          </DataTableHeadCell>
                        )
                    )}
                  </TableRow>
                </TableRow>
              ) : (
                <TableRow {...headerGroup.getHeaderGroupProps()}>
                  {headerGroup.headers.map((column) => (
                    <DataTableHeadCell
                      {...column.getHeaderProps(isSorted && column.getSortByToggleProps())}
                      width={column.width ? column.width : "auto"}
                      align={column.align ? column.align : "left"}
                      sorted={setSortedValue(column)}
                      backgroundColor={backgroundColor}
                      textColor={textColor}
                    >
                      {column.render("Header")}
                    </DataTableHeadCell>
                  ))}
                </TableRow>
              )
            )}
          </MDBox>
          <TableBody>
            {(() => {
              switch (status) {
                case Constants.PENDING:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDBox py={5} display="flex" justifyContent="center" alignItems="center">
                          {Icons.LOADING2}
                        </MDBox>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.FULFILLED:
                  return Object.keys(groupedData).map((func) => (
                    <Fragment key={func}>
                      <TableRow>
                        {functionPagination.includes(func) && (
                          <DataTableBodyCell
                            colSpan={6}
                            sx={{
                              fontWeight: "bold",
                              verticalAlign: headerGroups[0]?.headers[0]?.valign
                                ? headerGroups[0]?.headers[0]?.valign
                                : "center",
                            }}
                            isSticky={headerGroups[0]?.headers[0]?.isSticky}
                            index={1}
                            align={
                              headerGroups[0]?.headers[0]?.align
                                ? headerGroups[0]?.headers[0]?.align
                                : "left"
                            }
                          >
                            <Author name={func} maxContent={14} />
                          </DataTableBodyCell>
                        )}
                        {page?.map((row, key) => {
                          prepareRow(row);
                          return (
                            row?.values[keyField]?.props?.name === func && (
                              <TableRow {...row.getRowProps()}>
                                {row?.cells?.map(
                                  (cell) =>
                                    cell?.column?.Header !== keyFieldHeader && (
                                      <DataTableBodyCell
                                        noBorder={noEndBorder && rows.length - 1 === key}
                                        align={cell?.column?.align ? cell?.column?.align : "left"}
                                        isSticky={cell?.column?.isSticky}
                                        index={cell?.column?.index}
                                        color={cell?.value?.props?.color}
                                        {...cell.getCellProps()}
                                      >
                                        <MDBox
                                          style={{
                                            width: cell?.column?.width,
                                          }}
                                        >
                                          {cell.render("Cell")}
                                        </MDBox>
                                      </DataTableBodyCell>
                                    )
                                )}
                              </TableRow>
                            )
                          );
                        })}
                      </TableRow>
                    </Fragment>
                  ));

                case "noData":
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.NO_DATA_FOUND}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.REJECTED:
                default:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.SOMETHING_WENT_WRONG}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );
              }
            })()}
          </TableBody>
        </Table>
      </TableContainer>
      {status === Constants.FULFILLED && page.length > 0 && pageOptions.length > 1 && (
        <MDBox
          sx={{ color: "#f6f7ff" }}
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          justifyContent="space-between"
          alignItems={{ xs: "flex-start", sm: "center" }}
          ml="40%"
          p={!showTotalEntries && pageOptions.length === 1 ? 0 : 3}
        >
          <Pagination
            count={pageOptions.length}
            page={pageIndex + 1}
            onChange={handlePageChange}
            variant="outlined"
            shape="rounded"
            sx={{
              "& .Mui-selected:hover": {
                backgroundColor: "#f6f7ff",
              },
              "& .Mui-selected": {
                backgroundColor: "#e0e1f5",
              },
              ".MuiPaginationItem-root": {
                borderRadius: "50%",
                border: "none",
              },
            }}
          />
        </MDBox>
      )}
      {extraContent}
    </MDBox>
  );
}

// Setting default values for the props of DataTable
DataTable.defaultProps = {
  entriesPerPage: { defaultValue: 10, entries: [5, 10, 15, 20, 25] },
  canSearch: false,
  showTotalEntries: true,
  pagination: { variant: "gradient", color: "info" },
  isSorted: true,
  noEndBorder: false,
  currentPage: 0,
  loading: Constants.PENDING,
  licenseRequired: false,
  handleTablePagination: () => {},
  handleCurrentPage: () => {},
  backgroundColor: "", // Add a default value for backgroundColor
  textColor: "",
  extraContent: null,
  groupedData: [],
  keyField: "",
  keyFieldHeader: "",
};

// Typechecking props for the DataTable
DataTable.propTypes = {
  entriesPerPage: PropTypes.oneOfType([
    PropTypes.shape({
      defaultValue: PropTypes.number,
      entries: PropTypes.arrayOf(PropTypes.number),
    }),
    PropTypes.bool,
  ]),
  canSearch: PropTypes.bool,
  showTotalEntries: PropTypes.bool,
  table: PropTypes.objectOf(PropTypes.array).isRequired,
  pagination: PropTypes.shape({
    variant: PropTypes.oneOf(["contained", "gradient"]),
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "dark",
      "light",
    ]),
  }),
  isSorted: PropTypes.bool,
  noEndBorder: PropTypes.bool,
  loading: PropTypes.string,
  licenseRequired: PropTypes.bool,
  handleTablePagination: PropTypes.func,
  currentPage: PropTypes.number,
  handleCurrentPage: PropTypes.func,
  backgroundColor: PropTypes.string,
  textColor: PropTypes.string,
  extraContent: PropTypes.node,
  groupedData: PropTypes.arrayOf(PropTypes.object),
  keyField: PropTypes.string,
  keyFieldHeader: PropTypes.string,
};

export default DataTable;

import { useEffect, useState } from "react";

// Components
import Author from "components/Table/Author";

function QhseData(qhseDprList) {
  const [qhseRows, setQhseRows] = useState([]);

  useEffect(() => {
    if (qhseDprList) {
      const list = qhseDprList.map((item, index) => {
        const isIncident = item?.type?.toLowerCase() === "incident";
        const fontWeight = isIncident ? "bold" : "normal";
        const type = item.type.charAt(0).toUpperCase() + item.type.slice(1);
        const temp = {
          srNo: <Author name={index + 1} style={{ fontWeight }} />,
          type: <Author name={type} style={{ fontWeight }} />,
          previous: <Author name={item?.previous || "-"} style={{ fontWeight }} />,
          today: <Author name={item?.today || "-"} style={{ fontWeight }} />,
          total: <Author name={item?.total || "-"} style={{ fontWeight }} />,
          open: <Author name={item?.open || "-"} style={{ fontWeight }} />,
        };
        return temp;
      });
      setQhseRows([...list]);
    }
  }, [qhseDprList]);

  return {
    qhseColumns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Type", accessor: "type", width: "50%" },
      { Header: "Previous", accessor: "previous", align: "left", width: "10%" },
      { Header: "Today", accessor: "today", align: "left", width: "10%" },
      { Header: "Total", accessor: "total", align: "left", width: "10%" },
      { Header: "Open", accessor: "open", align: "left", width: "10%" },
    ],

    qhseRows,
  };
}

export default QhseData;

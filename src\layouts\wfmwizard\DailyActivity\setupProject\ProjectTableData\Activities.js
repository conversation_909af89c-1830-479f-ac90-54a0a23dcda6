import MDTypography from "components/MDTypography";
import MDBox from "components/MDBox";
import { useEffect, useState } from "react";
import { Icons } from "utils/Constants";
import { Icon, IconButton } from "@mui/material";

export default function Activities(
  activityList,
  handleOpenNewModal,
  setModalType,
  editLists,
  setEditLists,
  handleDelete,
  handleActivitySort,
  sorted,
  permission
) {
  const [rows, setRows] = useState([]);
  const mongooseId = "_id";

  const handleEdit = (item) => {
    setModalType("Update");
    setEditLists({ ...editLists, activity: item });
    handleOpenNewModal("Activity");
  };

  useEffect(() => {
    if (activityList) {
      const list = activityList.map((item) => {
        const temp = {
          name: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.name}
            </MDTypography>
          ),
          scope: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.scopeId?.name}
            </MDTypography>
          ),
          sortOrder: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.sortOrder}
            </MDTypography>
          ),
          action: (
            <MDBox>
              {permission?.update && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleEdit(item)}
                  sx={{ cursor: "pointer" }}
                  disabled={!item?.isDeletable || false}
                >
                  {Icons.EDIT}
                </IconButton>
              )}{" "}
              &nbsp;
              {permission?.delete && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  sx={{ cursor: "pointer" }}
                  onClick={() => handleDelete("Activity", item[mongooseId])}
                  disabled={!item?.isDeletable || false}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [activityList, permission]);

  return {
    ActivitiesColumns: [
      {
        Header: () => (
          <div
            onClick={handleActivitySort}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Enter" && handleActivitySort()}
            style={{ cursor: "pointer" }}
          >
            Name
            <MDBox
              position="absolute"
              top={-3}
              left="20%"
              color={sorted === "asc" ? "text" : "secondary"}
              opacity={sorted === "asc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_up</Icon>
            </MDBox>
            <MDBox
              position="absolute"
              top={3}
              left="20%"
              color={sorted === "desc" ? "text" : "secondary"}
              opacity={sorted === "desc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_down</Icon>
            </MDBox>
          </div>
        ),
        accessor: "name",
        width: "40%",
        align: "left",
      },
      { Header: "Scope", accessor: "scope", width: "40%", align: "left" },
      { Header: "Sort Order", accessor: "sortOrder", width: "10%", align: "left" },
      ...(permission?.update || permission?.delete
        ? [{ Header: "Action", accessor: "action", width: "10%", align: "right" }]
        : []),
    ],
    ActivitiesRows: rows,
  };
}

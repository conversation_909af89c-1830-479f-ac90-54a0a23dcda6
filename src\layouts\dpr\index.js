import React, { useEffect, useState } from "react";
import { Feature } from "flagged";
import { useDispatch, useSelector } from "react-redux";

// MUI Components
import { Card, Divider, Grid, FormControl } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import DprModal from "examples/modal/Dpr";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";
import ResetFilterButton from "components/Buttons/ResetButton";
import DprViewCardDrawer from "examples/Drawers/Dpr";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import FTextField from "components/Form/FTextField";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";

// Assets
import pxToRem from "assets/theme/functions/pxToRem";

// Utilities
import Constants, {
  ButtonTitles,
  Icons,
  Colors,
  PageTitles,
  FeatureTags,
  defaultData,
  FiltersModuleName,
  ModalContent,
} from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

// Store
import { dprListThunk, deleteDpr } from "redux/Thunks/Dpr";
import { openSnackbar } from "redux/Slice/Notification";
import { setDprFilters, resetDprFilters } from "redux/Slice/Filter";
import { projectListThunk } from "redux/Thunks/FieldsData";
import { exportAllDprExcel } from "redux/Thunks/Filter";
import { resetDPR } from "redux/Slice/Dpr";

// Data
import DprData from "./data/dprData";

const dprFiltersArr = [
  {
    inputLabel: FiltersModuleName.PROJECT,
    list: [FiltersModuleName.DPR_FILTERS_TITLE_OBJ],
    selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
  },
  {
    inputLabel: FiltersModuleName.CREATED,
    list: ["All", "Today", "This Week", "This Month", "Last Month", "This Year"],
    selectedValue: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
  },
  {
    inputLabel: FiltersModuleName.STATUS,
    list: ["All", "Open", "Submitted", "In Discussion", "Closed"],
    selectedValue: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
  },
];

function Dpr() {
  const dispatch = useDispatch();

  const { dprList, loading, dprListpagination } = useSelector((state) => state?.dprs) || {};
  const dprFiltersList = useSelector((state) => state.filtersSlice.dprFilters);
  // Checking If the store has any previous filters saved.
  const initialFilters = dprFiltersList?.length > 0 ? dprFiltersList : dprFiltersArr;

  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.PER_PAGE,
  });
  const [openDpr, setOpenDpr] = useState(false);
  const [shouldUpdateState, setShouldUpdateState] = useState(false);
  const [viewDprAnchor, setViewDprAnchor] = useState({ right: false });
  const [dprViewId, setDprViewId] = useState(null);
  const [filters, setFilters] = useState(initialFilters);
  const [exportLoader, setExportLoader] = useState(false);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[12]?.screensInfo?.agreement;
  const [dprDelete, setDprDelete] = useState({
    open: false,
    id: "",
  });
  const [deleteText, setDeleteText] = useState("");

  const handleOpenDprDetailDrawer = async (id) => {
    dispatch(resetDPR());
    setViewDprAnchor({ right: true });
    setDprViewId(id);
  };

  const handleDprDelete = async () => {
    const res = await dispatch(deleteDpr(dprDelete.id));
    if (res.payload.status === 200) {
      await dispatch(
        openSnackbar({ message: Constants.DPR_DELETE_SUCCESS, notificationType: "success" })
      );
      setDprDelete({ open: false, id: "" });
    } else {
      await dispatch(
        openSnackbar({ message: Constants.DPR_DELETE_ERROR, notificationType: "error" })
      );
      setDprDelete({ open: false, id: "" });
    }
  };

  const handleOpenDprDelete = (id) => {
    setDeleteText("");
    setDprDelete({
      open: true,
      id,
    });
  };

  const { columns, rows } = DprData({
    dprList: dprList || [],
    handleOpenDprDetailDrawer,
    handleOpenDprDelete,
  });

  const handleTablePagination = async (value) => {
    if (dprListpagination.completed.includes(value)) return;
    setShouldUpdateState((prev) => !prev);
  };

  // Get Project List Func for dropdown.
  const getProjectListFunc = async () => {
    try {
      const projectList = await dispatch(projectListThunk());
      if (projectList?.payload?.status) {
        setFilters((prev) => {
          const updatedFilters = prev.map((filter) => {
            if (filter.inputLabel === FiltersModuleName.PROJECT) {
              return {
                ...filter,
                selectedValue: projectList?.payload?.data?.some(
                  (project) => project[Constants.MONGOOSE_ID] === filters[0]?.selectedValue
                )
                  ? filters[0]?.selectedValue
                  : FiltersModuleName.ALL_IN_SMALL_CASE,
                list: [FiltersModuleName.DPR_FILTERS_TITLE_OBJ, ...projectList.payload.data],
              };
            }
            return filter;
          });

          // Updating the Store also with the latest Project List.
          dispatch(setDprFilters(updatedFilters));
          return updatedFilters;
        });
        setShouldUpdateState((prev) => !prev);
      }
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleFilterChange = async (e) => {
    const { value, name } = e.target;

    setFilters((prevFilters) => {
      const updatedFilters = prevFilters.map((filter) => {
        if (filter.inputLabel === name) {
          return { ...filter, selectedValue: value };
        }
        return filter;
      });
      // Updating the Store also with the latest Project List.
      dispatch(setDprFilters(updatedFilters));
      return updatedFilters; // Only update the state here
    });

    setTablePagination((prev) => ({ ...prev, page: 0 }));
    setShouldUpdateState((prev) => !prev);
  };

  const setFilterPamrams = () => {
    const filterParams = {
      page: tablePagination.page,
      perPage: tablePagination.perPage,
      project: filters[0].selectedValue === "all" ? "" : filters[0].selectedValue,
      date: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      status:
        filters[2].selectedValue.toLowerCase() === "all"
          ? ""
          : filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    };

    return filterParams;
  };

  // All the component functions starts from here.
  const getDprData = async () => {
    const filterParams = setFilterPamrams();
    const updatedFilterParams = paramCreater(filterParams);
    try {
      await dispatch(dprListThunk(updatedFilterParams));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const reloadDpr = async () => {
    await getProjectListFunc();
    setShouldUpdateState((prev) => !prev);
  };

  const handleResetFilter = () => {
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => ({
        ...filter,
        selectedValue: filter.list[0][Constants.MONGOOSE_ID] || filter.list[0],
      }));
      return updatedFilters;
    });
    setTablePagination((prev) => ({ ...prev, page: 0 }));
    dispatch(resetDprFilters());
    setShouldUpdateState((prev) => !prev);
  };

  useEffect(() => {
    getProjectListFunc();
  }, []);

  useEffect(() => {
    getDprData();
    dispatch(resetDPR());
  }, [shouldUpdateState]);

  const handleErrorFunc = () => {
    dispatch(
      openSnackbar({
        message: Constants.SOMETHING_WENT_WRONG,
        notificationType: Constants.NOTIFICATION_ERROR,
      })
    );
  };

  const handleExport = async (format) => {
    setExportLoader(true);
    const currentDate = new Date();
    const filename = `Reynard_dpr_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.${format}`;
    const queryParams = {
      project: filters[0].selectedValue,
      date: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      status: filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const res = await dispatch(exportAllDprExcel(queryString));
    if (res.error) {
      handleErrorFunc();
      setExportLoader(false);
      return;
    }
    const url = window.URL.createObjectURL(res.payload);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    setExportLoader(false);
  };

  return (
    <DashboardLayout module={defaultData.DPR_SCREEN_ID}>
      <DashboardNavbar />
      <Feature name={FeatureTags.DPR_DETAILS}>
        <MDBox
          display="flex"
          flexDirection={{ md: "row", sm: "column" }}
          justifyContent={{ md: "space-between" }}
          alignItems={{ lg: "space-between", sm: "center" }}
        >
          <PageTitle title={PageTitles.DPR} />
          <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
            {permission?.create && (
              <CustomButton
                title={ButtonTitles.NEW_DPR}
                icon={Icons.NEW}
                background={Colors.PRIMARY}
                color={Colors.WHITE}
                openModal={setOpenDpr}
              />
            )}
            <Divider
              orientation="vertical"
              sx={{
                backgroundColor: "var(--gray-300, #D0D5DD)",
                height: "auto",
                marginLeft: pxToRem(16),
                marginRight: 0,
              }}
            />
            <BasicButton
              icon={Icons.RELOAD}
              background={Colors.WHITE}
              action={reloadDpr}
              border
              color={Colors.BLACK}
            />
          </MDBox>
        </MDBox>
        <Divider sx={{ marginTop: 2 }} />

        <MDBox display="flex" justifyContent="space-between" mx={0}>
          <MDBox
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "start",
              alignItems: "flex-end",
              mx: 0,
              rowGap: pxToRem(16),
            }}
          >
            {filters?.map((val) => {
              const isProject = val?.inputLabel === FiltersModuleName.PROJECT;
              return isProject ? (
                <FormControl
                  key={val?.inputLabel}
                  variant="standard"
                  size="medium"
                  style={{ marginTop: "26px", width: 200, marginRight: "15px" }}
                >
                  <CustomAutoComplete
                    label={val?.inputLabel}
                    name={val?.inputLabel}
                    id={val?.inputLabel}
                    getOptionLabel={(option) => option.title || ""}
                    menu={val?.list}
                    value={{
                      title:
                        val?.list.find((item) => item[Constants.MONGOOSE_ID] === val?.selectedValue)
                          ?.title || "",
                    }}
                    handleChange={handleFilterChange}
                    valueStyle={{
                      backgroundColor: Colors.WHITE,
                      height: pxToRem(40),
                      verticalMarginTop: pxToRem(4),
                      menuWidth: 400,
                      inputWidth: 250,
                      padding: pxToRem(1),
                    }}
                    labelStyle={{
                      fontSize: pxToRem(14),
                      fontWeight: 600,
                      color: Colors.BLACK,
                    }}
                  />
                </FormControl>
              ) : (
                <FilterDropdown
                  key={val.inputLabel}
                  label={val.inputLabel}
                  name={val.inputLabel}
                  defaultValue={val?.selectedValue}
                  value={val?.selectedValue}
                  menu={val.list}
                  maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
                  handleChange={handleFilterChange}
                />
              );
            })}
            <ResetFilterButton handleReset={handleResetFilter} style={{ marginLeft: "1rem" }} />
            <BasicButton
              title={exportLoader ? ButtonTitles.EXPORTING : ButtonTitles.EXPORT}
              icon={Icons.EXPORT}
              background={Colors.WHITE}
              color={Colors.BLACK}
              border
              action={() => handleExport("xlsx")}
              // disabled={filters[0].selectedValue === "all"}
              style={{ btnMarginLeft: pxToRem(0) }}
            />
          </MDBox>
        </MDBox>
        <MDBox mt={3} mb={3}>
          <Grid item xs={12}>
            <Card sx={{ boxShadow: "none" }}>
              <MDBox>
                <DataTable
                  table={{ columns, rows }}
                  isSorted={false}
                  entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
                  showTotalEntries={false}
                  noEndBorder
                  loading={loading}
                  licenseRequired
                  currentPage={tablePagination.page}
                  handleTablePagination={handleTablePagination}
                  handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
                  isGotoVisisble
                />
              </MDBox>
            </Card>
          </Grid>
        </MDBox>

        {/* New DPR */}
        {openDpr && (
          <DprModal
            openDprModal={openDpr}
            setOpenDprModal={setOpenDpr}
            setShouldUpdateState={setShouldUpdateState}
          />
        )}
      </Feature>
      {viewDprAnchor.right && (
        <DprViewCardDrawer
          viewDprAnchor={viewDprAnchor}
          dprViewId={dprViewId}
          setDprAnchor={setViewDprAnchor}
          // closeDrawer={handleCloseQhseDetailDrawer}
        />
      )}
      <DeleteModal
        open={dprDelete.open}
        title={ModalContent.DPR_DELETE_TITLE}
        message={ModalContent.DPR_DELETE_MESSAGE}
        handleClose={() => setDprDelete({ open: false, id: "" })}
        handleDelete={handleDprDelete}
        confirmText="DELETE"
        confirmInput={deleteText}
      >
        <FTextField
          placeholder="Enter Message"
          name="delete"
          id="delete"
          type="text"
          value={deleteText}
          handleChange={(e) => setDeleteText(e.target.value)}
        />
      </DeleteModal>
    </DashboardLayout>
  );
}

export default Dpr;

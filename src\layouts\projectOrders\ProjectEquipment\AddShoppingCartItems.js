import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// 3rd party libraries
import { useDispatch, useSelector } from "react-redux";
import moment from "moment-timezone";

// Components
import MDBox from "components/MDBox";
import FTextField from "components/Form/FTextField";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts";
import { ModalBreakPoint } from "components/Responsive/BreakPoints";

// Examples
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";

// Layouts
import ProjectOrdeReqItemTable from "layouts/projectOrders/data/ProjectOrdeReqItemTable";
// import NewShoppingCartModalTableData from "layouts/projectOrders/data/NewShoppingCartTables/NewShoppingCartModalTableData";

// Redux
import { openSnackbar } from "redux/Slice/Notification";
import { EquipmentTypeThunk } from "redux/Thunks/Equipment";

// Utils
import Constants, {
  Common,
  FormFields,
  defaultData,
  ButtonTitles,
  Icons,
  Colors,
} from "utils/Constants";
import { formatKeyName } from "utils/methods/methods";

const initialTableData = {
  rows: [],
  columns: [],
};

const initialRequestedItemsObj = {
  equipmentType: "",
  engineerRequestedQuantity: "",
  engineerComment: "",
};

function AddShoppingCartItems({
  addShoppingCartObjList,
  setAddShoppingCartObjList,
  headerItemsObj,
  setHeaderItemsObj,
  shoppingCartItemsErrors,
  setShoppingCartItemsErrors,
  fromDetailedViewPage,
}) {
  const dispatch = useDispatch();

  const configData = useSelector((state) => state.config) || {};
  const userId = configData?.config?.[0]?.id;
  const equipmentTypeList = useSelector((state) => state.product.equipmentTypeList) || [];

  const [projectOptionsList, setProjectOptionsList] = useState([]);
  const [equipmentOptionsList, setEquipmentOptionsList] = useState([]);

  const [requestedItemsObj, setRequestedItemsObj] = useState(initialRequestedItemsObj);
  const [updateStateAtAddItem, setUpdateStateAtAddItem] = useState(false);

  const [shoppingCartItemsDataTable, setShoppingCartItemsDataTable] = useState(initialTableData);

  const deleteShoppingCartItemFunc = (equipmentType) => {
    setAddShoppingCartObjList((prev) =>
      prev.filter((item) => item?.equipmentType !== equipmentType)
    );
    setUpdateStateAtAddItem((prev) => !prev);
  };

  // New Shopping Cart table columns and rows
  const { requestedItemsColumns, requestedItemsRows } = ProjectOrdeReqItemTable({
    dataList: addShoppingCartObjList,
    deleteReqItemFunc: deleteShoppingCartItemFunc,
    updateStateAtAddItem,
  });

  // Equipment Type List
  const getEquipmentTypeListFunc = async () => {
    try {
      await dispatch(EquipmentTypeThunk());
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const createCommentObjFunc = (dataObj = {}) => {
    if (!dataObj?.engineerComment || dataObj?.engineerComment?.trim() === "") {
      return null;
    }
    const commentObj = {
      user: userId,
      time: moment().format(Common.CURRENT_TIME_ZONE_FORMAT),
      status: "pending",
      comment: dataObj.engineerComment,
    };

    return commentObj;
  };

  const handleFormValueChange = (e, fromHeaderType = false) => {
    const { name, value } = e.target;

    // When header Items are added
    if (fromHeaderType) {
      setHeaderItemsObj((prev) => ({ ...prev, [name]: value }));
    } else {
      setRequestedItemsObj((prev) => ({ ...prev, [name]: value }));
    }

    setShoppingCartItemsErrors((prev) => {
      const { [name]: _, ...rest } = prev;
      return rest;
    });
  };

  const handleDateChange = (e) => {
    const { name, value } = e.target;
    let adjustedDate = null;

    // Only format the date if value is not empty/null
    if (value && value.trim() !== "") {
      adjustedDate = moment(value).format(defaultData.DATABSE_DATE_FORMAT);
    }

    setHeaderItemsObj((prev) => ({ ...prev, [name]: adjustedDate }));
    setShoppingCartItemsErrors((prev) => {
      const { [name]: _, ...rest } = prev;
      return rest;
    });
  };

  const validateFormDataFunc = (formDataObj = {}) => {
    // Making comment optional by removing it from formData.
    const { engineerComment, ...formData } = formDataObj;

    const errors = {};

    Object.keys(formData).forEach((key) => {
      const value = formData[key];

      // If the value is a string, trim and check if it's empty
      if (typeof value === "string" && value.trim() === "") {
        errors[key] = `${formatKeyName(key)} is required`;
      }
      // If the value is null or undefined (but allow numbers including 0)
      else if (value === null || value === undefined) {
        errors[key] = `${formatKeyName(key)} is required`;
      }
    });

    setShoppingCartItemsErrors((prev) => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  // Get Equipment Details by Id and update the requested items obj
  const getEquipmentDetailsById = (dataObj) => {
    const { equipmentType } = dataObj;
    const equipmentDetails = equipmentTypeList.find(
      (item) => item[Constants.MONGOOSE_ID] === equipmentType
    );

    if (equipmentDetails) {
      const productCategory = equipmentDetails?.equipmentCategory?.name;
      const productType = equipmentDetails?.type;
      const quantityType = equipmentDetails?.quantityType?.priceType;

      const engineerRequestedQuantity = Number(dataObj?.engineerRequestedQuantity) || 0;
      const totalAmount = Number(equipmentDetails?.price || 0) * engineerRequestedQuantity || 0;

      return {
        ...dataObj,
        productCategory,
        productType,
        quantityType,
        totalAmount,
        engineerRequestedQuantity,
      };
    }

    return dataObj;
  };

  // Update existing equipment type internally without API call
  const updateExistingEquipmentTypeFunc = (existingIndex, newRequestedObj) => {
    const existingItem = addShoppingCartObjList[existingIndex];
    const unitPrice = existingItem.totalAmount / existingItem.engineerRequestedQuantity;

    const currQuantity = Number(newRequestedObj.engineerRequestedQuantity) || 0;
    const totalQuantity = currQuantity + existingItem.engineerRequestedQuantity;
    const totalAmount = unitPrice * totalQuantity;

    const commentObj = createCommentObjFunc(newRequestedObj);
    const newCommentArr = commentObj
      ? [...existingItem.engineerComment, commentObj]
      : existingItem.engineerComment;

    const updatedItemObj = {
      ...existingItem,
      engineerRequestedQuantity: totalQuantity,
      totalAmount,
      engineerComment: newCommentArr,
    };

    setAddShoppingCartObjList((prev) =>
      prev.map((item, index) => (index === existingIndex ? updatedItemObj : item))
    );
    setRequestedItemsObj(initialRequestedItemsObj);
  };

  // Add Equipment in Shopping Cart without API call
  const addEquipmentInShoppingCartFunc = () => {
    const validateData = validateFormDataFunc({ ...headerItemsObj, ...requestedItemsObj });

    if (!validateData) {
      dispatch(
        openSnackbar({
          message: Constants.FILL_ALL_REQUIRED_FIELDS,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }

    const newUpdatedItemsObj = { ...requestedItemsObj, project: headerItemsObj?.project };

    // Check if the Item already exists
    const isEquipmentTypeExist = addShoppingCartObjList?.findIndex(
      (item) => item?.equipmentType === newUpdatedItemsObj?.equipmentType
    );

    if (isEquipmentTypeExist !== -1) {
      updateExistingEquipmentTypeFunc(isEquipmentTypeExist, newUpdatedItemsObj);
      setUpdateStateAtAddItem((prev) => !prev);
      return;
    }

    const equipmentDetailsObj = getEquipmentDetailsById(newUpdatedItemsObj);
    const commentObj = createCommentObjFunc(newUpdatedItemsObj);
    const newCommentArr = commentObj ? [commentObj] : [];

    const newEquipmentDetailsObj = {
      ...equipmentDetailsObj,
      engineerComment: newCommentArr,
    };

    setAddShoppingCartObjList((prev) => [...prev, newEquipmentDetailsObj]);
    setRequestedItemsObj(initialRequestedItemsObj);
    setUpdateStateAtAddItem((prev) => !prev);
  };

  useEffect(() => {
    const getProjectList = configData?.screens?.[5]?.screensInfo?.properties || [];
    const projectItem = getProjectList.find((item) => item.id === Common.PROJECT_TEXT);

    const createDropdownListFunc = (listArr = []) => {
      const temp = listArr.map((item) => ({
        title: item.title,
        [Constants.MONGOOSE_ID]: item.id,
      }));

      return temp;
    };

    if (projectItem) {
      const temp = createDropdownListFunc(projectItem.options);
      setProjectOptionsList(temp);
    }

    getEquipmentTypeListFunc();
  }, [configData]);

  useEffect(() => {
    if (equipmentTypeList?.length > 0) {
      const equipmentOptions = equipmentTypeList
        ?.filter((elem) => elem?.isTemporary === false)
        .map((item) => ({
          title: item.type,
          [Constants.MONGOOSE_ID]: item?.[Constants.MONGOOSE_ID],
        }));

      setEquipmentOptionsList(equipmentOptions);
    }
  }, [equipmentTypeList]);

  useEffect(() => {
    setShoppingCartItemsDataTable({ rows: requestedItemsRows, columns: requestedItemsColumns });
  }, [addShoppingCartObjList, updateStateAtAddItem]);

  return (
    <MDBox display="flex" flexDirection="column" gap={2}>
      {/* Title header Form Fields container */}
      {!fromDetailedViewPage && (
        <MDBox display="flex" gap={2} justifyContent="flex-start">
          <MDBox flex="1 1 20%">
            <FTextField
              label={Common.PROJECT_ORDER_SHOPPING_CART_TITLE_LABEL}
              name={Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME}
              id={Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME}
              type={FormFields.TYPE_TEXT}
              placeholder={Common.PROJECT_ORDER_SHOPPING_CART_TITLE_LABEL}
              disabled={addShoppingCartObjList.length > 0}
              value={headerItemsObj[Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME] || ""}
              error={Boolean(
                shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME]
              )}
              helperText={shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME]}
              handleChange={(e) => handleFormValueChange(e, true)}
            />
          </MDBox>

          <MDBox flex="1 1 20%">
            <CustomAutoComplete
              label={Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_LABEL}
              name={Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME}
              id={Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME}
              hint={Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_LABEL}
              getOptionLabel={(option) => option.title || ""}
              menu={projectOptionsList}
              disabled={addShoppingCartObjList.length > 0}
              value={{
                title: projectOptionsList.find(
                  (item) =>
                    item[Constants.MONGOOSE_ID] ===
                    headerItemsObj[Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME]
                )?.title,
              }}
              error={shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME]}
              helperText={shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME]}
              handleChange={(e) => handleFormValueChange(e, true)}
            />
          </MDBox>

          <MDBox flex="0 1 20%">
            <FTextField
              label={Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_LABEL}
              placeholder={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
              name={Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME}
              id={Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME}
              type="date"
              value={
                headerItemsObj?.fromDate
                  ? moment(headerItemsObj?.fromDate).format(defaultData.DATABSE_DATE_FORMAT)
                  : ""
              }
              error={Boolean(
                shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME]
              )}
              helperText={
                shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME]
              }
              handleChange={handleDateChange}
              InputProps={{
                inputProps: {
                  max: headerItemsObj?.toDate
                    ? moment(headerItemsObj?.toDate).format(defaultData.DATABSE_DATE_FORMAT)
                    : undefined,
                  style: { textTransform: "uppercase" },
                },
              }}
              onKeyPress={(e) => {
                // Prevent manual typing, only allow date picker selection
                e.preventDefault();
              }}
              disabled={addShoppingCartObjList.length > 0}
            />
          </MDBox>

          <MDBox flex="0 1 20%">
            <FTextField
              label={Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_LABEL}
              placeholder={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
              name={Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME}
              id={Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME}
              type="date"
              value={
                headerItemsObj?.toDate
                  ? moment(headerItemsObj?.toDate).format(defaultData.DATABSE_DATE_FORMAT)
                  : ""
              }
              error={Boolean(
                shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME]
              )}
              helperText={shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME]}
              handleChange={handleDateChange}
              InputProps={{
                inputProps: {
                  min: headerItemsObj?.fromDate
                    ? moment(headerItemsObj?.fromDate).format(defaultData.DATABSE_DATE_FORMAT)
                    : undefined,
                  style: { textTransform: "uppercase" },
                },
              }}
              onKeyPress={(e) => {
                // Prevent manual typing, only allow date picker selection
                e.preventDefault();
              }}
              disabled={addShoppingCartObjList.length > 0}
            />
          </MDBox>
        </MDBox>
      )}

      <MDBox display="flex" flexDirection="column" gap={2}>
        <MDBox>
          <MDTypography
            sx={{
              fontSize: FontComponent({ sizes: ModalBreakPoint.baseTitleBreakPoint }),
              color: "#475467",
              fontWeight: "700",
            }}
          >
            Equipment Items
          </MDTypography>
        </MDBox>

        {/* Box for Requested Items */}
        <MDBox display="flex" flexDirection="column" gap={1}>
          {/* Form Fields and Button Box */}
          <MDBox
            display="flex"
            flexDirection="row"
            gap={2}
            justifyContent="space-between"
            alignItems="bottom"
          >
            <MDBox
              display="flex"
              flexGrow={1}
              flexDirection="row"
              justifyContent="space-between"
              gap={2}
              key={updateStateAtAddItem}
            >
              <MDBox width="20%" minWidth="160px">
                <CustomAutoComplete
                  label={Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_LABEL}
                  name={Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME}
                  id={Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME}
                  hint={Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_LABEL}
                  getOptionLabel={(option) => option.title || ""}
                  menu={equipmentOptionsList}
                  value={{
                    title: equipmentOptionsList.find(
                      (item) =>
                        item[Constants.MONGOOSE_ID] ===
                        requestedItemsObj[Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME]
                    )?.title,
                  }}
                  error={
                    shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME]
                  }
                  helperText={
                    shoppingCartItemsErrors[Common.PROJECT_ORDER_SHOPPING_CART_EQUIPMENT_TYPE_NAME]
                  }
                  handleChange={(e) => handleFormValueChange(e)}
                />
              </MDBox>

              <MDBox width="20%" minWidth="160px">
                <FTextField
                  label={Common.PROJECT_ORDER_SHOPPING_CART_QUANTITY_LABEL}
                  name={Common.PROJECT_ORDER_SHOPPING_CART_QUANTITY_NAME}
                  id={Common.PROJECT_ORDER_SHOPPING_CART_QUANTITY_NAME}
                  type={FormFields.TYPE_NUMBER}
                  placeholder={Common.PROJECT_ORDER_QUANTITY_LABEL}
                  error={Boolean(shoppingCartItemsErrors.engineerRequestedQuantity)}
                  helperText={shoppingCartItemsErrors.engineerRequestedQuantity}
                  value={requestedItemsObj.engineerRequestedQuantity || ""}
                  handleChange={(e) => handleFormValueChange(e)}
                />
              </MDBox>

              <MDBox flexGrow={1} width="auto">
                <FTextField
                  label={Common.PROJECT_ORDDER_SHOPPING_CART_COMMENTS_LABEL}
                  name={Common.PROJECT_ORDER_SHOPPING_CART_COMMENTS_NAME}
                  id={Common.PROJECT_ORDER_SHOPPING_CART_COMMENTS_NAME}
                  type={FormFields.TYPE_TEXT}
                  placeholder={Common.PROJECT_ORDDER_SHOPPING_CART_COMMENTS_LABEL}
                  value={requestedItemsObj.engineerComment || ""}
                  handleChange={(e) => handleFormValueChange(e)}
                />
              </MDBox>
            </MDBox>

            <MDBox
              display="flex"
              justifyContent="flex-end"
              alignItems="end"
              flexShrink={0}
              mb={Object.keys(shoppingCartItemsErrors).length === 0 ? 1 : 3}
            >
              <BasicButton
                title={ButtonTitles.ADD_ITEM}
                icon={Icons.ADD}
                background={Colors.WHITE}
                border
                color={Colors.BLACK}
                action={addEquipmentInShoppingCartFunc}
              />
            </MDBox>
          </MDBox>

          {/* Table Container */}
          <MDBox>
            <DataTable
              table={shoppingCartItemsDataTable}
              isSorted={false}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
            />
          </MDBox>
        </MDBox>
      </MDBox>
    </MDBox>
  );
}

AddShoppingCartItems.defaultProps = {
  fromDetailedViewPage: false,
};

AddShoppingCartItems.propTypes = {
  addShoppingCartObjList: PropTypes.arrayOf(PropTypes.object).isRequired,
  setAddShoppingCartObjList: PropTypes.func.isRequired,
  fromDetailedViewPage: PropTypes.bool,
  headerItemsObj: PropTypes.objectOf(PropTypes.any).isRequired,
  setHeaderItemsObj: PropTypes.func.isRequired,
  shoppingCartItemsErrors: PropTypes.objectOf(PropTypes.any).isRequired,
  setShoppingCartItemsErrors: PropTypes.func.isRequired,
};

export default AddShoppingCartItems;

/* eslint-disable react/function-component-definition */

// MUI components
import { Icon, Modal } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import ModalTitle from "examples/NewDesign/ModalTitle";

// Styles
import style from "assets/style/Modal";

// Constants
import { Icons } from "utils/Constants";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// 3rd party library
import PropTypes from "prop-types";

function ViewProject({ openProjectList, handleCloseProjectList, title, children, maxWidth = 900 }) {
  return (
    <Modal
      open={openProjectList}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <MDBox sx={{ ...style, maxWidth }}>
        <MDBox
          bgColor="info"
          p={3}
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          borderRadius="lg"
          sx={{ borderBottomRightRadius: 0, borderBottomLeftRadius: 0, height: pxToRem(72) }}
        >
          <ModalTitle title={title} color="white" />

          <Icon
            sx={{ cursor: "pointer", color: "beige" }}
            fontSize="medium"
            onClick={handleCloseProjectList}
          >
            {Icons.CROSS}
          </Icon>
        </MDBox>
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          p={4}
          sx={{
            maxHeight: 500,
            overflowY: "scroll",
            overflowX: "scroll",
            "::-webkit-scrollbar": { display: "none" },
            scrollbarWidth: "none",
          }}
        >
          {children}
        </MDBox>
      </MDBox>
    </Modal>
  );
}

ViewProject.propTypes = {
  openProjectList: PropTypes.bool.isRequired,
  handleCloseProjectList: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  maxWidth: PropTypes.number,
};

ViewProject.defaultProps = {
  children: null,
  maxWidth: 900,
};

export default ViewProject;

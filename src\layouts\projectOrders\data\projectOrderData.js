import React, { useEffect, useState } from "react";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import { IconButton } from "@mui/material";

// Components
import Author from "components/Table/Author";
import CustomImage from "components/Table/GroupImage";
import { useNavigate } from "react-router-dom";

// Redux
import { useSelector } from "react-redux";

// Utils
import Constants, { Icons } from "utils/Constants";

export default function OrderDetailsData(list) {
  const { currentStatus } = useSelector((state) => state.equipmentRequest);
  const [projectrows, setProjectRows] = useState([]);
  const navigate = useNavigate();
  const handleView = (id) => {
    navigate(`/client/project-orders/project-order-details`, { state: { projectId: id } });
  };
  useEffect(() => {
    if (list?.dataList?.length > 0) {
      const tempRows = list?.dataList?.map((element, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          titleName: <Author name={element?.shoppingCartTitle} />,
          project: <Author name={element?.projectName} />,
          requestedEquipment: (
            <MDBox display="flex" flexDirection="row" justifyContent="flex-start">
              {element?.equipmentTypes?.length > 5
                ? element?.equipmentTypes.slice(0, 5).map((item, imgIndex) => (
                    <React.Fragment key={item[Constants.MONGOOSE_ID]}>
                      <CustomImage
                        item={item?.equipmentTypeImage?.url}
                        index={imgIndex}
                        requestedQuantity={item?.totalEngineerRequestedQuantity}
                        remainingLength={(element?.equipmentTypes?.length ?? 0) - 5}
                        width={30}
                        height={30}
                      />
                    </React.Fragment>
                  ))
                : element?.equipmentTypes.map((item, imgIndex) => (
                    <React.Fragment key={item[Constants.MONGOOSE_ID]}>
                      {item?.type && (
                        <CustomImage
                          item={item?.equipmentTypeImage?.url}
                          index={imgIndex}
                          requestedQuantity={item?.totalEngineerRequestedQuantity}
                          width={30}
                          height={30}
                        />
                      )}
                    </React.Fragment>
                  ))}
            </MDBox>
          ),
          reqQty: <Author name={element?.totalQuantity} />,
          reqEqpQty: <Author name={element?.totalEquipmentType} />,
          action: (
            <MDBox>
              <IconButton
                aria-label="fingerprint"
                color="info"
                onClick={() =>
                  handleView(
                    currentStatus === Constants.QUEUE
                      ? element[Constants.MONGOOSE_ID]?.shoppingCart
                      : element[Constants.MONGOOSE_ID]
                  )
                }
              >
                {Icons.VIEW}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setProjectRows([...tempRows]);
    }
  }, [list.dataList, currentStatus]);

  const projectColumns = [
    { Header: "No.", accessor: "srNo", width: "5%" },
    ...(currentStatus === Constants.QUEUE
      ? [{ Header: "Title Name", accessor: "titleName", align: "left" }]
      : []),
    { Header: "Project", accessor: "project" },
    { Header: "Equipment Type", accessor: "requestedEquipment", align: "left", width: "30%" },
    { Header: "Total Requested Qty", accessor: "reqQty", align: "center", width: "8%" },
    {
      Header: "Total Requested Equipment Type",
      accessor: "reqEqpQty",
      align: "center",
      width: "8%",
    },
    { Header: "Action", accessor: "action", width: "5%", align: "center" },
  ];

  const tableData = {
    projectColumns,
    projectrows,
  };

  return tableData;
}

/* eslint-disable react/prop-types */

import React, { useEffect, useState } from "react";

// Custom Components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import FTextField from "components/Form/FTextField";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import MultiSelectDropdown from "components/Dropdown/MultiSelectDropdown";
import NewProject from "examples/modal/NewProject/NewProject";
import MDInput from "components/MDInput";
import EquipmentGroupdata from "layouts/wfmwizard/Equipment/data/EquipmentGroupData";
import EquipmentTypedata from "layouts/wfmwizard/Equipment/data/EquipmentTypeData";
import EquipmentHSCodedata from "layouts/wfmwizard/Equipment/data/EquipmentHSCodeData";
import CertificateTypedata from "layouts/wfmwizard/Equipment/data/CertificateTypeData";
import WeightFormdata from "layouts/wfmwizard/Equipment/data/WeightFormData";
import QuantityTypedata from "layouts/wfmwizard/Equipment/data/QuantityTypeData";
import CurrencyUnitdata from "layouts/wfmwizard/Equipment/data/CurrencyUnitData";
import CENormData from "layouts/wfmwizard/Equipment/data/CENormData";
import DataTable from "examples/Tables/DataTable";
import ViewDrawer from "examples/Drawers/ViewTableDrawer/index";
import ExportHOC from "examples/HigherOrderComponents/ExportHOC";
import CustomRadio from "components/CustomRadio/CustomRadio";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import FDropdown from "components/Dropdown/FDropdown";
import SearchBar from "components/Search/SearchInTable";

// MUI Components
import {
  Card,
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  InputAdornment,
} from "@mui/material";

// Redux
import {
  EquipmentCategoryThunk,
  EquipmentTypeThunk,
  typegetByIdThunk,
  EquipmentHSCodeThunk,
  groupCreateThunk,
  groupUpdateThunk,
  groupDeleteThunk,
  hscodeCreateThunk,
  hscodeUpdateThunk,
  hscodeDeleteThunk,
  typeCreateThunk,
  typeUpdateThunk,
  typeDeleteThunk,
  certificateListTypeThunk,
  certificateTypeCreateThunk,
  certificateTypeUpdateThunk,
  certificateTypeDeleteThunk,
  weightformListThunk,
  weightformUpdateThunk,
  weightformCreateThunk,
  weightformDeleteThunk,
  quantitytypeListThunk,
  quantitytypeCreateThunk,
  quantitytypeUpdateThunk,
  quantitytypeDeleteThunk,
  currencyunitListThunk,
  currencyunitCreateThunk,
  currencyunitUpdateThunk,
  currencyunitDeleteThunk,
  ceNormListThunk,
  ceNormCreateThunk,
  ceNormUpdateThunk,
  ceNormDeleteThunk,
} from "redux/Thunks/Equipment";
import {
  updateEquipmentType,
  updateSetupEquipment,
  deleteEquipmentType,
} from "redux/Slice/SetupEquipment";
import { openSnackbar } from "redux/Slice/Notification";
import { useDispatch, useSelector } from "react-redux";

// Constants
import Constants, {
  PageTitles,
  Icons,
  FeatureTags,
  ButtonTitles,
  Colors,
  defaultData,
} from "utils/Constants";
import Validators from "utils/Validations";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// 3rd party library
import { Feature } from "flagged";
import { paramCreater, numericFieldValidation } from "utils/methods/methods";
import useSortHandler from "components/Table/Sort";
import FontComponent from "components/Responsive/fonts";

const cardList = [
  {
    cardTitle: "Equipment Type",
    content: "Add or Update Equipment Type.",
  },
  {
    cardTitle: "Equipment Category",
    content: "Add or Update Equipment Category",
  },
  {
    cardTitle: "Currency Unit",
    content: "Add or Update Currency Unit.",
  },
  {
    cardTitle: "Weight Form",
    content: "Add or Update Weight Form.",
  },
  {
    cardTitle: "Quantity Type",
    content: "Add or Update Quantity Type.",
  },
  {
    cardTitle: "HS Code",
    content: "Add or Update HS Code.",
  },
  {
    cardTitle: "Certificate Type",
    content: "Add or Update Certificate Type.",
  },
  {
    cardTitle: "CE Norm",
    content: "Add or Update CE Norm.",
  },
];

function SetupEquipment(props) {
  const fontSize = FontComponent({ sizes: {} });
  const dispatch = useDispatch();
  const [next, setNext] = useState({
    group: 0,
    equiptype: 0,
    hscode: 0,
    certificatetype: 0,
    weightForm: 0,
    quantityType: 0,
    currencyUnit: 0,
    ceNorms: 0,
  });
  const [openEquipmentGroup, setOpenEquipmentGroup] = useState(false);
  const [openEquipmentType, setOpenEquipmentType] = useState(false);
  const [openHSCode, setOpenHSCode] = useState(false);
  const [openCertificateType, setOpenCertificateType] = useState(false);
  const [openWeightForm, setOpenWeightForm] = useState(false);
  const [openQuantityType, setOpenQuantityType] = useState(false);
  const [openCurrencyUnit, setOpenCurrencyUnit] = useState(false);
  const [openCENorm, setOpenCENorm] = useState(false);
  const [modalType, setModalType] = useState("New");
  const [loading, setLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [deleteData, setDeleteData] = useState({ openDeleteModal: false, type: "", id: "" });
  const [tablePagination, setTablePagination] = useState({
    page: defaultData.PAGE,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });
  const [openEquipmentGroupData, setOpenEquipmentGroupData] = useState({ right: false });
  const [filters, setFilters] = useState([
    {
      inputLabel: "Search",
      list: [{ [Constants.MONGOOSE_ID]: "All", title: "All" }],
      selectedValue: "All",
      isLoading: false,
    },
  ]);
  const { handleEquipmentSampleFileExport } = props;
  const list = useSelector((state) => state.setupEquipment);
  const handleCloseEquipmentGroupData = () => {
    setOpenEquipmentGroupData({ right: false });
    setModalType("New");
    setTablePagination({ ...tablePagination, page: 0 });
    setFilters((prev) => [
      {
        ...prev[0],
        selectedValue: "All",
        list: [{ [Constants.MONGOOSE_ID]: "All", title: "All" }],
        isLoading: false,
      },
      ...prev.slice(1),
    ]);
    setRefresh(!refresh);
  };

  const [openEquipmentTypeData, setOpenEquipmentTypeData] = useState(false);
  const handleCloseEquipmentTypeData = () => {
    setOpenEquipmentTypeData({ right: false });
    setModalType("New");
    setTablePagination({ ...tablePagination, page: 0 });
    setFilters((prev) => [
      {
        ...prev[0],
        selectedValue: "All",
        list: [{ [Constants.MONGOOSE_ID]: "All", title: "All" }],
        isLoading: false,
      },
      ...prev.slice(1),
    ]);
    setRefresh(!refresh);
  };
  const [openHSCodeData, setOpenHSCodeData] = useState({ right: false });
  const handleCloseHSCodeData = () => {
    setOpenHSCodeData({ right: false });
    setModalType("New");
    setTablePagination({ ...tablePagination, page: 0 });
    setFilters((prev) => [
      {
        ...prev[0],
        selectedValue: "All",
        list: [{ [Constants.MONGOOSE_ID]: "All", title: "All" }],
        isLoading: false,
      },
      ...prev.slice(1),
    ]);
    setRefresh(!refresh);
  };

  const [openCertificateTypeData, setOpenCertificateTypeData] = useState({ right: false });
  const handleCloseCertificateTypeData = () => {
    setOpenCertificateTypeData({ right: false });
    setModalType("New");
    setTablePagination({ ...tablePagination, page: 0 });
  };

  const [openWeightFormData, setOpenWeightFormData] = useState({ right: false });
  const handleCloseOpenWeightFormData = () => {
    setOpenWeightFormData({ right: false });
    setModalType("New");
  };

  const [openQuantityTypeData, setOpenQuantityTypeData] = useState({ right: false });
  const handleCloseOpenQuantityTypeData = () => {
    setOpenQuantityTypeData({ right: false });
    setModalType("New");
  };

  const [openCurrencyUnitData, setOpenCurrencyUnitData] = useState({ right: false });
  const handleCloseCurrencyUnitData = () => {
    setOpenCurrencyUnitData({ right: false });
    setModalType("New");
  };
  const [openCENormData, setOpenCENormData] = useState({ right: false });
  const handleCloseCENormData = () => {
    setOpenCENormData({ right: false });
    setModalType("New");
  };
  const [lists, setLists] = useState({
    group: [],
    equiptype: [],
    hscode: [],
    certificatetype: [],
    weightForm: [],
    quantityType: [],
    currencyUnit: [],
    ceNorms: [],
  });
  const [loadingStatus, setLaodingStatus] = useState({
    group: Constants.PENDING,
    equiptype: Constants.PENDING,
    hscode: Constants.PENDING,
    certificatetype: Constants.PENDING,
    weightForm: Constants.PENDING,
    quantityType: Constants.PENDING,
    currencyUnit: Constants.PENDING,
    ceNorms: Constants.PENDING,
  });
  const [editLists, setEditLists] = useState({
    group: {},
    equiptype: {
      certificateTypes: [],
    },
    hscode: {},
    certificatetype: {},
    weightForm: {},
    quantityType: {},
    currencyUnit: {},
    ceNorms: {},
  });
  const [body, setBody] = useState({
    group: {},
    equiptype: {
      certificateTypes: [],
    },
    hscode: {},
    certificatetype: {
      isValidityDate: "false",
    },
    weightForm: {},
    quantityType: {},
    currencyUnit: {},
    ceNorms: {},
  });
  const [error, setError] = useState({
    group: {},
    equiptype: {},
    hscode: {},
    certificatetype: {},
    weightForm: {},
    quantityType: {},
    currencyUnit: {},
    ceNorms: {},
  });
  const [dropdownData, setDropdownData] = useState({
    group: [],
    type: [],
    hscode: [],
    weightForm: [],
    quantityType: [],
    currencyUnit: [],
    price: [],
    certificateType: [],
  });

  let debounceTimeout;
  const handleCloseDeleteModal = () => setDeleteData({ openDeleteModal: false, type: "", id: "" });
  const handleDelete = (type, id) => setDeleteData({ openDeleteModal: true, type, id });
  const { sortState: sortTypeName, handleHeaderClick: handleSortTypeName } = useSortHandler(
    EquipmentTypeThunk,
    setTablePagination,
    setNext,
    loadingStatus,
    setLaodingStatus,
    filters,
    tablePagination
  );
  const { sortState: sortCategoryName, handleHeaderClick: handleSortCategoryName } = useSortHandler(
    EquipmentCategoryThunk,
    setTablePagination,
    setNext,
    loadingStatus,
    setLaodingStatus,
    filters,
    tablePagination
  );
  const handleOpenDeleteModal = async () => {
    const { type, id } = deleteData;

    if (type === "group") {
      const res = await dispatch(groupDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "equipmentCategory" }));
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_CATEGORY_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "hscode") {
      const res = await dispatch(hscodeDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "hsCode" }));
        await dispatch(
          openSnackbar({
            message: Constants.HSCODE_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "equiptype") {
      const res = await dispatch(typeDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "equiptype" }));
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_TYPE_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else if (res.payload.status === 405) {
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_TYPE_LINKED_TO_DELETE,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "certificatetype") {
      const res = await dispatch(certificateTypeDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "certificatetype" }));
        await dispatch(
          openSnackbar({
            message: Constants.CERTIFICATE_TYPE_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "weightForm") {
      const res = await dispatch(weightformDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "weightForm" }));
        await dispatch(
          openSnackbar({
            message: Constants.WEIGHT_FORM_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "quantityType") {
      const res = await dispatch(quantitytypeDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "quantityType" }));
        await dispatch(
          openSnackbar({
            message: Constants.QUANTITY_TYPE_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "currencyUnit") {
      const res = await dispatch(currencyunitDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "currencyUnit" }));
        await dispatch(
          openSnackbar({
            message: Constants.CURRENCY_UNIT_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else if (type === "ceNorms") {
      const res = await dispatch(ceNormDeleteThunk(id));
      if (res.payload.status === 200) {
        await dispatch(deleteEquipmentType({ id, cardType: "ceNorms" }));
        await dispatch(
          openSnackbar({
            message: Constants.CE_NORM_DELETE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        await dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    }

    handleCloseDeleteModal();
  };

  const deleteMessage = (type) => {
    let message = "";
    if (type === "group") {
      message = "Equipment Category";
    } else if (type === "equiptype") {
      message = "Equipment Type";
    } else if (type === "hscode") {
      message = "HS Code";
    } else if (type === "certificatetype") {
      message = "Certificate Type";
    } else if (type === "weightForm") {
      message = "Weight Form";
    } else if (type === "quantityType") {
      message = "Quantity Type";
    } else if (type === "currencyUnit") {
      message = "Currency Unit";
    }
    return message;
  };
  const handleOpenNewModal = (modal) => {
    if (modal === "Equipment Category") {
      setOpenEquipmentGroup(true);
    } else if (modal === "Equipment Type") {
      setOpenEquipmentType(true);
    } else if (modal === "HS Code") {
      setOpenHSCode(true);
    } else if (modal === "Certificate Type") {
      setOpenCertificateType(true);
    } else if (modal === "Weight Form") {
      setOpenWeightForm(true);
    } else if (modal === "Quantity Type") {
      setOpenQuantityType(true);
    } else if (modal === "Currency Unit") {
      setOpenCurrencyUnit(true);
    } else if (modal === "CE Norm") {
      setOpenCENorm(true);
    }
  };
  const handleViewModal = (modal) => {
    if (modal === "Equipment Category") {
      setOpenEquipmentGroupData({ right: true });
    } else if (modal === "Equipment Type") {
      setOpenEquipmentTypeData({ right: true });
    } else if (modal === "HS Code") {
      setOpenHSCodeData({ right: true });
    } else if (modal === "Certificate Type") {
      setOpenCertificateTypeData({ right: true });
    } else if (modal === "Weight Form") {
      setOpenWeightFormData({ right: true });
    } else if (modal === "Quantity Type") {
      setOpenQuantityTypeData({ right: true });
    } else if (modal === "Currency Unit") {
      setOpenCurrencyUnitData({ right: true });
    } else if (modal === "CE Norm") {
      setOpenCENormData({ right: true });
    }
  };
  const handleCloseNewModal = (modal) => {
    if (modal === "Equipment Category") {
      setOpenEquipmentGroup(false);
    } else if (modal === "Equipment Type") {
      setOpenEquipmentType(false);
    } else if (modal === "HS Code") {
      setOpenHSCode(false);
    } else if (modal === "Certificate Type") {
      setOpenCertificateType(false);
    } else if (modal === "Weight Form") {
      setOpenWeightForm(false);
    } else if (modal === "Quantity Type") {
      setOpenQuantityType(false);
    } else if (modal === "Currency Unit") {
      setOpenCurrencyUnit(false);
    } else if (modal === "CE Norm") {
      setOpenCENorm(false);
    }
    setModalType("New");
    setError({
      ...error,
      group: {},
      hscode: {},
      equiptype: {},
      certificatetype: {},
      weightForm: {},
      quantityType: {},
      currencyUnit: {},
      ceNorms: {},
    });
    setBody((prevBody) => ({
      ...prevBody,
      group: {},
      equiptype: {},
      hscode: {},
      certificatetype: { isValidityDate: "false" },
      weightForm: {},
      quantityType: {},
      currencyUnit: {},
      ceNorms: {},
    }));
  };

  const { groupColumns, groupRows } = EquipmentGroupdata(
    list.categoryList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete,
    handleSortCategoryName,
    sortCategoryName
  );
  const { typeColumns, typeRows } = EquipmentTypedata(
    list.typeList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete,
    handleSortTypeName,
    sortTypeName
  );
  const { hscodeColumns, hscodeRows } = EquipmentHSCodedata(
    list.hsCodeList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  const { certificateColumns, certificateRows } = CertificateTypedata(
    list.certificateTypeList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  const { weightFormColumns, weightFormRows } = WeightFormdata(
    list.weightFormList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  const { quantityTypeColumns, quantityTypeRows } = QuantityTypedata(
    list.quantityTypeList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  const { currencyUnitColumns, currencyUnitRows } = CurrencyUnitdata(
    list.currencyUnitList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  const { ceNormColumns, ceNormRows } = CENormData(
    list.CENormList,
    handleOpenNewModal,
    setModalType,
    editLists,
    setEditLists,
    handleDelete
  );
  useEffect(() => {
    (async () => {
      setTablePagination({ ...tablePagination, page: 0 });
      setNext({
        group: 0,
        equiptype: 0,
        hscode: 0,
        certificatetype: 0,
        weightForm: 0,
        quantityType: 0,
        currencyUnit: 0,
        ceNorms: 0,
      });
      const data = {
        page: 0,
        perPage: tablePagination.perPage,
      };
      const [
        groupRes,
        typeRes,
        hscodeRes,
        certificatetypeRes,
        weightFormRes,
        quantitytypeRes,
        currencyUnitRes,
        ceNormRes,
      ] = await Promise.all([
        dispatch(EquipmentCategoryThunk(paramCreater(data))),
        dispatch(EquipmentTypeThunk(paramCreater(data))),
        dispatch(EquipmentHSCodeThunk(paramCreater(data))),
        dispatch(certificateListTypeThunk(paramCreater(data))),
        dispatch(weightformListThunk(paramCreater(data))),
        dispatch(quantitytypeListThunk(paramCreater(data))),
        dispatch(currencyunitListThunk(paramCreater(data))),
        dispatch(ceNormListThunk(paramCreater(data))),
      ]);
      const dropdownFormat = {
        [Constants.MONGOOSE_ID]: "",
        title: "",
      };
      const tempCategory = groupRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.name;
        return temp;
      });
      const tempWeight = weightFormRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.abbreviation;
        return temp;
      });
      const tempCurrency = currencyUnitRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.symbol;
        return temp;
      });

      const tempQuantityType = quantitytypeRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.name;
        return temp;
      });
      const tempHSCode = hscodeRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = `${item.name}(${item.code})`;
        return temp;
      });
      const tempCertificateType = certificatetypeRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.title;
        return temp;
      });
      const tempCENorm = ceNormRes.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.name;
        return temp;
      });
      setDropdownData({
        ...dropdownData,
        group: tempCategory,
        weightForm: tempWeight,
        quantityType: tempQuantityType,
        currencyUnit: tempCurrency,
        hscode: tempHSCode,
        certificateType: tempCertificateType,
        ceNorms: tempCENorm,
      });
      setLists({
        ...lists,
        group: groupRes.payload.data.data,
        equiptype: typeRes.payload.data.data,
        hscode: hscodeRes.payload.data.data,
        certificatetype: certificatetypeRes.payload.data.data,
        weightForm: weightFormRes.payload.data.data,
        quantityType: quantitytypeRes.payload.data.data,
        currencyUnit: currencyUnitRes.payload.data.data,
        ceNorms: ceNormRes.payload.data.data,
      });
      setLaodingStatus({
        ...loadingStatus,
        group: Constants.FULFILLED,
        equiptype: Constants.FULFILLED,
        hscode: Constants.FULFILLED,
        certificatetype: Constants.FULFILLED,
        weightForm: Constants.FULFILLED,
        quantityType: Constants.FULFILLED,
        currencyUnit: Constants.FULFILLED,
        ceNorms: Constants.FULFILLED,
      });
    })();
  }, [dispatch, refresh]);
  useEffect(() => {
    const updatedBody = { ...body };
    if (Object.keys(editLists.group).length !== 0 && editLists.group.constructor === Object) {
      updatedBody.group = {
        name: editLists.group.name,
        abbreviation: editLists.group.abbreviation,
      };
    }
    if (
      Object.keys(editLists.equiptype).length !== 0 &&
      editLists.equiptype.constructor === Object
    ) {
      updatedBody.equiptype = {
        type: editLists.equiptype.type,
        equipmentCategory: editLists.equiptype.equipmentCategory?.[Constants.MONGOOSE_ID],
        equipmentUnit: editLists.equiptype.equipmentUnit?.[Constants.MONGOOSE_ID],
        price: editLists.equiptype.price,
        currencyUnit: editLists.equiptype.currencyUnit?.[Constants.MONGOOSE_ID],
        quantityType: editLists.equiptype.quantityType?.[Constants.MONGOOSE_ID],
        hsCode: editLists.equiptype?.hsCode?.[Constants.MONGOOSE_ID],
        certificateTypes:
          editLists.equiptype?.certificateTypes?.map((type) => type?.[Constants.MONGOOSE_ID]) || [],
        ceNorms: editLists.equiptype.ceNorms?.[Constants.MONGOOSE_ID],
      };
    }
    if (Object.keys(editLists.hscode).length !== 0 && editLists.hscode.constructor === Object) {
      updatedBody.hscode = {
        code: editLists.hscode.code,
        name: editLists.hscode.name,
      };
    }
    if (
      Object.keys(editLists.certificatetype).length !== 0 &&
      editLists.certificatetype.constructor === Object
    ) {
      updatedBody.certificatetype = {
        title: editLists.certificatetype.title,
        isValidityDate: editLists.certificatetype.isValidityDate,
      };
    }
    if (
      Object.keys(editLists.weightForm).length !== 0 &&
      editLists.weightForm.constructor === Object
    ) {
      updatedBody.weightForm = {
        title: editLists.weightForm.title,
        abbreviation: editLists.weightForm.abbreviation,
      };
    }
    if (
      Object.keys(editLists.quantityType).length !== 0 &&
      editLists.quantityType.constructor === Object
    ) {
      updatedBody.quantityType = {
        name: editLists.quantityType.name,
        priceType: editLists.quantityType.priceType,
        quantityType: editLists.quantityType.quantityType,
      };
    }
    if (
      Object.keys(editLists.currencyUnit).length !== 0 &&
      editLists.currencyUnit.constructor === Object
    ) {
      updatedBody.currencyUnit = {
        name: editLists.currencyUnit.name,
        symbol: editLists.currencyUnit.symbol,
        isDefault: editLists.currencyUnit.isDefault,
      };
    }
    if (Object.keys(editLists.ceNorms).length !== 0 && editLists.ceNorms.constructor === Object) {
      updatedBody.ceNorms = {
        name: editLists.ceNorms.name,
        month: editLists.ceNorms.month,
      };
    }
    setBody(updatedBody);
  }, [editLists]);
  const handleChange = (event) => {
    const { name, value, id } = event.target;

    // Check if uppercase transformation is needed
    const uppercaseValue =
      id === "abbreviation" || id === "weightabbrevation" ? value.toUpperCase() : value;

    // Creating a new object for the specific field
    const updatedField = {
      ...body[name], // Preserve other properties in the field if any
      [id]: uppercaseValue, // Assuming you want to store the value with an ID
    };

    // Updating the appropriate field in the body
    setBody((prevBody) => ({
      ...prevBody,
      [name]: updatedField,
    }));
  };

  const handleRadioChange = (bodyName, e) => {
    if (e.target) {
      const { name, value } = e.target;
      setBody((prevBody) => ({
        ...prevBody,
        [bodyName]: {
          ...prevBody[bodyName],
          [name]: value,
        },
      }));
    }
  };
  const currencyData = lists.currencyUnit;
  const defaultCurrency =
    currencyData.find((currency) => currency.isDefault)?.[Constants.MONGOOSE_ID] ?? "";

  const handleCheckboxChange = (bodyName, e) => {
    setBody({
      ...body,
      [bodyName]: {
        ...body[bodyName],
        [e.target.name]: e.target.checked,
      },
    });
  };
  const validation = (type) => {
    const {
      group,
      hscode,
      equiptype,
      certificatetype,
      weightForm,
      quantityType,
      currencyUnit,
      ceNorms,
    } = body;
    const tempError = {};

    if (type === "group") {
      const groupTitle = Validators.validate("basic", group?.name || "");
      const abbrevationTitle = Validators.validate("basic", group?.abbreviation || "");
      if (groupTitle !== "") tempError.name = groupTitle;
      if (abbrevationTitle !== "") {
        tempError.abbreviation = abbrevationTitle;
      } else if (
        (group?.abbreviation && group.abbreviation.length < 3) ||
        (group?.abbreviation && group.abbreviation.length > 3)
      ) {
        tempError.abbreviation = "Equipment Nr. Abbreviation must be exactly 3 characters long.";
      }
    } else if (type === "hscode") {
      const { name, code } = hscode || {};
      const hscodeName = Validators.validate("basic", name || "");
      const hscodeCode = Validators.validate("basic2", code?.toString());
      if (hscodeName !== "") tempError.name = hscodeName;
      if (hscodeCode !== "") tempError.code = hscodeCode;
    } else if (type === "equiptype") {
      const typeTitle = Validators.validate("basic", equiptype?.type || "");
      const typeCategory = Validators.validate("basic2", equiptype?.equipmentCategory || "");

      const typeWeight = Validators.validate("basic2", equiptype?.equipmentUnit || "");
      const typeQuantity = Validators.validate("basic2", equiptype?.quantityType || "");
      const typeHsCode = Validators.validate("basic2", equiptype?.hsCode || "");
      const typeCENorm = Validators.validate("basic2", equiptype?.ceNorms || "");
      if (typeTitle !== "") tempError.type = typeTitle;
      if (typeCategory !== "") tempError.equipmentCategory = typeCategory;

      if (typeWeight !== "") tempError.equipmentUnit = typeWeight;
      if (!equiptype?.price) {
        tempError.price = "Required";
      } else {
        const numericRentalPrice = parseFloat(equiptype.price);
        if (Number.isNaN(numericRentalPrice) || numericRentalPrice < 0) {
          tempError.price = "Invalid Rental Price";
        }
      }
      if (typeQuantity !== "") tempError.quantityType = typeQuantity;
      if (typeHsCode !== "") tempError.hsCode = typeHsCode;
      if (typeCENorm !== "") tempError.ceNorms = typeCENorm;
    } else if (type === "certificatetype") {
      const certificateTitle = Validators.validate("basic", certificatetype?.title || "");
      if (certificateTitle !== "") tempError.title = certificateTitle;
    } else if (type === "weightForm") {
      const weightFormTitle = Validators.validate("basic", weightForm?.title || "");
      const abbrevationTitle = Validators.validate("basic", weightForm?.abbreviation || "");
      if (weightFormTitle !== "") tempError.title = weightFormTitle;
      if (abbrevationTitle !== "") {
        tempError.abbreviation = abbrevationTitle;
      }
    } else if (type === "quantityType") {
      const quantityTypeTitle = Validators.validate("basic", quantityType?.name || "");
      const typePrice = Validators.validate("basic2", quantityType?.priceType || "");
      const typeQuantity = Validators.validate("basic2", quantityType?.quantityType || "");

      if (quantityTypeTitle !== "") tempError.name = quantityTypeTitle;
      if (typePrice !== "") tempError.priceType = typePrice;
      if (typeQuantity !== "") tempError.quantityType = typeQuantity;
    } else if (type === "currencyUnit") {
      const currencyUnitTitle = Validators.validate("basic", currencyUnit?.name || "");
      const currencySymbolTitle = Validators.validate("basic", currencyUnit?.symbol || "");
      if (currencyUnitTitle !== "") tempError.name = currencyUnitTitle;
      if (currencySymbolTitle !== "") tempError.symbol = currencySymbolTitle;
    } else if (type === "ceNorms") {
      const { name, month } = ceNorms || {};
      const ceNormName = Validators.validate("basic", name || "");
      if (ceNormName !== "") tempError.name = ceNormName;
      if (!month) {
        tempError.month = "Required";
      } else if (month < 0) {
        tempError.month = "Months must be a positive number";
      }
    }
    const isValid = Object.keys(tempError).length === 0;
    setError({ ...error, [type.substring(0, 1).toLowerCase() + type.substring(1)]: tempError });
    return isValid;
  };

  const handleCreate = async (type) => {
    setLoading(true);
    const isValid = validation(type);
    if (type === "group" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(groupCreateThunk(body.group));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenEquipmentGroup(false);
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_CATEGORY_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          group: {},
        }));
      } else if (res.payload.status === 422) {
        const newError = res.payload.data.data.error;
        let tempError = {};
        newError.forEach((item) => {
          tempError = { ...tempError, ...item };
        });
        setError((prevError) => ({
          ...prevError,
          group: { ...prevError.group, ...tempError },
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenEquipmentGroup(false);
      }
    } else if (type === "equiptype" && isValid) {
      setDisableSubmit(true);
      const { currencyUnit, ...rest } = body.equiptype;
      const currencyToUse = currencyUnit || defaultCurrency;
      const res = await dispatch(typeCreateThunk({ ...rest, currencyUnit: currencyToUse }));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenEquipmentType(false);
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_TYPE_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          equiptype: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.equiptype.quantityType = res.payload.data.data.error[0].quantityType;
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenEquipmentType(false);
      }
    } else if (type === "hscode" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(hscodeCreateThunk(body.hscode));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenHSCode(false);
        setRefresh(!refresh);
        await dispatch(
          openSnackbar({
            message: Constants.HSCODE_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          hscode: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.hscode.code = res.payload.data.data.error[0].code;
        temp.hscode.name = "";
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenHSCode(false);
      }
    } else if (type === "certificatetype" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(certificateTypeCreateThunk(body.certificatetype));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenCertificateType(false);
        await dispatch(
          openSnackbar({
            message: Constants.CERTIFICATE_TYPE_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          certificatetype: { isValidityDate: "false" },
        }));
      } else if (res.payload.status === 400) {
        const temp = { ...error };
        temp.certificatetype.title = res.payload.data.message;
        setError(temp);
      } else if (res.payload.status === false) {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCertificateType(false);
      }
    } else if (type === "weightForm" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(weightformCreateThunk(body.weightForm));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenWeightForm(false);
        await dispatch(
          openSnackbar({
            message: Constants.WEIGHT_FORM_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          weightForm: {},
        }));
      } else if (res.payload.status === false) {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenWeightForm(false);
      }
    } else if (type === "quantityType" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(quantitytypeCreateThunk(body.quantityType));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenQuantityType(false);
        await dispatch(
          openSnackbar({
            message: Constants.QUANTITY_TYPE_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          quantityType: {},
        }));
      } else if (res.payload.status === false) {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenQuantityType(false);
      }
    } else if (type === "currencyUnit" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(currencyunitCreateThunk(body.currencyUnit));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenCurrencyUnit(false);
        await dispatch(
          openSnackbar({
            message: Constants.CURRENCY_UNIT_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          currencyUnit: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.currencyUnit.isDefault = res.payload.data.data.error[0].isDefault;
        temp.currencyUnit.name = "";
        temp.currencyUnit.symbol = "";
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCurrencyUnit(false);
      }
    } else if (type === "ceNorms" && isValid) {
      setDisableSubmit(true);
      const res = await dispatch(ceNormCreateThunk(body.ceNorms));
      setDisableSubmit(false);
      if (res.payload.status === 200) {
        setOpenCENorm(false);
        await dispatch(
          openSnackbar({
            message: Constants.CE_NORM_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setRefresh(!refresh);
        setBody((prevBody) => ({
          ...prevBody,
          ceNorms: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.ceNorms.name = res.payload.data.data.error[0].name;
        temp.ceNorms.month = res.payload.data.data.error[0].month;
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCENorm(false);
      }
    }
    setLoading(false);
    if (isValid) {
      setModalType("New");
    }
  };
  const handleUpdate = async (type) => {
    setLoading(true);
    const isValid = validation(type);
    if (type === "group" && isValid) {
      const res = await dispatch(
        groupUpdateThunk({
          body: body.group,
          id: editLists.group[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        await dispatch(
          updateSetupEquipment({
            body: body.group,
            id: editLists.group[Constants.MONGOOSE_ID],
            cardType: "equipmentCategory",
          })
        );
        setOpenEquipmentGroup(false);
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_CATEGORY_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          group: {},
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenEquipmentGroup(false);
      }
    } else if (type === "hscode" && isValid) {
      const res = await dispatch(
        hscodeUpdateThunk({ body: body.hscode, id: editLists.hscode[Constants.MONGOOSE_ID] })
      );
      if (res.payload.status === 200) {
        await dispatch(
          updateSetupEquipment({
            body: body.hscode,
            id: editLists.hscode[Constants.MONGOOSE_ID],
            cardType: "hsCode",
          })
        );
        setOpenHSCode(false);
        await dispatch(
          openSnackbar({
            message: Constants.HSCODE_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          hscode: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.hscode.code = res.payload.data.data.error[0].code;
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenHSCode(false);
      }
    } else if (type === "equiptype" && isValid) {
      const res = await dispatch(
        typeUpdateThunk({ body: body.equiptype, id: editLists.equiptype[Constants.MONGOOSE_ID] })
      );
      if (res.payload.status === 200) {
        const getByIdres = await dispatch(
          typegetByIdThunk(editLists.equiptype[Constants.MONGOOSE_ID])
        );
        await dispatch(updateEquipmentType(getByIdres?.payload.data.data));
        setOpenEquipmentType(false);
        await dispatch(
          openSnackbar({
            message: Constants.EQUIPMENT_TYPE_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );

        setBody((prevBody) => ({
          ...prevBody,
          equiptype: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.equiptype.quantityType = res.payload.data.data.error[0].quantityType;
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenEquipmentType(false);
      }
    } else if (type === "certificatetype" && isValid) {
      const res = await dispatch(
        certificateTypeUpdateThunk({
          body: body.certificatetype,
          id: editLists.certificatetype[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        setOpenCertificateType(false);
        await dispatch(
          updateSetupEquipment({
            body: body.certificatetype,
            id: editLists.certificatetype[Constants.MONGOOSE_ID],
            cardType: "certificatetype",
          })
        );
        await dispatch(
          openSnackbar({
            message: Constants.CERTIFICATE_TYPE_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          certificatetype: { isValidityDate: "false" },
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCertificateType(false);
      }
    } else if (type === "weightForm" && isValid) {
      const res = await dispatch(
        weightformUpdateThunk({
          body: body.weightForm,
          id: editLists.weightForm[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        setOpenWeightForm(false);
        await dispatch(
          updateSetupEquipment({
            body: body.weightForm,
            id: editLists.weightForm[Constants.MONGOOSE_ID],
            cardType: "weightForm",
          })
        );
        await dispatch(
          openSnackbar({
            message: Constants.WEIGHT_FORM_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          weightForm: {},
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenWeightForm(false);
      }
    } else if (type === "quantityType" && isValid) {
      const res = await dispatch(
        quantitytypeUpdateThunk({
          body: body.quantityType,
          id: editLists.quantityType[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        setOpenQuantityType(false);
        await dispatch(
          updateSetupEquipment({
            body: body.quantityType,
            id: editLists.quantityType[Constants.MONGOOSE_ID],
            cardType: "quantityType",
          })
        );
        await dispatch(
          openSnackbar({
            message: Constants.QUANTITY_TYPE_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          quantityType: {},
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenQuantityType(false);
      }
    } else if (type === "currencyUnit" && isValid) {
      const res = await dispatch(
        currencyunitUpdateThunk({
          body: body.currencyUnit,
          id: editLists.currencyUnit[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        setOpenCurrencyUnit(false);
        await dispatch(
          updateSetupEquipment({
            body: body.currencyUnit,
            id: editLists.currencyUnit[Constants.MONGOOSE_ID],
            cardType: "currencyUnit",
          })
        );
        await dispatch(
          openSnackbar({
            message: Constants.CURRENCY_UNIT_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          currencyUnit: {},
        }));
      } else if (res.payload.status === 422) {
        const temp = { ...error };
        temp.currencyUnit.isDefault = res.payload.data.data.error[0].isDefault;
        temp.currencyUnit.name = "";
        temp.currencyUnit.symbol = "";
        setError(temp);
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCurrencyUnit(false);
      }
    } else if (type === "ceNorms" && isValid) {
      const res = await dispatch(
        ceNormUpdateThunk({
          body: body.ceNorms,
          id: editLists.ceNorms[Constants.MONGOOSE_ID],
        })
      );
      if (res.payload.status === 200) {
        setOpenCENorm(false);
        await dispatch(
          updateSetupEquipment({
            body: body.ceNorms,
            id: editLists.ceNorms[Constants.MONGOOSE_ID],
            cardType: "ceNorms",
          })
        );
        await dispatch(
          openSnackbar({
            message: Constants.CE_NORM_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setBody((prevBody) => ({
          ...prevBody,
          ceNorms: {},
        }));
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setOpenCENorm(false);
      }
    }
    setLoading(false);
    setModalType("Update");
  };

  const handleTablePagination = async (key) => {
    if (key === "groupPage") {
      const searchTerm = filters[0].selectedValue === "All" ? "" : filters[0].selectedValue.trim();
      const data = {
        name: searchTerm,
        page: next.group + 1,
        perPage: tablePagination.perPage,
        sortOrder: sortCategoryName || "",
      };
      const res = await dispatch(EquipmentCategoryThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setLists({
          ...lists,
          group: [...lists.group, ...res.payload.data.data],
        });
        setNext({
          ...next,
          group: res.payload.data.data.length > 0 ? next.group + 1 : next.group,
        });
      }
    } else if (key === "typePage") {
      const searchTerm = filters[0].selectedValue === "All" ? "" : filters[0].selectedValue.trim();
      const data = {
        name: searchTerm,
        page: next.equiptype + 1,
        perPage: tablePagination.perPage,
        sortOrder: sortTypeName || "",
      };
      const res = await dispatch(EquipmentTypeThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setLists({
          ...lists,
          equiptype: [...lists.equiptype, ...res.payload.data.data],
        });
        setNext({
          ...next,
          equiptype: res.payload.data.data.length > 0 ? next.equiptype + 1 : next.equiptype,
        });
      }
    } else if (key === "hsPage") {
      const searchTerm = filters[0].selectedValue === "All" ? "" : filters[0].selectedValue.trim();
      const data = {
        code: searchTerm,
        page: next.hscode + 1,
        perPage: tablePagination.perPage,
      };
      const res = await dispatch(EquipmentHSCodeThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setLists({
          ...lists,
          hscode: [...lists.hscode, ...res.payload.data.data],
        });
        setNext({
          ...next,
          hscode: res.payload.data.data.length > 0 ? next.hscode + 1 : next.hscode,
        });
      }
    } else if (key === "certificatePage") {
      const data = new URLSearchParams({
        page: next.certificatetype + 1,
        perPage: tablePagination.perPage,
      });
      const res = await dispatch(certificateListTypeThunk(data));
      if (res.payload.status === 200) {
        setLists({
          ...lists,
          certificatetype: [...lists.certificatetype, ...res.payload.data.data],
        });
        setNext({
          ...next,
          certificatetype:
            res.payload.data.data.length > 0 ? next.certificatetype + 1 : next.certificatetype,
        });
      }
    } else if (key === "currencyUnitPage") {
      const data = {
        page: next.currencyUnit + 1,
        perPage: tablePagination.perPage,
      };
      const res = await dispatch(currencyunitListThunk(paramCreater(data)));

      if (res.payload.status === 200) {
        setNext({
          ...next,
          currencyUnit:
            res.payload.data.data.length > 0 ? next.currencyUnit + 1 : next.currencyUnit,
        });
      }
    } else if (key === "quantityTypePage") {
      const data = {
        page: next.quantityType + 1,
        perPage: tablePagination.perPage,
      };
      const res = await dispatch(quantitytypeListThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setNext({
          ...next,
          quantityType:
            res.payload.data.data.length > 0 ? next.quantityType + 1 : next.quantityType,
        });
      }
    } else if (key === "weightPage") {
      const data = {
        page: next.weightForm + 1,
        perPage: tablePagination.perPage,
      };
      const res = await dispatch(weightformListThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setNext({
          ...next,
          weightForm: res.payload.data.data.length > 0 ? next.weightForm + 1 : next.weightForm,
        });
      }
    } else if (key === "ceNormPage") {
      const data = {
        page: next.ceNorms + 1,
        perPage: tablePagination.perPage,
      };
      const res = await dispatch(ceNormListThunk(paramCreater(data)));
      if (res.payload.status === 200) {
        setNext({
          ...next,
          ceNorms: res.payload.data.data.length > 0 ? next.ceNorms + 1 : next.ceNorms,
        });
      }
    }
  };

  const handleSearch = async (apiThunk, propertyKey, sortKey, searchValue = filters) => {
    setFilters((prev) => {
      const temp = [...prev];
      temp[0].isLoading = true;
      return temp;
    });

    setTablePagination({ ...tablePagination, page: 0 });

    const searchTerm =
      searchValue[0].selectedValue === "All" ? "" : searchValue[0].selectedValue.trim();
    const data = {
      [propertyKey === "code" ? "code" : "name"]: searchTerm,
      page: 0,
      perPage: tablePagination.perPage,
      sortOrder: sortKey || "",
    };

    const res = await dispatch(apiThunk(paramCreater(data)));
    const temp = [...filters];
    const typeName = res.payload.data.data.map((item) => ({
      [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
      title: item[propertyKey],
    }));
    temp[0].list = [{ [Constants.MONGOOSE_ID]: "all", title: "All" }, ...typeName];
    temp[0].isLoading = false;
    setFilters(temp);
  };

  const debounce =
    (func, delay) =>
    (...args) => {
      const context = this;
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => func.apply(context, args), delay);
    };

  const createDebouncedHandleSearch = (apiThunk, propertyKey, sortKey) =>
    debounce((e) => {
      setTablePagination({ ...tablePagination, page: 0 });
      setNext({ group: 0, equiptype: 0, hscode: 0, ceNorms: 0 });
      const temp = [...filters];
      const searchTerm = e.target.value.trim();

      if (searchTerm.length < 3) {
        temp[0].selectedValue = searchTerm;
        setFilters(temp);
        return;
      }
      temp[0].selectedValue = searchTerm;
      handleSearch(apiThunk, propertyKey, sortKey, temp);
    }, 300);

  const debouncedHandleSearchEquipmentType = createDebouncedHandleSearch(
    EquipmentTypeThunk,
    "type",
    sortTypeName
  );
  const debouncedHandleSearchEquipmentCategory = createDebouncedHandleSearch(
    EquipmentCategoryThunk,
    "name",
    sortCategoryName
  );
  const debouncedHandleSearchHSCode = createDebouncedHandleSearch(EquipmentHSCodeThunk, "code");
  const debouncedHandleSearchCENorm = createDebouncedHandleSearch(ceNormListThunk, "name");

  return (
    <DashboardLayout>
      <MDBox>
        <DashboardNavbar />
        <MDBox display="flex" justifyContent="space-between">
          <PageTitle title={PageTitles.EQUIPMENT_SETUP} />
          <CustomButton
            key="equipment-sample-file-export"
            title={ButtonTitles.DOWNLOAD_IMPORT_SAMPLE}
            icon={Icons.DOWNLOAD2}
            background={Colors.PRIMARY}
            color={Colors.WHITE}
            openModal={() =>
              handleEquipmentSampleFileExport(
                process.env.REACT_APP_EQUIPMENT_TYPE_IMPORT_SAMPLE_FILE_NAME,
                process.env.REACT_APP_EQUIPMENT_TYPE_IMPORT_SAMPLE_FILE_NAME.split(".")[0]
              )
            }
          />
        </MDBox>
      </MDBox>
      <Feature name={FeatureTags.SETUP_EQUIPMENT}>
        <NewProject
          title={modalType === "New" ? "New Equipment Category" : "Edit Equipment Category"}
          openNewProject={openEquipmentGroup}
          handleCloseNewProject={() => handleCloseNewModal("Equipment Category")}
          handleSave={() => (modalType === "New" ? handleCreate("group") : handleUpdate("group"))}
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Name*"
            placeholder="Name"
            id="name"
            name="group"
            type="text"
            error={Boolean(error.group.name)}
            helperText={error.group.name}
            value={body.group?.name}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FTextField
            label="Equipment Nr. Abbreviation*"
            placeholder="Equipment Nr. Abbreviation"
            id="abbreviation"
            name="group"
            type="text"
            value={body.group?.abbreviation}
            error={Boolean(error.group.abbreviation)}
            helperText={error.group.abbreviation}
            handleChange={handleChange}
            marginBottom={2}
          />
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New Equipment Type" : "Edit Equipment Type"}
          openNewProject={openEquipmentType}
          handleCloseNewProject={() => handleCloseNewModal("Equipment Type")}
          handleSave={() =>
            modalType === "New" ? handleCreate("equiptype") : handleUpdate("equiptype")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={
            disableSubmit ||
            (modalType === "New" && defaultCurrency?.length === 0 && !body?.equiptype?.currencyUnit)
          }
        >
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <MDTypography
                variant="caption"
                sx={{
                  fontSize,
                  color: "#344054",
                }}
              >
                Name*
              </MDTypography>
              <MDInput
                sx={{
                  marginTop: pxToRem(3),
                  "& input": {
                    fontSize: "16px",
                    color: "#667085",
                  },
                }}
                id="type"
                name="equiptype"
                placeholder="Name"
                error={Boolean(error.equiptype.type)}
                helperText={error.equiptype.type}
                FormHelperTextProps={{
                  sx: { marginLeft: 0, color: "#FF2E2E" },
                }}
                fullWidth
                defaultValue={modalType === "New" ? null : editLists.equiptype?.type}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={6}>
              <FDropdown
                label="Equipment Category*"
                id="equipmentCategory"
                name="equiptype"
                value={body?.equiptype?.equipmentCategory}
                menu={dropdownData.group}
                error={error.equiptype.equipmentCategory}
                helperText={error.equiptype.equipmentCategory}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
              />
            </Grid>
            <Grid item xs={6}>
              <FDropdown
                label="Weight Form*"
                id="equipmentUnit"
                name="equiptype"
                value={body?.equiptype?.equipmentUnit}
                menu={dropdownData.weightForm}
                error={error.equiptype.equipmentUnit}
                helperText={error.equiptype.equipmentUnit}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
              />
            </Grid>
            <Grid item xs={6}>
              <MDTypography variant="caption" mt={2} mb={1} sx={{ fontSize, color: "#344054" }}>
                Rental Price*
              </MDTypography>
              <MDInput
                sx={{
                  marginTop: 0,
                  "& input": {
                    fontSize: "16px",
                    color: "#667085",
                  },
                }}
                id="price"
                name="equiptype"
                type="number"
                placeholder="Rental Price"
                margin="normal"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <MDBox sx={{ marginLeft: "-15px" }}>
                        <FDropdown
                          id="currencyUnit"
                          name="equiptype"
                          value={body?.equiptype?.currencyUnit || defaultCurrency}
                          menu={dropdownData.currencyUnit}
                          handleChange={(name, value, id) =>
                            handleChange({ target: { name, value, id } })
                          }
                          border="1px solid #FFFFFF"
                          maxWidth={pxToRem(100)}
                          height={pxToRem(47)}
                          marginBottom={0.5}
                        />
                      </MDBox>
                    </InputAdornment>
                  ),
                }}
                error={Boolean(error.equiptype.price)}
                helperText={error.equiptype.price}
                FormHelperTextProps={{
                  sx: { marginLeft: 0, color: "#FF2E2E" },
                }}
                onKeyDown={(e) => numericFieldValidation(e)}
                fullWidth
                defaultValue={modalType === "New" ? null : editLists.equiptype?.price}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={6}>
              <FDropdown
                label="Quantity Type*"
                id="quantityType"
                name="equiptype"
                value={body?.equiptype?.quantityType}
                menu={dropdownData.quantityType}
                error={error.equiptype.quantityType}
                helperText={error.equiptype.quantityType}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
                marginBottom={2}
              />
            </Grid>
            <Grid item xs={6}>
              <FDropdown
                label="HS Code*"
                id="hsCode"
                name="equiptype"
                value={body?.equiptype?.hsCode}
                menu={dropdownData.hscode}
                error={Boolean(error.equiptype?.hsCode)}
                helperText={error.equiptype?.hsCode}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
                marginBottom={2}
              />
            </Grid>
            <Grid item xs={6}>
              <FDropdown
                label="CE Norm*"
                id="ceNorms"
                name="equiptype"
                value={body?.equiptype?.ceNorms}
                menu={dropdownData.ceNorms}
                error={Boolean(error.equiptype?.ceNorms)}
                helperText={error.equiptype?.ceNorms}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
                marginBottom={2}
              />
            </Grid>
            <Grid item xs={6} mb={2}>
              <MultiSelectDropdown
                label="Certificate Type"
                name="equiptype"
                id="certificateTypes"
                menu={dropdownData.certificateType}
                values={body?.equiptype?.certificateTypes || []}
                handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
              />
            </Grid>
            <FormGroup error={error.equiptype.showOnDpr} sx={{ marginLeft: "18px" }}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="showOnDpr"
                    defaultChecked={modalType === "New" ? null : editLists.equiptype?.showOnDpr}
                    onChange={(e) => handleCheckboxChange("equiptype", e, modalType)}
                  />
                }
                label="Show Equipment On DPR"
              />
              <FormHelperText sx={{ marginLeft: 0, color: "#FF2E2E" }}>
                {error.currencyUnit.isDefault}
              </FormHelperText>
            </FormGroup>
          </Grid>
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New HS Code" : "Edit HS Code"}
          openNewProject={openHSCode}
          handleCloseNewProject={() => handleCloseNewModal("HS Code")}
          handleSave={() => (modalType === "New" ? handleCreate("hscode") : handleUpdate("hscode"))}
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="HS Name*"
            placeholder="HS Name"
            id="name"
            name="hscode"
            type="text"
            value={body.hscode?.name}
            error={Boolean(error.hscode.name)}
            helperText={error.hscode.name}
            handleChange={handleChange}
            marginBottom={2}
          />

          <FTextField
            label="HS Code*"
            placeholder="HS Code"
            id="code"
            name="hscode"
            type="text"
            value={body.hscode?.code}
            error={Boolean(error.hscode.code)}
            helperText={error.hscode.code}
            handleChange={handleChange}
            marginBottom={2}
          />
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New Certificate Type" : "Edit Certificate Type"}
          openNewProject={openCertificateType}
          handleCloseNewProject={() => handleCloseNewModal("Certificate Type")}
          handleSave={() =>
            modalType === "New" ? handleCreate("certificatetype") : handleUpdate("certificatetype")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Name*"
            placeholder="Name"
            id="title"
            name="certificatetype"
            type="text"
            error={Boolean(error.certificatetype.title)}
            helperText={error.certificatetype.title}
            value={body.certificatetype?.title}
            handleChange={handleChange}
            marginBottom={2}
          />
          <CustomRadio
            label="You want to add validity date?"
            name="isValidityDate"
            list={[
              { label: "Yes", value: "true" },
              { label: "No", value: "false" },
            ]}
            value={body?.certificatetype?.isValidityDate}
            handleChange={(e) => handleRadioChange("certificatetype", e)}
          />
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New Weight Form" : "Edit Weight Form"}
          openNewProject={openWeightForm}
          handleCloseNewProject={() => handleCloseNewModal("Weight Form")}
          handleSave={() =>
            modalType === "New" ? handleCreate("weightForm") : handleUpdate("weightForm")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Weight Form*"
            placeholder="Weight Form"
            id="title"
            name="weightForm"
            type="text"
            error={Boolean(error.weightForm.title)}
            helperText={error.weightForm.title}
            value={body.weightForm?.title}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FTextField
            label="Weight Abbrevation*"
            placeholder="Weight Abbrevation"
            id="abbreviation"
            name="weightForm"
            type="text"
            error={Boolean(error.weightForm.abbreviation)}
            helperText={error.weightForm.abbreviation}
            value={body.weightForm?.abbreviation}
            handleChange={handleChange}
            marginBottom={2}
          />
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New Quantity Type" : "Edit Quantity Type"}
          openNewProject={openQuantityType}
          handleCloseNewProject={() => handleCloseNewModal("Quantity Type")}
          handleSave={() =>
            modalType === "New" ? handleCreate("quantityType") : handleUpdate("quantityType")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Name*"
            placeholder="Name"
            id="name"
            name="quantityType"
            type="text"
            error={Boolean(error.quantityType.name)}
            helperText={error.quantityType.name}
            value={body.quantityType?.name}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FDropdown
            label="Price*"
            id="priceType"
            name="quantityType"
            value={body?.quantityType?.priceType}
            menu={["buy", "rental"]}
            error={error.quantityType.priceType}
            helperText={error.quantityType.priceType}
            handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
            marginBottom={2}
          />
          <FDropdown
            label="Quantity Type*"
            id="quantityType"
            name="quantityType"
            value={body?.quantityType?.quantityType}
            menu={["unique", "multiple"]}
            error={error.quantityType.quantityType}
            helperText={error.quantityType.quantityType}
            handleChange={(name, value, id) => handleChange({ target: { name, value, id } })}
            marginBottom={2}
          />
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New Currency Unit" : "Edit Currency Unit"}
          openNewProject={openCurrencyUnit}
          handleCloseNewProject={() => handleCloseNewModal("Currency Unit")}
          handleSave={() =>
            modalType === "New" ? handleCreate("currencyUnit") : handleUpdate("currencyUnit")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Name*"
            placeholder="Name"
            id="name"
            name="currencyUnit"
            type="text"
            error={Boolean(error.currencyUnit.name)}
            helperText={error.currencyUnit.name}
            value={body.currencyUnit?.name}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FTextField
            label="Currency Symbol*"
            placeholder="Currency Symbol"
            id="symbol"
            name="currencyUnit"
            type="text"
            error={Boolean(error.currencyUnit.symbol)}
            helperText={error.currencyUnit.symbol}
            value={body.currencyUnit?.symbol}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FormGroup error={error.currencyUnit.isDefault}>
            <FormControlLabel
              control={
                <Checkbox
                  name="isDefault"
                  defaultChecked={modalType === "New" ? null : editLists.currencyUnit?.isDefault}
                  onChange={(e) => handleCheckboxChange("currencyUnit", e, modalType)}
                />
              }
              label="isDefault"
            />
            <FormHelperText sx={{ marginLeft: 0, color: "#FF2E2E" }}>
              {error.currencyUnit.isDefault}
            </FormHelperText>
          </FormGroup>
        </NewProject>
        <NewProject
          title={modalType === "New" ? "New CE Norm" : "Edit CE Norm"}
          openNewProject={openCENorm}
          handleCloseNewProject={() => handleCloseNewModal("CE Norm")}
          handleSave={() =>
            modalType === "New" ? handleCreate("ceNorms") : handleUpdate("ceNorms")
          }
          actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
          disabled={disableSubmit}
        >
          <FTextField
            label="Name*"
            placeholder="Name"
            id="name"
            name="ceNorms"
            type="text"
            error={Boolean(error.ceNorms.name)}
            helperText={error.ceNorms.name}
            value={body.ceNorms?.name}
            handleChange={handleChange}
            marginBottom={2}
          />
          <FTextField
            label="Months*"
            placeholder="Months"
            id="month"
            name="ceNorms"
            type="number"
            error={Boolean(error.ceNorms.month)}
            helperText={error.ceNorms.month}
            value={body.ceNorms?.month}
            onKeyPress={(e) => numericFieldValidation(e)}
            handleChange={handleChange}
            marginBottom={2}
          />
        </NewProject>
        <ViewDrawer
          title="Equipment Category"
          openDrawer={openEquipmentGroupData}
          closeDrawer={handleCloseEquipmentGroupData}
        >
          <MDBox mb={2} mt={-3}>
            <SearchBar
              freeSolos
              options={filters[0]?.list.map((val) => val.title) || []}
              filters={filters}
              placeholder="Search"
              value={filters[0].selectedValue}
              debouncedHandleSearch={debouncedHandleSearchEquipmentCategory}
              handleFilterChange={(e, value) =>
                debouncedHandleSearchEquipmentCategory({
                  target: { name: filters[0]?.inputLabel, value },
                })
              }
              isLoading={filters[0].isLoading}
            />
          </MDBox>
          <DataTable
            table={{
              columns: groupColumns,
              rows: groupRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.group}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("groupPage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer
          title="Equipment Type"
          openDrawer={openEquipmentTypeData}
          closeDrawer={handleCloseEquipmentTypeData}
          drawerWidth="90%"
        >
          <MDBox mb={2} mt={-3}>
            <SearchBar
              freeSolos
              options={filters[0]?.list.map((val) => val.title) || []}
              filters={filters}
              placeholder="Search"
              value={filters[0].selectedValue}
              debouncedHandleSearch={debouncedHandleSearchEquipmentType}
              handleFilterChange={(e, value) =>
                debouncedHandleSearchEquipmentType({
                  target: { name: filters[0]?.inputLabel, value },
                })
              }
              isLoading={filters[0].isLoading}
            />
          </MDBox>
          <DataTable
            table={{
              columns: typeColumns,
              rows: typeRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.equiptype}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("typePage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer title="HS Code" openDrawer={openHSCodeData} closeDrawer={handleCloseHSCodeData}>
          <MDBox mb={2} mt={-3}>
            <SearchBar
              freeSolos
              options={filters[0]?.list.map((val) => val.title) || []}
              filters={filters}
              placeholder="Search"
              value={filters[0].selectedValue}
              debouncedHandleSearch={debouncedHandleSearchHSCode}
              handleFilterChange={(e, value) =>
                debouncedHandleSearchHSCode({ target: { name: filters[0]?.inputLabel, value } })
              }
              isLoading={filters[0].isLoading}
            />
          </MDBox>
          <DataTable
            table={{
              columns: hscodeColumns,
              rows: hscodeRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.hscode}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("hsPage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer
          title="Certificate Type"
          openDrawer={openCertificateTypeData}
          closeDrawer={handleCloseCertificateTypeData}
        >
          <DataTable
            table={{
              columns: certificateColumns,
              rows: certificateRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.certificatetype}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("certificatePage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer
          title="Weight Form"
          openDrawer={openWeightFormData}
          closeDrawer={handleCloseOpenWeightFormData}
        >
          <DataTable
            table={{
              columns: weightFormColumns,
              rows: weightFormRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.weightForm}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("weightPage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer
          title="Quantity Type"
          openDrawer={openQuantityTypeData}
          closeDrawer={handleCloseOpenQuantityTypeData}
        >
          <DataTable
            table={{
              columns: quantityTypeColumns,
              rows: quantityTypeRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.quantityType}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("quantityTypePage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer
          title="Currency Unit"
          openDrawer={openCurrencyUnitData}
          closeDrawer={handleCloseCurrencyUnitData}
        >
          <DataTable
            table={{
              columns: currencyUnitColumns,
              rows: currencyUnitRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.currencyUnit}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("currencyUnitPage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <ViewDrawer title="CE Norm" openDrawer={openCENormData} closeDrawer={handleCloseCENormData}>
          <MDBox mb={2} mt={-3}>
            <SearchBar
              freeSolos
              options={filters[0]?.list.map((val) => val.title) || []}
              filters={filters}
              placeholder="Search"
              value={filters[0].selectedValue}
              debouncedHandleSearch={debouncedHandleSearchCENorm}
              handleFilterChange={(e, value) =>
                debouncedHandleSearchCENorm({
                  target: { name: filters[0]?.inputLabel, value },
                })
              }
              isLoading={filters[0].isLoading}
            />
          </MDBox>
          <DataTable
            table={{
              columns: ceNormColumns,
              rows: ceNormRows,
            }}
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingStatus.ceNorms}
            currentPage={tablePagination.page}
            handleTablePagination={() => handleTablePagination("ceNormPage")}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
            licenseRequired
          />
        </ViewDrawer>
        <MDBox py={3}>
          <Grid container spacing={3} sx={{ display: "flex" }}>
            {cardList.map((val) => (
              <Grid
                item
                xs={12}
                sm={6}
                md={6}
                lg={4}
                key={val.cardTitle}
                sx={{ display: "flex", minWidth: 0 }}
              >
                <MDBox mb={1.5} sx={{ width: "100%" }}>
                  <Card
                    style={{
                      boxShadow: "none",
                      border: "1px solid #E0E6F5",
                      minHeight: "200px",
                      display: "flex",
                      flexDirection: "column",
                      height: "100%",
                      width: "100%",
                    }}
                  >
                    <MDBox sx={{ flex: "1 1 auto" }}>
                      <MDBox display="flex" justifyContent="space-between" pt={1} px={2}>
                        <MDBox textAlign="right" lineHeight={1.25}>
                          <MDTypography variant="h4" fontWeight="bold" color="text">
                            {val.cardTitle}
                          </MDTypography>
                        </MDBox>
                      </MDBox>
                      <MDBox mt={2} ml={1} sx={{ flex: "1 1 auto" }}>
                        <MDTypography fontWeight="light" variant="body2" m={1}>
                          {val.content}
                        </MDTypography>
                      </MDBox>
                    </MDBox>
                    <MDBox display="flex" justifyContent="flex-end" pt={1} px={2} pb={2}>
                      <BasicButton
                        title="View List"
                        background={Colors.WHITE}
                        border
                        borderColor={Colors.PRIMARY}
                        icon={Icons.VIEW}
                        color={Colors.PRIMARY}
                        fontWeight="bold"
                        action={() => handleViewModal(val.cardTitle)}
                      />
                      <BasicButton
                        title="Add"
                        background={Colors.WHITE}
                        border
                        borderColor={Colors.PRIMARY}
                        icon={Icons.ADD}
                        color={Colors.PRIMARY}
                        fontWeight="bold"
                        action={() => handleOpenNewModal(val.cardTitle)}
                      />
                    </MDBox>
                  </Card>
                </MDBox>
              </Grid>
            ))}
          </Grid>
        </MDBox>
        <DeleteModal
          open={deleteData.openDeleteModal}
          title={`Delete ${deleteMessage(deleteData.type)}`}
          message="Are you sure you want to delete?"
          handleClose={handleCloseDeleteModal}
          handleDelete={handleOpenDeleteModal}
        />
      </Feature>
    </DashboardLayout>
  );
}

const setupEquipmentComponent = ExportHOC(SetupEquipment);
export default setupEquipmentComponent;

// Material Dashboard 2 React components
import { Grid } from "@mui/material";
import MDBox from "components/MDBox";

// Components
import pxToRem from "assets/theme/functions/pxToRem";
import ConfigDropdown from "components/Dropdown/ConfigDropdown";
import DateTime from "components/DateTime/DateTime";
import FTextField from "components/Form/FTextField";

// libraries
import ReactDatePicker from "react-datepicker";
import moment from "moment";
import PropTypes from "prop-types";

// Redux
import { useSelector } from "react-redux";

// utils
import Constants, { defaultData, Common, BackendFrontend } from "utils/Constants";
import MDTypography from "components/MDTypography";
import { useEffect } from "react";

function WarehouseInfoTab({
  equipmentId,
  warehouseData,
  setWarehouseData,
  searchData,
  setUpdatedBody,
  quantityType,
}) {
  const configData = useSelector((state) => state.config);
  const warehouseInfoFields = configData?.equipmentConfig?.properties?.warehouseInfo || [];

  const handleConfigChange = (name, value) => {
    setWarehouseData((prevData) => ({ ...prevData, [name]: value }));

    if (name.includes(BackendFrontend.DATE)) {
      setWarehouseData((prevData) => ({
        ...prevData,
        [name]: name.includes(BackendFrontend.DATE) && value,
      }));
    }
    setUpdatedBody((prevData) => ({ ...prevData, [name]: value }));
  };

  useEffect(() => {
    if (equipmentId === "" || !equipmentId) {
      configData?.equipmentConfig?.properties?.warehouseInfo?.forEach((item) => {
        if (item?.type === BackendFrontend.OPTIONS && item?.id === Common.WAREHOUSE) {
          handleConfigChange(
            item?.id,
            item?.options?.length === 1
              ? item?.options?.[0]?.id
              : searchData?.warehouse?.[Constants.MONGOOSE_ID]
          );
        } else if (item?.type === BackendFrontend.NUMBER && item?.id === Common.QUANTITY) {
          handleConfigChange(item?.id, quantityType === true ? 1 : warehouseData?.[item?.id]);
        } else if (item?.id === Common.CONDITION) {
          handleConfigChange(item?.id, "ok");
        }
      });
    }
  }, [equipmentId, quantityType]);

  return (
    <MDBox display="flex" flexDirection="column" gap={1}>
      <MDBox>
        <MDTypography
          sx={{
            fontSize: pxToRem(20),
            fontWeight: 600,
            color: "#667085",
          }}
        >
          Warehouse Info
        </MDTypography>
      </MDBox>

      <Grid container spacing={1} sx={{ display: "flex", justifyContent: "start" }}>
        {warehouseInfoFields.length > 0
          ? warehouseInfoFields?.map((item) => {
              const fieldValue = warehouseData[item.id] || "";
              switch (item.type) {
                case BackendFrontend.TEXT:
                  return (
                    <Grid item lg={6} sm={12} key={item.id}>
                      <FTextField
                        label={item?.IsRequired ? `${item.title}*` : item.title}
                        placeholder={item?.hint}
                        name={item?.id}
                        id={item?.id}
                        type="text"
                        value={fieldValue}
                        error={Boolean(warehouseData?.errors[item.id])}
                        helperText={warehouseData?.errors[item.id]}
                        handleChange={(e) => handleConfigChange(item?.id, e.target.value, item?.id)}
                      />
                    </Grid>
                  );

                case BackendFrontend.OPTIONS:
                  return (
                    <Grid item lg={item?.id === "warehouse" ? 12 : 6} sm={12} key={item.id}>
                      <ConfigDropdown
                        label={item?.IsRequired ? `${item.title}*` : item.title}
                        menu={item?.options.map((val) => ({
                          [Constants.MONGOOSE_ID]: val?.id,
                          title: val?.title,
                        }))}
                        name={item?.id}
                        id={item?.id}
                        error={warehouseData.errors && warehouseData.errors[item.id]}
                        helperText={warehouseData.errors && warehouseData.errors[item.id]}
                        defaultValue=""
                        value={fieldValue}
                        handleChange={(e, value, id) => handleConfigChange(e, value, id)}
                      />
                    </Grid>
                  );

                case BackendFrontend.DATE:
                  return (
                    <Grid item lg={6} sm={12} key={item.id}>
                      <ReactDatePicker
                        selected={
                          (warehouseData?.[item.id]
                            ? moment(warehouseData?.[item.id].split(".")[0]).toDate()
                            : new Date()) || null
                        }
                        onChange={(date) =>
                          handleConfigChange(
                            item.id,
                            moment(date).format(defaultData.DATABSE_DATE_FORMAT).toString(),
                            item.questionId ? item.questionId : item.id
                          )
                        }
                        customInput={
                          <DateTime
                            item={item}
                            value={fieldValue}
                            label={`${item?.title}${item?.IsRequired ? "*" : ""}`}
                            errors={warehouseData.errors?.[item.id]}
                            helperText={warehouseData.errors?.[item.id]}
                            placeholder={Common.DATE_FORMAT_MM_DD_YYYY}
                          />
                        }
                        placeholderText={Common.DATE_FORMAT_MM_DD_YYYY}
                        dateFormat={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
                      />
                    </Grid>
                  );

                case BackendFrontend.NUMBER:
                  return (
                    <Grid item lg={6} sm={12} key={item.id}>
                      <FTextField
                        label={item?.IsRequired ? `${item.title}*` : item.title}
                        placeholder={item?.hint}
                        name={item?.id}
                        id={item?.id}
                        value={
                          !equipmentId && item?.id === Common.QUANTITY && quantityType === true
                            ? 1
                            : warehouseData[item.id]
                        }
                        type="number"
                        disabled={
                          (equipmentId && item?.id === Common.QUANTITY) ||
                          (item?.id === Common.QUANTITY && quantityType === true)
                        }
                        error={Boolean(warehouseData?.errors[item.id])}
                        helperText={warehouseData?.errors[item.id]}
                        handleChange={(e) => handleConfigChange(item?.id, e.target.value, item?.id)}
                      />
                    </Grid>
                  );
                default:
                  return null;
              }
            })
          : null}
      </Grid>
    </MDBox>
  );
}

WarehouseInfoTab.propTypes = {
  warehouseData: PropTypes.objectOf(PropTypes.any).isRequired,
  setWarehouseData: PropTypes.func.isRequired,
  equipmentId: PropTypes.bool.isRequired,
  setUpdatedBody: PropTypes.func.isRequired,
  searchData: PropTypes.objectOf(PropTypes.any).isRequired,
  quantityType: PropTypes.bool.isRequired,
};

export default WarehouseInfoTab;

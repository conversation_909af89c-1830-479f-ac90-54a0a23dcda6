import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import ApiService from "redux/ApiService/ApiService";
import { filterProjectStatusFunc, normalizeParamsAndAddValues } from "utils/methods/methods";

export const dprListThunk = createAsyncThunk("dpr-list/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`dpr?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const createDprThunk = createAsyncThunk("create-dpr/api", async (body) => {
  const res = await ApiService.post(
    "dpr",
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((error) => error.response);
  return res;
});

export const getContactDetailsDPR = createAsyncThunk("dpr-contact-details/api", async (body) => {
  const res = await ApiService.get(`dpr/dpr-members/${body}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const getQHSEDprData = createAsyncThunk("dpr-qhse/api", async (body) => {
  const res = await ApiService.get(`safety-cards/${body}/summary`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const getPersonnelDprListData = createAsyncThunk("dpr-personnel/api", async (body) => {
  const res = await ApiService.get(`shifts/activities/${body}/members-summary`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const getDetailedTimeAnalysisData = createAsyncThunk(
  "dpr-time-analysis/api",
  async (body) => {
    const res = await ApiService.get(`shifts/activities/${body}/summary`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r.data)
      .catch((error) => error.response);
    return res;
  }
);

export const getProjectTrackerDprData = createAsyncThunk(
  "dpr-project-tracker/api",
  async (body) => {
    const res = await ApiService.get(
      `reports/project-tracker/${body.projectId}?summary=true&dprId=${body.dprId}`,
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r.data)
      .catch((error) => error.response);
    return res;
  }
);

export const getDprEquipmentData = createAsyncThunk("dpr-equipment/api", async (body) => {
  const res = await ApiService.get(`equipments/equipment-summary/${body}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const updateDprData = createAsyncThunk("update-dpr/api", async (body) => {
  const res = await ApiService.patch(
    `dpr/${body.dprId}`,
    { ...body.body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getDprDetails = createAsyncThunk("dpr-details/api", async (body) => {
  const res = await ApiService.get(`dpr/dpr-details/${body}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r.data)
    .catch((error) => error.response);
  return res;
});

export const getDprApplicationDocumentThunk = createAsyncThunk(
  "dpr-project-document/api",
  async (id) => {
    const res = await ApiService.get(`/project-documents/${id}/summary`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((error) => error.response);
    return res;
  }
);

export const getDetailsProgressData = createAsyncThunk(
  "dpr-detail-progress-data/api",
  async (id) => {
    const res = await ApiService.get(`reports/detailed-progress/${id}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r.data)
      .catch((error) => error.response);
    return res;
  }
);

export const getdailyActivityLogsData = createAsyncThunk(
  "get-daily-activity-data/api",
  async (id) => {
    const res = await ApiService.get(`shifts/activities/${id}/daily-activity-log`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r.data)
      .catch((error) => error.response);
    return res;
  }
);

export const updateLastReloadTime = createAsyncThunk(
  "update-last-reload-time/api",
  async (body) => {
    const res = await ApiService.patch(
      `dpr/${body.dprId}/reload`,
      { ...body.body },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const updateWorkingStatus = createAsyncThunk(
  "update-personnel-status-working-day/api",
  async (body) => {
    const res = await ApiService.patch(
      `/team-members/batch-update`,
      { ...body.body },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const deleteDpr = createAsyncThunk("delete-dpr/api", async (dprId) => {
  const res = await ApiService.delete(`/dpr/hard/${dprId}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

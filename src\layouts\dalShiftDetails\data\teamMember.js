/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";

// MUI Components
import { IconButton } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import Author from "components/Table/Author";

// Constants
import Constants, { Icons } from "utils/Constants";
import notInListIcon from "assets/images/icons/Shifts/file-excel-line.png";

export default function TeamMemberData(
  handleOpenTeamMember,
  handleOpenDeleteTeamMember,
  currentShift
) {
  const [rows, setRows] = useState([]);

  useEffect(() => {
    if (Object.keys(currentShift).length > 0) {
      const list = currentShift?.allMembers?.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          member: (
            <MDBox display="flex" justifyContent="start" alignItems="center">
              <MDBox
                style={{
                  flex: 1,
                  width: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                <Author name={item?.memberName} />
              </MDBox>
              {item?.notInList && (
                <MDBox display="flex" justifyContent="center" alignItems="center">
                  <img src={notInListIcon} alt="Not In List" style={{ width: 20, height: 20 }} />
                </MDBox>
              )}
            </MDBox>
          ),
          function: <Author name={item?.functionName} />,
          action: (
            <MDBox>
              <IconButton
                aria-label="fingerprint"
                color="info"
                onClick={() => handleOpenTeamMember(item, "update")}
              >
                {Icons.EDIT}
              </IconButton>
              <IconButton
                aria-label="fingerprint"
                color="error"
                onClick={() => handleOpenDeleteTeamMember(item?.[Constants.MONGOOSE_ID], item)}
              >
                {Icons.DELETE}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [currentShift]);

  return {
    teamMemberColumn: [
      { Header: "No.", accessor: "srNo", width: "7%" },
      { Header: "Member", accessor: "member", align: "left" },
      { Header: "Function", accessor: "function", align: "left" },
      { Header: "Action", accessor: "action", width: "10%", align: "left" },
    ],

    teamMemberRow: rows,
  };
}

// Material Components
import { Box } from "@mui/material";

// Material Common Components
import pxToRem from "assets/theme/functions/pxToRem";
import MDTypography from "components/MDTypography";
import MDBox from "components/MDBox";

// Utils
import Constants, { Colors, Icons } from "utils/Constants";

// 3rd party
import PropTypes from "prop-types";

const titleDimensions = {
  [Constants.RELOAD_DATE_TIME]: {
    bgColor: Colors.LIGHT_ORANGE,
    color: Colors.DARK_ORANGE,
    // width: pxToRem(105),
  },
};

export default function ReloadDateTime({ title, label, style }) {
  const { bgColor, color, width } = titleDimensions[title] || {};

  return (
    <MDBox
      bgColor={bgColor}
      color={color}
      variant="contained"
      borderRadius={pxToRem(16)}
      opacity={1}
      p={1}
      width={width}
      height={pxToRem(24)}
      textAlign="right"
      display="flex"
      alignItems="center"
      justifyContent="space-around"
      style={style}
    >
      <Box
        sx={{
          width: pxToRem(10),
          height: pxToRem(10),
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {Icons.RELOAD_DATE_TIME}
      </Box>
      <MDTypography
        ml={1}
        variant="caption"
        sx={{
          textTransform: "capitalize",
          color,
        }}
      >
        {label}
      </MDTypography>
    </MDBox>
  );
}

ReloadDateTime.propTypes = {
  title: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  style: PropTypes.shape({}),
};

ReloadDateTime.defaultProps = {
  style: {}, // Added defaultProps for 'style'
};

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import FormTextArea from "components/Form/FTextArea";
import SignaturePad from "components/SignaturePad";
import FontComponent from "components/Responsive/fonts";
import CardBreakPoint from "components/Responsive/BreakPoints";
import DynamicTypeData from "components/Table/DynamicTypeData";

// Assets
import pxToRem from "assets/theme/functions/pxToRem";

// Slice
import { setCommentOrRemarks, setNamesAndSignature } from "redux/Slice/Dpr";

function CommentAndSignaturesTab() {
  const dispatch = useDispatch();
  const CardTitleFontSize = FontComponent({ sizes: CardBreakPoint.baseTitleBreakPoint });
  const { dprData } = useSelector((state) => state.dprs);

  const initialRemarks = dprData?.commentsAndSignatures?.commentOrRemarks || {};
  const initialSignatures = dprData?.commentsAndSignatures?.namesAndSignature || {};

  const [dprCommentOrRemarks, setDprCommentOrRemarks] = useState({
    endClientRepresentative: initialRemarks.endClientRepresentative || "",
    clientRepresentative: initialRemarks.clientRepresentative || "",
    reynardRepresentative: initialRemarks.reynardRepresentative || "",
  });

  const [dprNamesAndSignature, setDprNamesAndSignature] = useState({
    ...initialSignatures,
    signEndClientRepresentative: false,
    signClientRepresentative: false,
    signReynardRepresentative: false,
  });

  const handleCommentOrRemarks = (name, value) => {
    const updatedComments = { ...dprCommentOrRemarks, [name]: value };
    setDprCommentOrRemarks(updatedComments);
    dispatch(setCommentOrRemarks(updatedComments));
  };

  const handleNamesAndSignature = (name, value) => {
    const updatedSignatures = {
      ...dprNamesAndSignature,
      [name]: value,
      signEndClientRepresentative:
        name === "EndClientRepresentative" || dprNamesAndSignature.signEndClientRepresentative,
      signClientRepresentative:
        name === "ClientRepresentative" || dprNamesAndSignature.signClientRepresentative,
      signReynardRepresentative:
        name === "ReynardRepresentative" || dprNamesAndSignature.signReynardRepresentative,
    };
    setDprNamesAndSignature(updatedSignatures);
    dispatch(setNamesAndSignature(updatedSignatures));
  };

  const renderTextArea = (label, value, name) => (
    <MDBox>
      <MDTypography sx={{ fontSize: pxToRem(16), color: "black", fontWeight: 500 }}>
        {label}
      </MDTypography>
      <FormTextArea
        value={value}
        name={name}
        placeholder="Add Description here..."
        backgroundColor="white"
        handleChange={(e) => handleCommentOrRemarks(name, e.target.value)}
      />
    </MDBox>
  );

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography
            p={pxToRem(7)}
            variant="h6"
            fontWeight="medium"
            sx={{ fontSize: CardTitleFontSize }}
          >
            Comments / Remarks
          </MDTypography>
        </MDBox>
        <MDBox pl={2}>
          {renderTextArea(
            "End Client Representative",
            dprCommentOrRemarks.endClientRepresentative,
            "endClientRepresentative"
          )}
          {renderTextArea(
            "Client Representative",
            dprCommentOrRemarks.clientRepresentative,
            "clientRepresentative"
          )}
          {renderTextArea(
            "Reynard Representative",
            dprCommentOrRemarks.reynardRepresentative,
            "reynardRepresentative"
          )}
        </MDBox>
      </Card>
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography
            p={pxToRem(7)}
            variant="h6"
            fontWeight="medium"
            sx={{ fontSize: CardTitleFontSize }}
          >
            Names and Signatures
          </MDTypography>
        </MDBox>
        <MDBox
          mb={2}
          pl={4}
          pb={2}
          sx={{
            display: "flex",
            width: "100%",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {["EndClientRepresentative", "ClientRepresentative", "ReynardRepresentative"].map(
            (rep) => (
              <MDBox pr={4} key={rep}>
                <MDTypography sx={{ fontSize: pxToRem(16), color: "black", fontWeight: 500 }}>
                  {rep.replace(/([A-Z])/g, " $1").trim()}
                </MDTypography>
                <MDBox display="flex" flexDirection="row" alignItems="center">
                  <SignaturePad
                    folderName={`${rep.replace(/([A-Z])/g, "_$1").toUpperCase()}_Signature`}
                    handleSignaturePad={(_, value) => handleNamesAndSignature(rep, value)}
                  />
                  {!dprNamesAndSignature[`sign${rep}`] &&
                    dprData?.commentsAndSignatures?.namesAndSignature[rep]?.url && (
                      <DynamicTypeData
                        type="signature"
                        data={dprData?.commentsAndSignatures?.namesAndSignature[rep]?.url}
                        imageArray={[dprData?.commentsAndSignatures?.namesAndSignature[rep]?.url]}
                      />
                    )}
                </MDBox>
              </MDBox>
            )
          )}
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default CommentAndSignaturesTab;

import React, { useEffect } from "react";
import { useLocation, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import FormTextArea from "components/Form/FTextArea";
import { openSnackbar } from "redux/Slice/Notification";

// Constants
import Constants, { Common, defaultData } from "utils/Constants";

// Thunks
import { getDprApplicationDocumentThunk } from "redux/Thunks/Dpr";

// Slice
import { setApplicableDocumentsRemarks, updateIsLatestDataApiCompleted } from "redux/Slice/Dpr";

import applicableDocumentsTabData from "../data/applicableDocumentsTabData";

function ApplicableDocumentsTab() {
  const dispatch = useDispatch();

  const location = useLocation();
  const { projectStatus } = location.state || {};

  const { id } = useParams();

  const { dprData, displayedDprTabsObj, isDprDetailReqCompleted } = useSelector(
    (state) => state.dprs
  );
  const currProjectStatus = dprData?.status || projectStatus;

  const { applicableDocColumns, applicableDocsRows } = applicableDocumentsTabData(
    dprData?.applicableDocuments?.applicableDocumentsSummary?.data ||
      dprData?.applicableDocuments?.applicableDocumentsSummary ||
      []
  );

  const getProjectDocuments = async () => {
    try {
      await dispatch(getDprApplicationDocumentThunk(id));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    if (currProjectStatus === Common.DPR_STATUS_OPEN && isDprDetailReqCompleted) {
      getProjectDocuments();
    }
  }, [currProjectStatus, isDprDetailReqCompleted]);

  useEffect(() => {
    if (displayedDprTabsObj?.applicableDocsTab > 0) {
      getProjectDocuments()
        .then(() => dispatch(updateIsLatestDataApiCompleted(true)))
        .catch(() => dispatch(updateIsLatestDataApiCompleted(false)));
    }
  }, [displayedDprTabsObj?.applicableDocsTab]);

  return (
    <MDBox width="100%">
      <Card id="progress-tab" sx={{ mt: 1, px: 2 }}>
        <MDBox p={2} width="100%" display="flex" justifyContent="space-between" alignItems="center">
          <MDTypography variant="h6" fontWeight="medium">
            Applicable Documents
          </MDTypography>
        </MDBox>
        <MDBox mb={2}>
          <Card>
            <MDBox>
              <DataTable
                table={{ columns: applicableDocColumns, rows: applicableDocsRows }}
                isSorted={false}
                entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
                showTotalEntries={false}
                noEndBorder
                loading={Constants.FULFILLED}
                licenseRequired
              />
            </MDBox>
          </Card>
        </MDBox>
        <MDBox mb={2} mt={2}>
          <MDBox pl={2}>
            <MDTypography variant="h6" fontWeight="medium">
              Remarks
            </MDTypography>
          </MDBox>

          <FormTextArea
            value={dprData?.applicableDocuments?.remarks}
            name="remarks"
            placeholder="Add Description here..."
            backgroundColor="white"
            handleChange={(e) => {
              dispatch(setApplicableDocumentsRemarks(e.target.value));
            }}
          />
        </MDBox>
      </Card>
    </MDBox>
  );
}

export default ApplicableDocumentsTab;

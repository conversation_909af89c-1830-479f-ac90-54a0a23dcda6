import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// 3rd party library
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";

import MDBox from "components/MDBox";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import FTextField from "components/Form/FTextField";
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts";
import { ModalBreakPoint } from "components/Responsive/BreakPoints";

// examples Components
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";

// Layouts
import ProjectOrdeReqItemTable from "layouts/projectOrders/data/ProjectOrdeReqItemTable";
import ProjectOrderReqTemporaryItemTable from "layouts/projectOrders/data/ProjectOrderReqTemporaryItemTable";
// Redux
import { openSnackbar } from "redux/Slice/Notification";
import { EquipmentTypeThunk } from "redux/Thunks/Equipment";

// Utils
import Constants, {
  ButtonTitles,
  FormFields,
  defaultData,
  Colors,
  Icons,
  Common,
} from "utils/Constants";
import { formatKeyName } from "utils/methods/methods";

const initialValueOfRequstedItems = {
  equipmentType: "",
  engineerRequestedQuantity: "",
  engineerComment: "",
};

const initialTemporaryItemsObj = {
  productName: "",
  engineerRequestedQuantity: "",
  engineerComment: "",
};

const initialTableData = {
  columns: [],
  rows: [],
};

function AddRequestEquipment({ addRequestedEquipmentObj, setAddRequestedEquipmentObj }) {
  const dispatch = useDispatch();
  const configData = useSelector((state) => state.config) || {};
  const userId = configData?.config?.[0]?.id;

  const equipmentTypeList = useSelector((state) => state.product.equipmentTypeList) || [];

  const [updateStateAtAddItem, setUpdateStateAtAddItem] = useState(false);
  const [projectOptionsList, setProjectOptionsList] = useState([]);
  const [equipmentOptionsList, setEquipmentOptionsList] = useState([]);
  const [requestedItemsErrors, setRequestedItemsErrors] = useState({});
  const [temporaryItemsErrors, setTemporaryItemsErrors] = useState({});

  const [requestedItemsTableData, setRequestedItemsTableData] = useState(initialTableData);
  const [temporaryItemsTableData, setTemporaryItemsTableData] = useState(initialTableData);

  const [selectedProjectId, setSelectedProjectId] = useState("");
  const [requestedItemsObj, setRequestedItemsObj] = useState(initialValueOfRequstedItems);
  const [temporaryItemsObj, setTemporaryItemsObj] = useState(initialTemporaryItemsObj);

  const deleteReqItemFunc = (equipmentType) => {
    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      equipmentOrders: prev.equipmentOrders.filter((item) => item.equipmentType !== equipmentType),
    }));
  };

  const deleteTemporaryItemsFunc = (id) => {
    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      tempOrders: prev.tempOrders.filter((item, index) => index !== id),
    }));
  };

  const { requestedItemsColumns, requestedItemsRows } = ProjectOrdeReqItemTable({
    dataList: addRequestedEquipmentObj?.equipmentOrders,
    deleteReqItemFunc,
    updateStateAtAddItem,
  });

  const { temporaryItemsColumns, temporaryItemsRows } = ProjectOrderReqTemporaryItemTable({
    dataList: addRequestedEquipmentObj?.tempOrders,
    deleteTemporaryItemsFunc,
    updateStateAtAddItem,
  });

  // setting Error Obj on handleChangeFunc for Requested Items
  const removeKeyFromRequestedItemError = (name) => {
    setRequestedItemsErrors((prev) => {
      const { [name]: _, ...rest } = prev;
      return rest;
    });
  };

  // Equipment Type List
  const getEquipmentTypeListFunc = async () => {
    try {
      await dispatch(EquipmentTypeThunk());
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  // Get Equipment Details by Id and update the requested items obj
  const getEquipmentDetailsById = (dataObj) => {
    const { equipmentType } = dataObj;
    const equipmentDetails = equipmentTypeList.find(
      (item) => item[Constants.MONGOOSE_ID] === equipmentType
    );

    if (equipmentDetails) {
      const productCategory = equipmentDetails?.equipmentCategory?.name;
      const productType = equipmentDetails?.type;
      const quantityType = equipmentDetails?.quantityType?.priceType;

      const engineerRequestedQuantity = Number(dataObj?.engineerRequestedQuantity) || 0;

      const totalAmount = Number(equipmentDetails?.price || 0) * engineerRequestedQuantity || 0;

      const updatedReqItemsObj = {
        ...dataObj,
        productCategory,
        productType,
        quantityType,
        totalAmount,
        engineerRequestedQuantity,
      };
      return updatedReqItemsObj;
    }

    return dataObj;
  };

  const createCommentObjFunc = (dataObj = {}) => {
    if (!dataObj?.engineerComment || dataObj?.engineerComment?.trim() === "") {
      return null;
    }
    const commentObj = {
      user: userId,
      time: moment().format(Common.CURRENT_TIME_ZONE_FORMAT),
      status: "pending",
      comment: dataObj.engineerComment,
    };

    return commentObj;
  };

  // Update the existing equipment type if already exists for requested Items.
  const updateExistingEquipmentTypeFunc = (existingIndex, newRquestedObj) => {
    const existingItem = addRequestedEquipmentObj?.equipmentOrders?.[existingIndex];
    const unitPrice = existingItem.totalAmount / existingItem.engineerRequestedQuantity;

    const currQuantity = Number(newRquestedObj.engineerRequestedQuantity) || 0;
    const totalQuantity = currQuantity + existingItem.engineerRequestedQuantity;

    const totalAmount = unitPrice * totalQuantity;

    const commentObj = createCommentObjFunc(newRquestedObj);
    const newCommentArr = commentObj
      ? [...existingItem.engineerComment, commentObj]
      : existingItem.engineerComment;

    const updatedItemObj = {
      ...existingItem,
      engineerRequestedQuantity: totalQuantity,
      totalAmount,
      engineerComment: newCommentArr,
    };

    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      equipmentOrders: prev.equipmentOrders.map((item, index) =>
        index === existingIndex ? updatedItemObj : item
      ),
    }));

    setRequestedItemsObj(initialValueOfRequstedItems);
  };

  // Updating the existing equipment type if already exists for temporary Items.
  const updateExistingTempItemsFunc = (existingIndex, newTemporaryItemsObj) => {
    const existingItem = addRequestedEquipmentObj?.tempOrders?.[existingIndex];

    const currQuantity = Number(newTemporaryItemsObj.engineerRequestedQuantity) || 0;
    const existingQuantity = Number(existingItem.engineerRequestedQuantity) || 0;

    const totalQuantity = currQuantity + existingQuantity;

    const commentObj = createCommentObjFunc(newTemporaryItemsObj);

    const newCommentArr = commentObj
      ? [...existingItem.engineerComment, commentObj]
      : existingItem.engineerComment;

    const updatedItemObj = {
      ...existingItem,
      engineerRequestedQuantity: totalQuantity,
      engineerComment: newCommentArr,
    };

    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      tempOrders: prev.tempOrders.map((item, index) =>
        index === existingIndex ? updatedItemObj : item
      ),
    }));

    setTemporaryItemsObj(initialTemporaryItemsObj);
  };

  // Checking the existing equipment type if already exists for temporary Items.
  const checkingExistingTempDataFunc = (newTemporaryItemsObjData = {}) => {
    const updatedQuantity = Number(newTemporaryItemsObjData.engineerRequestedQuantity) || 0;

    const newTemporaryItemsObj = {
      ...newTemporaryItemsObjData,
      engineerRequestedQuantity: updatedQuantity,
    };

    const isEquipmentTypeExist = addRequestedEquipmentObj?.tempOrders?.findIndex(
      (item) =>
        item?.productName &&
        item.productName?.toLowerCase() === newTemporaryItemsObj.productName?.toLowerCase()
    );

    // If the product name already exists, update the quantity and comment
    if (isEquipmentTypeExist !== -1) {
      updateExistingTempItemsFunc(isEquipmentTypeExist, newTemporaryItemsObj);
      return;
    }

    const commentObj = createCommentObjFunc(newTemporaryItemsObj);
    const newCommentArr = commentObj ? [commentObj] : [];

    const updatedItemObj = {
      ...newTemporaryItemsObj,
      engineerComment: newCommentArr,
    };

    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      tempOrders: [updatedItemObj, ...prev.tempOrders],
    }));
    setTemporaryItemsObj(initialTemporaryItemsObj);
  };

  const handleFormValueChange = (e, type = "") => {
    const { name, value } = e.target;

    if (type === Common.TEMPORARY_ITEMS_TYPES_TEXT) {
      setTemporaryItemsObj((prev) => ({ ...prev, [name]: value }));
      setTemporaryItemsErrors((prev) => {
        const { [name]: _, ...rest } = prev;
        return rest;
      });
      return;
    }

    if (name === Common.PROJECT_ORDER_PROJECT_NAME) {
      setSelectedProjectId(value);
      removeKeyFromRequestedItemError(name);
      setTemporaryItemsErrors((prev) => {
        const { [name]: _, ...rest } = prev;
        return rest;
      });
      return;
    }

    setRequestedItemsObj((prev) => ({ ...prev, [name]: value }));
    removeKeyFromRequestedItemError(name);
  };

  const validateFormDataFunc = (formDataObj = {}, type) => {
    // Making comment optional by removing it from formData.
    const { engineerComment, ...formData } = formDataObj;
    const errors = {};

    Object.keys(formData).forEach((key) => {
      const value = formData[key];

      // If the value is a string, trim and check if it's empty
      if (typeof value === "string" && value.trim() === "") {
        errors[key] = `${formatKeyName(key)} is required`;
      }
      // If the value is null or undefined (but allow numbers including 0)
      else if (value === null || value === undefined) {
        errors[key] = `${formatKeyName(key)} is required`;
      }
    });

    if (type === Common.TEMPORARY_ITEMS_TYPES_TEXT) {
      setTemporaryItemsErrors((prev) => ({ ...prev, ...errors }));
      return Object.keys(errors).length === 0;
    }

    setRequestedItemsErrors((prev) => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  // Add Requested Equipment when user click on add item button
  const addRequestedEquipmentFunc = (type = "") => {
    const newRquestedObj = { ...requestedItemsObj, project: selectedProjectId };
    const newTemporaryItemsObj = { ...temporaryItemsObj, project: selectedProjectId };

    const data = type === Common.TEMPORARY_ITEMS_TYPES_TEXT ? newTemporaryItemsObj : newRquestedObj;

    const validateData = validateFormDataFunc(data, type);

    if (!validateData) {
      dispatch(
        openSnackbar({
          message: Constants.FILL_ALL_REQUIRED_FIELDS,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }

    if (type === Common.TEMPORARY_ITEMS_TYPES_TEXT) {
      checkingExistingTempDataFunc(newTemporaryItemsObj);

      setUpdateStateAtAddItem((prev) => !prev);
      return;
    }

    const isEquipmentTypeExist = addRequestedEquipmentObj?.equipmentOrders?.findIndex(
      (item) => item.equipmentType === newRquestedObj.equipmentType
    );

    // If the equipment type already exists, update the quantity, total amount and comment
    if (isEquipmentTypeExist !== -1) {
      updateExistingEquipmentTypeFunc(isEquipmentTypeExist, newRquestedObj);
      setUpdateStateAtAddItem((prev) => !prev);
      return;
    }

    const equipmentDetailsObj = getEquipmentDetailsById(newRquestedObj);
    const commentObj = createCommentObjFunc(newRquestedObj);
    const newCommentArr = commentObj ? [commentObj] : [];

    const newEquipmentDetailsObj = {
      ...equipmentDetailsObj,
      engineerComment: newCommentArr,
    };

    setAddRequestedEquipmentObj((prev) => ({
      ...prev,
      equipmentOrders: [newEquipmentDetailsObj, ...prev.equipmentOrders],
    }));
    setRequestedItemsObj(initialValueOfRequstedItems);
    setUpdateStateAtAddItem((prev) => !prev);
  };

  useEffect(() => {
    const getProjectList = Array.isArray(configData?.screens?.[5]?.screensInfo?.properties)
      ? configData.screens[5].screensInfo.properties
      : [];

    const getEquipmentList = Array.isArray(configData?.screens?.[8]?.screensInfo?.properties)
      ? configData.screens[8].screensInfo.properties
      : [];

    const projectItem = getProjectList.find((item) => item.id === Common.PROJECT_TEXT);
    const equipmentItem = getEquipmentList.find((item) => item.id === Common.EQUIPMENT_TYPE);

    const createDropdownListFunc = (listArr = []) => {
      const temp = listArr.map((item) => ({
        title: item.title,
        [Constants.MONGOOSE_ID]: item.id,
      }));

      return temp;
    };

    if (projectItem) {
      const temp = createDropdownListFunc(projectItem.options);
      setProjectOptionsList(temp);
    }

    if (equipmentItem) {
      // Removing temporary items from equipment list
      const withoutTempItems = equipmentItem.options.filter(
        (item) => item?.currency !== Common.TEMPORARY_ITEMS_TYPE_TITLE_TEXT
      );
      const temp = createDropdownListFunc(withoutTempItems);
      setEquipmentOptionsList(temp);
    }

    setAddRequestedEquipmentObj({
      equipmentOrders: [],
      tempOrders: [],
    });
    setRequestedItemsErrors({});
  }, [configData]);

  useEffect(() => {
    getEquipmentTypeListFunc();
  }, []);

  useEffect(() => {
    setRequestedItemsTableData({ rows: requestedItemsRows, columns: requestedItemsColumns });
  }, [addRequestedEquipmentObj?.equipmentOrders, updateStateAtAddItem]);

  useEffect(() => {
    setTemporaryItemsTableData({ rows: temporaryItemsRows, columns: temporaryItemsColumns });
  }, [addRequestedEquipmentObj?.tempOrders, updateStateAtAddItem]);

  return (
    <MDBox display="flex" flexDirection="column" gap={2}>
      {/* Box for project dropdown */}
      <MDBox>
        <MDBox width="20%" minWidth="160px">
          <CustomAutoComplete
            label={Common.PROJECT_ORDER_PROJECT_LABEL}
            name={Common.PROJECT_ORDER_PROJECT_NAME}
            id={Common.PROJECT_ORDER_PROJECT_NAME}
            hint={Common.PROJECT_ORDER_PROJECT_LABEL}
            getOptionLabel={(option) => option.title || ""}
            menu={projectOptionsList}
            value={{
              title: projectOptionsList.find(
                (item) => item[Constants.MONGOOSE_ID] === selectedProjectId
              )?.title,
            }}
            disabled={Boolean(selectedProjectId)}
            error={requestedItemsErrors.project || temporaryItemsErrors.project}
            helperText={requestedItemsErrors.project || temporaryItemsErrors.project}
            handleChange={(e) => handleFormValueChange(e)}
          />
        </MDBox>
      </MDBox>

      {/* Box for Requested Items */}
      <MDBox display="flex" flexDirection="column" gap={1}>
        {/* Form Fields and Button Box */}
        <MDBox
          display="flex"
          flexDirection="row"
          gap={2}
          justifyContent="space-between"
          alignItems="bottom"
        >
          <MDBox
            display="flex"
            flexGrow={1}
            flexDirection="row"
            justifyContent="space-between"
            gap={2}
            key={updateStateAtAddItem}
          >
            <MDBox width="20%" minWidth="160px">
              <CustomAutoComplete
                label={Common.PROJECT_ORDER_EQUIPMENT_TYPE_LABEL}
                name={Common.PROJECT_ORDER_EQUIPMENT_TYPE_NAME}
                id={Common.PROJECT_ORDER_EQUIPMENT_TYPE_NAME}
                hint={Common.PROJECT_ORDER_EQUIPMENT_TYPE_LABEL}
                getOptionLabel={(option) => option.title || ""}
                menu={equipmentOptionsList}
                value={{
                  title: equipmentOptionsList.find(
                    (item) => item[Constants.MONGOOSE_ID] === requestedItemsObj?.equipmentType
                  )?.title,
                }}
                error={requestedItemsErrors.equipmentType}
                helperText={requestedItemsErrors.equipmentType}
                handleChange={(e) => handleFormValueChange(e)}
              />
            </MDBox>

            <MDBox width="20%" minWidth="160px">
              <FTextField
                label={Common.PROJECT_ORDER_QUANTITY_LABEL}
                name={Common.PROJECT_ORDER_QUANTITY_NAME}
                id={Common.PROJECT_ORDER_QUANTITY_NAME}
                type={FormFields.TYPE_NUMBER}
                placeholder={Common.PROJECT_ORDER_QUANTITY_LABEL}
                error={Boolean(requestedItemsErrors.engineerRequestedQuantity)}
                helperText={requestedItemsErrors.engineerRequestedQuantity}
                value={requestedItemsObj.engineerRequestedQuantity || ""}
                handleChange={(e) => handleFormValueChange(e)}
              />
            </MDBox>

            <MDBox flexGrow={1} width="auto">
              <FTextField
                label={Common.PROJECT_ORDER_COMMENTS_LABEL}
                name={Common.PROJECT_ORDER_COMMENTS_NAME}
                id={Common.PROJECT_ORDER_COMMENTS_NAME}
                type={FormFields.TYPE_TEXT}
                placeholder={Common.PROJECT_ORDER_COMMENTS_LABEL}
                value={requestedItemsObj.engineerComment || ""}
                handleChange={(e) => handleFormValueChange(e)}
              />
            </MDBox>
          </MDBox>

          <MDBox
            display="flex"
            justifyContent="flex-end"
            alignItems="end"
            flexShrink={0}
            mb={Object.keys(requestedItemsErrors).length === 0 ? 1 : 3}
          >
            <BasicButton
              title={ButtonTitles.ADD_ITEM}
              icon={Icons.ADD}
              background={Colors.WHITE}
              border
              color={Colors.BLACK}
              action={addRequestedEquipmentFunc}
            />
          </MDBox>
        </MDBox>

        {/* Table Box */}
        {addRequestedEquipmentObj?.equipmentOrders?.length > 0 && (
          <MDBox>
            <DataTable
              table={requestedItemsTableData}
              isSorted={false}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
            />
          </MDBox>
        )}
      </MDBox>

      {/* Box for Temporary Items */}
      <MDBox display="flex" flexDirection="column" gap={1}>
        {/* Temporary Items Title Box */}
        <MDBox mt={1} mb={0.5} ml={-1}>
          <MDTypography
            sx={{
              fontSize: FontComponent({ sizes: ModalBreakPoint.baseTitleBreakPoint }),
              color: "#191D31",
              fontWeight: "700",
            }}
          >
            Temporary Items
          </MDTypography>
        </MDBox>

        {/* Temporary Form Fields and Button Box */}
        <MDBox
          display="flex"
          flexDirection="row"
          gap={2}
          justifyContent="space-between"
          alignItems="bottom"
        >
          <MDBox display="flex" flexGrow={1} flexDirection="row" gap={2} key={updateStateAtAddItem}>
            <MDBox width="20%" minWidth="160px">
              <FTextField
                label={Common.PROJECT_ORDER_TEMPORARY_PRODUCT_LABEL}
                name={Common.PROJECT_ORDER_TEMPORARY_PRODUCT_NAME}
                id={Common.PROJECT_ORDER_TEMPORARY_PRODUCT_NAME}
                type={FormFields.TYPE_TEXT}
                placeholder={Common.PROJECT_ORDER_TEMPORARY_PRODUCT_LABEL}
                error={Boolean(temporaryItemsErrors.productName)}
                helperText={temporaryItemsErrors.productName}
                value={temporaryItemsObj.productName || ""}
                handleChange={(e) => handleFormValueChange(e, Common.TEMPORARY_ITEMS_TYPES_TEXT)}
              />
            </MDBox>

            <MDBox width="20%" minWidth="160px">
              <FTextField
                label={Common.PROJECT_ORDER_TEMPORARY_QUANTITY_LABEL}
                name={Common.PROJECT_ORDER_TEMPORARY_QUANTITY_NAME}
                id={Common.PROJECT_ORDER_TEMPORARY_QUANTITY_NAME}
                type={FormFields.TYPE_NUMBER}
                placeholder={Common.PROJECT_ORDER_TEMPORARY_QUANTITY_LABEL}
                error={Boolean(temporaryItemsErrors.engineerRequestedQuantity)}
                helperText={temporaryItemsErrors.engineerRequestedQuantity}
                value={temporaryItemsObj.engineerRequestedQuantity}
                handleChange={(e) => handleFormValueChange(e, Common.TEMPORARY_ITEMS_TYPES_TEXT)}
              />
            </MDBox>

            <MDBox flexGrow={1} width="auto">
              <FTextField
                label={Common.PROJECT_ORDER_COMMENTS_LABEL}
                name={Common.PROJECT_ORDER_COMMENTS_NAME}
                id={Common.PROJECT_ORDER_COMMENTS_NAME}
                type={FormFields.TYPE_TEXT}
                placeholder={Common.PROJECT_ORDER_COMMENTS_LABEL}
                value={temporaryItemsObj.engineerComment || ""}
                handleChange={(e) => handleFormValueChange(e, Common.TEMPORARY_ITEMS_TYPES_TEXT)}
              />
            </MDBox>
          </MDBox>

          <MDBox
            display="flex"
            justifyContent="flex-end"
            alignItems="end"
            flexShrink={0}
            mb={Object.keys(temporaryItemsErrors).length === 0 ? 1 : 3}
          >
            <BasicButton
              title={ButtonTitles.ADD_ITEM}
              icon={Icons.ADD}
              background={Colors.WHITE}
              border
              color={Colors.BLACK}
              action={() => addRequestedEquipmentFunc(Common.TEMPORARY_ITEMS_TYPES_TEXT)}
            />
          </MDBox>
        </MDBox>

        {/* Temporary Items Table */}
        <MDBox mt={0.5}>
          {addRequestedEquipmentObj?.tempOrders?.length > 0 && (
            <DataTable
              table={temporaryItemsTableData}
              isSorted={false}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
            />
          )}
        </MDBox>
      </MDBox>
    </MDBox>
  );
}

AddRequestEquipment.propTypes = {
  addRequestedEquipmentObj: PropTypes.objectOf(PropTypes.any).isRequired,
  setAddRequestedEquipmentObj: PropTypes.func.isRequired,
};

export default AddRequestEquipment;

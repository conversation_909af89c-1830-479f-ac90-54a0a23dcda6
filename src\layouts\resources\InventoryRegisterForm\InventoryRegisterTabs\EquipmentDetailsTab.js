import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";

// 3rd party library imports
import { Grid } from "@mui/material";
import { useDispatch } from "react-redux";
import ReactDatePicker from "react-datepicker";
import moment from "moment";

// Material Dashboard React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import FTextField from "components/Form/FTextField";
import SearchBar from "components/Search/SearchInTable";
import ConfigDropdown from "components/Dropdown/ConfigDropdown";
import DateTime from "components/DateTime/DateTime";

// Redux imports
import ProductListThunk from "redux/Thunks/Equipment";

// Function imports from assets
import pxToRem from "assets/theme/functions/pxToRem";

// utils
import Constants, {
  FiltersModuleName,
  defaultData,
  Colors,
  Common,
  BackendFrontend,
} from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

const initialFilterArr = [
  {
    inputLabel: FiltersModuleName.SEARCH,
    list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
    selectedValue: "",
    isLoading: false,
  },
];

function EquipmentDetailsTab({
  equipmentId,
  updatedBody,
  setUpdatedBody,
  setSearchData,
  formValues,
  setFormValues,
  errors,
  currency,
  setCurrency,
  updatedFormValues,
  setUpdatedFormValues,
  setQuantityType,
  formData,
  setFormData,
}) {
  let debounceTimeout;
  const dispatch = useDispatch();
  const [filters, setFilters] = useState(initialFilterArr);

  // Utility function to get equip value
  const getEquipValue = (item) => {
    if (item.parentFieldId === "") return {};
    return (
      item.options.find(
        (opt) =>
          opt.isVisibleForOptions.includes(formValues?.[item.parentFieldId]) ||
          opt.isVisibleForOptions.includes(formValues?.[item.parentFieldId]?.id)
      ) || {}
    );
  };

  // Func to fetch Data
  const getProductListFunc = async (SearchObj) => {
    const searchParams = paramCreater(SearchObj);

    const res = await dispatch(ProductListThunk(searchParams));
    if (res?.payload?.status === Common.API_STATUS_200) {
      const userList = res?.payload?.data?.data?.inventoryData?.map((item) => ({
        title: item?.name,
        details: item,
      }));

      // Filtering out the "All" option
      const filteredEquipmentList = userList?.filter(
        (item) => item?.title?.toLowerCase() !== "all"
      );

      setFilters((prev) => {
        const updatedFilters = prev.map((elem) => {
          if (elem?.inputLabel === FiltersModuleName.SEARCH) {
            return {
              ...elem,
              list: filteredEquipmentList,
              isLoading: false,
            };
          }
          return elem;
        });
        return updatedFilters;
      });

      // Updating the updatedBody with search data
      setUpdatedBody((prev) => ({
        ...prev,
        name: SearchObj?.search,
      }));
    }
  };

  const handleConfigDataChange = (name, value) => {
    const temp = { ...formValues };
    const updatedValuesCopy = { ...updatedBody };
    const equipmentValues = { ...updatedFormValues };

    if (name?.trim()?.includes(Common.WEIGHT)) {
      temp[name] = value;
      equipmentValues[name] = value.replace(",", ".");
    } else {
      temp[name] = value;
      equipmentValues[name] = value;
    }

    const updatedFormCopy = formData.map((elem) => {
      const copyTempValues = { ...temp };
      const checkID = /^\d/.test(copyTempValues?.equipmentType)
        ? copyTempValues?.equipmentType
        : updatedValuesCopy?.equipmentType;

      if (elem?.parentFieldId !== "" && !elem.dependentIds.includes(checkID)) {
        delete equipmentValues?.[elem?.id];
      }
      const updatedElem = { ...elem };
      if (updatedElem.type === BackendFrontend.OPTIONS_VALUES) {
        if (updatedElem.dependentIds.includes(checkID)) {
          updatedElem.isDefaultVisible = true;
          updatedElem.IsRequired = true;
        } else if (!updatedElem.dependentIds.includes(checkID)) {
          updatedElem.isDefaultVisible = false;
          updatedElem.IsRequired = false;
        }
      } else if (
        updatedElem.type === BackendFrontend.TEXT &&
        updatedElem.parentFieldId === Common.EQUIPMENT_TYPE
      ) {
        if (updatedElem.dependentIds.includes(checkID)) {
          setQuantityType(true);
          updatedElem.isDefaultVisible = true;
          updatedElem.IsRequired = true;
        } else if (!updatedElem.dependentIds.includes(checkID)) {
          setQuantityType(false);
          updatedElem.isDefaultVisible = false;
          updatedElem.IsRequired = false;
          setUpdatedBody((prev) => {
            const { serialNumber, ...rest } = prev;
            return rest;
          });
        }
      } else if (updatedElem.type === BackendFrontend.OPTIONS && updatedElem.id === Common.PRICE) {
        if (updatedElem.dependentIds.includes(checkID)) {
          setQuantityType(true);
          updatedElem.isDefaultVisible = true;
          updatedElem.IsRequired = true;
        } else if (!updatedElem.dependentIds.includes(checkID)) {
          setQuantityType(false);
          updatedElem.isDefaultVisible = false;
          updatedElem.IsRequired = false;
        }
      }
      if (name === Common.EQUIPMENT_TYPE && elem.id === Common.EQUIPMENT_TYPE) {
        elem.options.forEach((val) => {
          if (val.id === value) {
            setCurrency(val.currency);
          }
        });
      }
      return updatedElem;
    });

    setFormData(updatedFormCopy);
    setFormValues({ ...temp });
    setUpdatedFormValues({ ...equipmentValues });
    setUpdatedBody((prev) => ({
      ...prev,
      ...equipmentValues,
    }));
  };

  const handleFilterType = async (e) => {
    const { value } = e.target;

    setFilters((prev) => {
      const updatedFilters = prev.map((elem) => {
        if (elem?.inputLabel === FiltersModuleName.SEARCH) {
          return {
            ...elem,
            selectedValue: value?.trim(),
          };
        }
        return elem;
      });
      return updatedFilters;
    });
  };

  const handleSearch = async (e) => {
    if (e.target.value === "") {
      // Clear the list and search data when the search field is empty

      setSearchData(null);
      setFilters((prev) => {
        const updatedFilters = prev.map((elem) => {
          if (elem?.inputLabel === FiltersModuleName.SEARCH) {
            return {
              ...elem,
              list: [],
              isLoading: false,
            };
          }
          return elem;
        });
        return updatedFilters;
      });
      return;
    }

    setFilters((prev) => {
      const updatedFilters = prev.map((elem) => {
        if (elem?.inputLabel === FiltersModuleName.SEARCH) {
          return {
            ...elem,
            isLoading: true,
          };
        }
        return elem;
      });
      return updatedFilters;
    });

    const ValueObj = {
      search: e?.target?.value?.trim(),
    };

    // Deleting the search key if value is empty
    Object.keys(ValueObj).forEach((key) => {
      if (ValueObj[key] === "") {
        delete ValueObj[key];
      }
    });

    await getProductListFunc(ValueObj);
  };

  const debouncedHandleSearch = (e) => {
    clearTimeout(debounceTimeout);
    debounceTimeout = setTimeout(async () => handleSearch(e), 300);
  };

  const handleWeightNumberChange = (id, value) => {
    const regex = /^\d+([,.]\d*)?$/;

    if (regex.test(value) || value === "") {
      handleConfigDataChange(id, value, id);
    }
  };

  useEffect(() => {
    filters.forEach((elem) => {
      if (elem?.inputLabel === FiltersModuleName.SEARCH) {
        const detailObj = elem?.list?.find((item) => item?.title === elem?.selectedValue);
        setSearchData(detailObj?.details);
      }
    });
  }, [filters[0]?.selectedValue]);

  return (
    <MDBox display="flex" flexDirection="column" gap={1}>
      <MDBox>
        <MDTypography
          sx={{
            fontSize: pxToRem(20),
            fontWeight: 600,
            color: "#667085",
          }}
        >
          Equipment Information
        </MDTypography>
      </MDBox>

      <MDBox
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
        }}
      >
        <Grid
          container
          spacing={1}
          sx={{
            display: "flex",
            padding: 0,
            justifyContent: "flex-start",
          }}
        >
          {formData?.length > 0 &&
            formData
              ?.filter((item) => item.isDefaultVisible)
              .map((item) => {
                const fieldValue = formValues[item.id] || "";
                switch (item.type) {
                  case BackendFrontend.TEXT:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        <FTextField
                          label={item.title}
                          placeholder={item?.hint}
                          name={item?.id}
                          id={item?.id}
                          type="text"
                          error={Boolean(errors[item.id])}
                          helperText={errors[item.id]}
                          value={fieldValue}
                          handleChange={(e) =>
                            handleConfigDataChange(item?.id, e.target.value, item?.id)
                          }
                        />
                      </Grid>
                    );

                  case BackendFrontend.SEARCH:
                    return (
                      <Grid item lg={12} sm={12} key={item.id}>
                        <SearchBar
                          label={item?.IsRequired === true ? `${item.title}*` : item.title}
                          width="100%"
                          marginRight={0}
                          options={
                            filters[0]?.list
                              ?.filter((val) => val[Constants.MONGOOSE_ID] !== "all") // Exclude "All" option
                              .map((val) => val.title) || []
                          }
                          value={filters[0]?.selectedValue || updatedBody?.[item.id]}
                          filters={filters}
                          freeSolos
                          error={Boolean(errors[item?.id])}
                          helperText={errors[item?.id]}
                          placeholder={item?.title}
                          isLoading={filters[0]?.isLoading}
                          handleFilterChange={(e, value) => {
                            handleFilterType({
                              target: {
                                name: filters[0].inputLabel,
                                value,
                              },
                            });
                          }}
                          debouncedHandleSearch={debouncedHandleSearch}
                        />
                      </Grid>
                    );

                  case BackendFrontend.OPTIONS:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        {(() => {
                          const equipvalue = getEquipValue(item);
                          return (
                            <ConfigDropdown
                              label={item?.IsRequired ? `${item.title}*` : item.title}
                              name={item?.id}
                              id={item?.id}
                              menu={item?.options.map((val) => ({
                                [Constants.MONGOOSE_ID]: val?.id,
                                title: val?.title,
                              }))}
                              disabled={
                                item?.parentFieldId !== "" ||
                                (equipmentId && item?.id === Common.EQUIPMENT_TYPE)
                              }
                              //   Check for this e value
                              handleChange={(e, value, id) => {
                                if (item?.parentFieldId === "") {
                                  handleConfigDataChange(e, value, id);
                                }
                              }}
                              value={
                                (item.parentFieldId !== "" && (equipvalue?.id || "")) ||
                                formValues?.[item?.id] ||
                                fieldValue
                              }
                              error={errors?.[item.id]}
                              helperText={errors?.[item.id]}
                              minWidth={pxToRem(317)}
                              maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
                              startAdornment={
                                item?.id === Common.PRICE && currency ? (
                                  <MDTypography variant="body" sx={{ color: Colors.GREY }}>
                                    {currency}
                                  </MDTypography>
                                ) : null
                              }
                            />
                          );
                        })()}
                      </Grid>
                    );

                  case BackendFrontend.NUMBER:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        <FTextField
                          label={item?.IsRequired ? `${item.title}*` : item.title}
                          placeholder={item?.hint}
                          name={item?.id}
                          id={item?.id}
                          type="number"
                          error={Boolean(errors[item?.id])}
                          helperText={errors[item?.id]}
                          value={formValues[item?.id] || ""}
                          handleChange={(e) =>
                            handleConfigDataChange(item?.id, e.target.value, item?.id)
                          }
                        />
                      </Grid>
                    );

                  case BackendFrontend.DECIMAL_NUMBER:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        <FTextField
                          label={item?.IsRequired ? `${item.title}*` : item.title}
                          placeholder={item?.hint}
                          name={item?.id}
                          id={item?.id}
                          error={Boolean(errors[item.id])}
                          helperText={errors[item.id]}
                          value={formValues[item.id] || ""}
                          handleChange={(e) => handleWeightNumberChange(item?.id, e.target.value)}
                        />
                      </Grid>
                    );

                  case BackendFrontend.DATE:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        <MDBox sx={{ minWidth: "100%", mt: 0, mr: 1, zIndex: 9999 }} display="flex">
                          <ReactDatePicker
                            selected={
                              (formValues?.[item?.id] &&
                                moment(formValues?.[item?.id].split(".")[0]).toDate()) ||
                              null
                            }
                            onChange={(date) =>
                              handleConfigDataChange(
                                item.id,
                                moment(date).format(defaultData.DATABSE_DATE_FORMAT).toString(),
                                item?.questionId || item?.id
                              )
                            }
                            customInput={
                              <DateTime
                                value={fieldValue}
                                item={item}
                                label={`${item?.title}${item?.IsRequired ? "*" : ""}`}
                                errors={formValues.errors?.[item.id]}
                                helperText={formValues.errors?.[item.id]}
                                placeholder={item?.hint}
                              />
                            }
                            placeholderText={item?.hint}
                            dateFormat={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
                          />
                        </MDBox>
                      </Grid>
                    );

                  case BackendFrontend.OPTIONS_VALUES:
                    return (
                      <Grid item lg={6} sm={12} key={item.id}>
                        <FTextField
                          label={item?.IsRequired ? `${item.title}*` : item.title}
                          placeholder={item?.hint}
                          name={item?.id}
                          id={item?.id}
                          type="number"
                          error={Boolean(errors[item.id])}
                          helperText={errors[item.id]}
                          value={formValues[item.id] || ""}
                          handleChange={(e) =>
                            handleConfigDataChange(item?.id, e.target.value, item?.id)
                          }
                          InputProps={
                            currency
                              ? {
                                  startAdornment: (
                                    <MDTypography variant="body">{currency}</MDTypography>
                                  ),
                                }
                              : {}
                          }
                        />
                      </Grid>
                    );
                  default:
                    return null;
                }
              })}
        </Grid>
      </MDBox>
    </MDBox>
  );
}

EquipmentDetailsTab.defaultProps = {
  equipmentId: null,
};

EquipmentDetailsTab.propTypes = {
  equipmentId: PropTypes.number,
  updatedBody: PropTypes.objectOf(PropTypes.any).isRequired,
  setUpdatedBody: PropTypes.func.isRequired,
  setSearchData: PropTypes.func.isRequired,
  formValues: PropTypes.objectOf(PropTypes.any).isRequired,
  setFormValues: PropTypes.func.isRequired,
  errors: PropTypes.objectOf(PropTypes.any).isRequired,
  currency: PropTypes.string.isRequired,
  setCurrency: PropTypes.func.isRequired,
  updatedFormValues: PropTypes.objectOf(PropTypes.any).isRequired,
  setUpdatedFormValues: PropTypes.func.isRequired,
  setQuantityType: PropTypes.func.isRequired,
  formData: PropTypes.arrayOf(PropTypes.object).isRequired,
  setFormData: PropTypes.func.isRequired,
};

export default EquipmentDetailsTab;

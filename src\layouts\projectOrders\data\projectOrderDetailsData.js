import React, { useEffect, useState } from "react";

// Material Dashboard 2 React components
import { Badge, IconButton } from "@mui/material";

// Common Components
import MDBox from "components/MDBox";
import Author from "components/Table/Author";
import CustomImage from "components/Table/GroupImage";

// Redux
import { useSelector } from "react-redux";

// Utils
import Constants, { Icons, Common } from "utils/Constants";

export default function ProjectOrderDetailsData(
  requestOderDetailsData,
  currentStatus,
  handleOpenUserModal,
  openApproverCommentModal,
  openAddToQueueModal,
  openRejectOrderConfirmationBox
) {
  const [requestOrderRows, setRequestOrdersRows] = useState([]);
  const { shoppingDetailsByIdList } = useSelector((state) => state.equipmentRequest);
  useEffect(() => {
    if (requestOderDetailsData) {
      const list = requestOderDetailsData?.map((element, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          equipmenttype: (
            <MDBox display="flex" justifyContent="start" alignItems="center">
              <CustomImage item={element?.equipmentTypeImage?.url} width={30} height={30} />
              <MDBox
                style={{
                  flex: 1,
                  width: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                <Author name={element?.type} />
              </MDBox>
              {element?.isTemporary && (
                <MDBox>
                  <IconButton aria-label="fingerprint">{Icons.CLOCK}</IconButton>
                </MDBox>
              )}
            </MDBox>
          ),
          equipmentcategory: <Author name={element?.equipmentCategory?.name} />,
          quantityType: <Author name={element?.quantityType?.priceType} />,
          reqQty: <Author name={element?.totalEngineerRequestedQuantity} />,
          totalApproved: (
            <Author
              name={
                currentStatus === Constants.STATUS_PENDING
                  ? element?.totalApprovedQuantity
                  : element?.pmOrderManageDetails?.pmRequestedQuantity
              }
            />
          ),
          price: <Author name={`${element?.currencyUnit?.abbreviation} ${element?.price}`} />,
          comments: element?.users?.some((isc) => isc.engineerComment?.length > 0) && (
            <IconButton aria-label="fingerprint">{Icons.CORRECT_OUTLINE}</IconButton>
          ),
          totalAmount: (
            <Author
              name={
                (element?.price ?? 0) *
                (element?.pmOrderManageDetails?.pmRequestedQuantity ?? 0) *
                (element?.quantityType?.priceType === Common.RENTAL
                  ? shoppingDetailsByIdList?.totalDays ?? 1
                  : 1)
              }
            />
          ),
          action: (
            <MDBox display="flex" justifyContent="center" alignItems="center">
              <IconButton
                aria-label="fingerprint"
                color="info"
                onClick={() => handleOpenUserModal(element)}
              >
                {Icons.VIEW}
              </IconButton>
              {currentStatus !== Constants.STATUS_REJECTED && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  onClick={() => {
                    openApproverCommentModal(
                      "approver",
                      currentStatus === Constants.STATUS_PENDING
                        ? element?.pmComments
                        : element?.pmOrderManageDetails?.comments?.pmComments,
                      element[Constants.MONGOOSE_ID],
                      element?.pmOrderManageDetails[Constants.MONGOOSE_ID],
                      currentStatus
                    );
                  }}
                >
                  <Badge
                    badgeContent={
                      currentStatus === Constants.QUEUE
                        ? element?.pmOrderManageDetails?.comments?.pmComments?.length
                        : element?.pmComments?.length
                    }
                    color="error"
                    max={9}
                    sx={{ "& .MuiBadge-badge": { fontSize: 10, height: 15, minWidth: 15 } }}
                  >
                    {Icons.ADD_COMMENT}
                  </Badge>
                </IconButton>
              )}
              {currentStatus === Constants.STATUS_PENDING && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  onClick={() =>
                    openAddToQueueModal(
                      element[Constants.MONGOOSE_ID],
                      element,
                      Common.PROJECT_ORDER_QUEUE_SINGLE
                    )
                  }
                >
                  {Icons.CHEKOUT}
                </IconButton>
              )}
              {currentStatus !== Constants.STATUS_REJECTED && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  onClick={() =>
                    openRejectOrderConfirmationBox(
                      Constants.REJECT_EQUIPMENT_TITLE,
                      Constants.REJECT_EQUIPMENT_MESSAGE,
                      Common.EQUIPMENT_TEXT,
                      element
                    )
                  }
                >
                  {Icons.REJECTED}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRequestOrdersRows(list);
    }
  }, [requestOderDetailsData]);

  return {
    requestOrderColumns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Equipment Type", accessor: "equipmenttype", align: "left", width: "25%" },
      { Header: "Equipment Category", accessor: "equipmentcategory", align: "left" },
      { Header: "Quantity Type", accessor: "quantityType", align: "left" },
      { Header: "Total Req Qty", accessor: "reqQty", align: "left" },
      { Header: "Total Approved Qty", accessor: "totalApproved", align: "left" },
      { Header: "Price Per Equipment Type", accessor: "price", align: "left" },
      ...(currentStatus !== Constants.QUEUE
        ? [{ Header: "Comments", accessor: "comments", align: "left" }]
        : []),
      ...(currentStatus === Constants.QUEUE
        ? [{ Header: "Total Amount", accessor: "totalAmount", align: "left" }]
        : []),
      {
        Header: "Action",
        accessor: "action",
        width: "10%",
        align: "center",
      },
    ],
    requestOrderRows,
  };
}

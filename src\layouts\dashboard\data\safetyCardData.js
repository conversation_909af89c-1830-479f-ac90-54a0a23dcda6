/* eslint-disable react/prop-types */
/* eslint-disable react/function-component-definition */

import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

// Custom Components
import MDBox from "components/MDBox";
import Status from "components/Table/Status";
import Author from "components/Table/Author";
import RiskFactor from "components/Table/RiskFactor";
import By from "components/Table/By";

// MUI Components
import { IconButton } from "@mui/material";

// 3rd party library
import moment from "moment";

// Constants and Utils
import Constants, { Icons, defaultData } from "utils/Constants";
import { getFormattedProjectName } from "utils/methods/methods";

export default function SafetyCardData(
  handleEditOpen,
  handleDeleteOpen,
  handleOpenQhseDetailDrawer,
  handleExportIndividualPdf,
  exportSafetyCardId
) {
  const [rows, setRows] = useState([]);
  const safetyCards = useSelector((state) => state.safetCard.list);
  const ConfigData = useSelector((state) => state.config);

  const screens = ConfigData?.screens;
  useEffect(() => {
    if (safetyCards) {
      const list = [];
      safetyCards.forEach((item, index) => {
        const configObjIdx = screens.findIndex(
          (s) =>
            s.screensInfo &&
            s.screensInfo.title &&
            s.screensInfo.title.toLowerCase() === item.cardType
        );
        const configObj = configObjIdx !== -1 ? screens[configObjIdx] : null;
        if (configObj && configObj.screensInfo?.agreement?.read) {
          const temp = {
            srNo: <Author name={index + 1} />,
            title: <Author name={item.title ? item.title : ""} maxContent={20} />,
            project: (
              <Author
                name={getFormattedProjectName(item.project)}
                nickName={item.defaultProject?.title ?? ""}
                maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
              />
            ),
            type: (
              <Author
                icon={item?.images && item.images.length > 0 ? Icons.ATTACHMENT : null}
                name={item?.cardType === Constants.NCR_VALUE ? "NCR" : item?.cardType}
              />
            ),
            location: (
              <Author name={item?.location?.title} maxContent={defaultData.MEDIUM_CONTENT_LENGTH} />
            ),
            category: (
              <Author
                name={item?.category?.categoryName}
                maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
              />
            ),
            riskFactor: item?.riskFactor && <RiskFactor risk={parseInt(item?.riskFactor, 10)} />,
            status: (
              <Status
                title={`${
                  item?.status === "submitted(client)"
                    ? "submitted to client"
                    : item?.status.replace("_", " ")
                }`}
              />
            ),
            createdBy: (
              <By
                name={`${
                  item.createdBy?.callingName
                    ? item.createdBy?.callingName
                    : item.createdBy?.firstName
                } ${item.createdBy?.lastName}`}
                isSuperAdmin={item.createdBy?.role?.title === defaultData.SUPER_ADMIN_ROLE}
                when={moment(item?.createdAt).format(defaultData.WEB_DATE_FORMAT)}
              />
            ),
            lastChangedBy: (
              <By
                name={
                  item.updatedBy
                    ? `${
                        item.updatedBy?.callingName
                          ? item.updatedBy?.callingName
                          : item.updatedBy?.firstName
                      } ${item.updatedBy?.lastName} `
                    : ""
                }
                isSuperAdmin={item.updatedBy?.role?.title === defaultData.SUPER_ADMIN_ROLE}
                when={moment(item?.updatedAt).format(defaultData.WEB_DATE_FORMAT)}
              />
            ),
            action: (
              <MDBox>
                {configObj?.screensInfo?.agreement?.read && (
                  <IconButton
                    aria-label="Edit Safety Card"
                    color="info"
                    onClick={() => handleOpenQhseDetailDrawer(item?.[Constants.MONGOOSE_ID])}
                  >
                    {Icons.VIEW}
                  </IconButton>
                )}
                &nbsp;
                {configObj?.screensInfo?.agreement?.update && (
                  <IconButton aria-label="Edit Safety Card" onClick={() => handleEditOpen(item)}>
                    {Icons.EDIT}
                  </IconButton>
                )}
                &nbsp;
                {configObj?.screensInfo?.agreement?.delete && (
                  <IconButton
                    aria-label="fingerprint"
                    color="error"
                    onClick={() => handleDeleteOpen(item)}
                  >
                    {Icons.DELETE}
                  </IconButton>
                )}
                &nbsp;
                {configObj?.screensInfo?.agreement?.update && (
                  <IconButton
                    aria-label="Export Safety Card"
                    color="info"
                    onClick={() => handleExportIndividualPdf(item?.[Constants.MONGOOSE_ID])}
                  >
                    {exportSafetyCardId === item?.[Constants.MONGOOSE_ID]
                      ? Icons.LOADING
                      : Icons.EXPORT}
                  </IconButton>
                )}
              </MDBox>
            ),
          };
          list.push(temp);
        }
      });
      setRows([...list]);
    }
  }, [safetyCards, ConfigData, exportSafetyCardId]);

  return {
    columns: [
      { Header: "No.", accessor: "srNo", width: "3%" },
      { Header: "Project", accessor: "project", align: "left", width: "15%" },
      { Header: "Type", accessor: "type", align: "left", width: "5%" },
      { Header: "Title", accessor: "title", align: "left", width: "30%" },
      { Header: "Location", accessor: "location", align: "left", width: "10%" },
      { Header: "Category", accessor: "category", align: "left", width: "8%" },
      { Header: "Risk Factor", accessor: "riskFactor", width: "5%", align: "left" },
      { Header: "Status", accessor: "status", align: "left", width: "5%" },
      { Header: "Created By", accessor: "createdBy", align: "left", width: "10%" },
      { Header: "Last Changed By", accessor: "lastChangedBy", align: "left", width: "10%" },
      { Header: "Action", accessor: "action", align: "center", width: "10%" },
    ],

    rows,
  };
}

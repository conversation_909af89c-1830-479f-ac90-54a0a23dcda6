import React, { useEffect, useState } from "react";

// MUI Components
import { Grid } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import DataTable from "examples/Tables/DataTable";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import NewProject from "examples/modal/NewProject/NewProject";
import ImageUpload from "components/ImageUpload/imageUpload";
import FTextField from "components/Form/FTextField";
import FullScreenImageComponent from "components/ViewFullImage/ViewImage";

// Constants
import Constants, {
  ButtonTitles,
  Icons,
  defaultData,
  Colors,
  ModalContent,
  Common,
  BackendFrontend,
  PageTitles,
} from "utils/Constants";

// Utils
import Validators from "utils/Validations";
import pattern from "utils/Patterns";

// Redux component
import { openSnackbar } from "redux/Slice/Notification";
import { useDispatch } from "react-redux";
import getAllQHSEDocuments, {
  createQHSEDocument,
  updateQHSEDocument,
  deleteQHSEDocument,
} from "redux/Thunks/QHSEDocuments";

// Local components
import QHSEDocumentData from "./data/QHSEDocumentData";

function QHSEDocuments() {
  const [documentList, setDocumentList] = useState([]);
  const [openUploadModal, setOpenUploadModal] = useState(false);
  const [deleteData, setDeleteData] = useState({
    openDeleteModal: false,
    deleteId: "",
    deleteLoading: false,
  });
  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });
  const [next, setNext] = useState(0);
  const [fullScreenImage, setFullScreenImage] = useState(null);
  const [modaltype, setModalType] = useState(Common.NEW);
  const [loading, setLoading] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState(Constants.PENDING);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [editLists, setEditLists] = useState({
    qhseDocument: {},
  });
  const [body, setBody] = useState({
    qhseDocument: {},
  });
  const [error, setError] = useState({
    qhseDocument: {},
  });

  const dispatch = useDispatch();

  // Modal handlers
  const handleOpenUploadModal = () => {
    setOpenUploadModal(true);
  };

  const handleCloseUploadModal = () => {
    setOpenUploadModal(false);
    setModalType(Common.NEW);
    setBody({ qhseDocument: {} });
    setError({ qhseDocument: {} });
    setEditLists({ qhseDocument: {} });
  };

  const handleOpenDeleteModal = (documentId) => {
    setDeleteData({
      ...deleteData,
      openDeleteModal: true,
      deleteId: documentId,
    });
  };

  const handleCloseDeleteModal = () => {
    setDeleteData({
      ...deleteData,
      openDeleteModal: false,
      deleteId: "",
    });
  };

  // API functions
  const fetchDocuments = async () => {
    setTablePagination({ ...tablePagination, page: 0 });
    const data = new URLSearchParams({
      page: defaultData.PAGE,
      perPage: tablePagination.perPage,
    });
    setLoadingStatus(Constants.PENDING);
    const res = await dispatch(getAllQHSEDocuments(data));
    setLoadingStatus(Constants.FULFILLED);
    if (res.payload.status === Common.API_STATUS_200) {
      setDocumentList(res.payload.data.data);
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleDeleteDocument = async () => {
    setDeleteData({ ...deleteData, deleteLoading: true });
    const res = await dispatch(deleteQHSEDocument(deleteData.deleteId));
    if (res.payload.status === Common.API_STATUS_200) {
      const index = documentList.findIndex(
        (element) => element[Constants.MONGOOSE_ID] === deleteData.deleteId
      );
      const temp = [...documentList];
      temp.splice(index, 1);
      setDocumentList(temp);

      dispatch(
        openSnackbar({
          message: Constants.DOCUMENT_DELETE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );

      // Close modal and reset delete data
      setDeleteData({
        openDeleteModal: false,
        deleteId: "",
        deleteLoading: false,
      });
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      setDeleteData({ ...deleteData, deleteLoading: false });
    }
  };

  const handleEditDocument = (documentData) => {
    setModalType(Common.UPDATE);
    setEditLists({ qhseDocument: documentData });
    setBody({
      qhseDocument: {
        title: documentData.documentName,
        document: [
          {
            url: documentData.documentUrl,
            name: documentData.documentName,
          },
        ],
      },
    });
    setOpenUploadModal(true);
  };

  const handleViewImage = (imageUrl) => {
    setFullScreenImage(imageUrl);
  };

  const handleCloseFullView = () => {
    setFullScreenImage(null);
  };
  const handleReload = async () => {
    setLoadingStatus(Constants.PENDING);
    setNext(0);
    fetchDocuments();
  };

  // Form handling functions
  const handleChange = (e) => {
    const { name, value } = e.target;
    setBody((prevBody) => ({
      ...prevBody,
      qhseDocument: {
        ...prevBody.qhseDocument,
        [name]: value,
      },
    }));
  };

  const handleImageChange = (imageValues, name) => {
    const imageInfoArray = {
      url: imageValues[0].url,
      name: imageValues[0].name,
    };
    setBody((prevBody) => ({
      ...prevBody,
      qhseDocument: {
        ...prevBody.qhseDocument,
        [name]: imageInfoArray,
      },
    }));
  };

  const handleImageCancel = (fieldName, updatedImageUrl) => {
    const newImageUrlArray = updatedImageUrl;
    const filteredImageUrlArray = newImageUrlArray.filter((img) => img.url !== "Frame");

    setBody((prevBody) => ({
      ...prevBody,
      qhseDocument: {
        ...prevBody.qhseDocument,
        [fieldName]: filteredImageUrlArray,
      },
    }));
  };

  const validation = () => {
    const { qhseDocument } = body;
    const tempError = {};

    const validateField = (fieldName, value, validatorType) => {
      const fieldValue = Validators.validate(
        validatorType,
        value || "",
        null,
        null,
        null,
        fieldName || ""
      );
      if (fieldValue !== "") tempError[fieldName] = fieldValue;
    };

    validateField("title", qhseDocument?.title, "basic");
    validateField("document", qhseDocument?.document?.toString(), "basic");

    const isValid = Object.keys(tempError).length === 0;
    setError({
      qhseDocument: tempError,
    });
    return isValid;
  };

  const handleCreate = async () => {
    setLoading(true);
    const isValid = validation();

    if (isValid) {
      setDisableSubmit(true);

      const requestBody = {
        documentName: body.qhseDocument.title.trim(),
        documentUrl: body.qhseDocument.document?.url || "",
      };

      const res = await dispatch(createQHSEDocument(requestBody));
      setDisableSubmit(false);
      if (res.payload.status === Common.API_STATUS_200) {
        dispatch(
          openSnackbar({
            message: Constants.DOCUMENT_CREATED_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );

        handleCloseUploadModal();
        fetchDocuments();
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setDisableSubmit(false);
      }
    }
    setLoading(false);
  };

  const handleUpdate = async () => {
    setLoading(true);
    const isValid = validation();

    if (isValid) {
      setDisableSubmit(true);

      let currentDocumentUrl;

      if (body.qhseDocument.document?.url) {
        currentDocumentUrl = body.qhseDocument.document.url;
      } else if (body.qhseDocument.document?.[0]?.url) {
        currentDocumentUrl = body.qhseDocument.document[0].url;
      } else {
        currentDocumentUrl = editLists.qhseDocument.documentUrl;
      }

      const requestBody = {
        documentName: body.qhseDocument.title.trim(),
        documentUrl: currentDocumentUrl,
      };

      const res = await dispatch(
        updateQHSEDocument({
          id: editLists.qhseDocument[Constants.MONGOOSE_ID],
          body: requestBody,
        })
      );
      setDisableSubmit(false);
      if (res.payload.status === Common.API_STATUS_200) {
        const updatedDocumentList = documentList.map((doc) => {
          if (doc[Constants.MONGOOSE_ID] === editLists.qhseDocument[Constants.MONGOOSE_ID]) {
            return {
              ...doc,
              documentName: requestBody.documentName.trim(),
              documentUrl: requestBody.documentUrl,
            };
          }
          return doc;
        });
        setDocumentList(updatedDocumentList);

        dispatch(
          openSnackbar({
            message: Constants.DOCUMENT_UPDATED_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );

        handleCloseUploadModal();
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        setDisableSubmit(false);
      }
    }
    setLoading(false);
  };

  // Get table data
  const { QHSEDocumentColumns, QHSEDocumentRows } = QHSEDocumentData(
    documentList,
    handleOpenUploadModal,
    modaltype,
    editLists,
    handleEditDocument,
    handleOpenDeleteModal,
    handleViewImage
  );

  useEffect(() => {
    (async () => {
      fetchDocuments();
    })();
  }, []);

  const handleTablePagination = async () => {
    const data = new URLSearchParams({
      page: next + 1,
      perPage: tablePagination.perPage,
    });
    const res = await dispatch(getAllQHSEDocuments(data));
    if (res.payload.status === Common.API_STATUS_200) {
      setDocumentList([...documentList, ...res.payload.data.data]);
      setNext(res.payload.data.data.length > 0 ? next + 1 : next);
    }
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between" mt={1}>
        <PageTitle title={PageTitles.QHSE_DOCUMENTS} />
        <MDBox display="flex" gap={1}>
          <BasicButton
            title={ButtonTitles.UPLOAD_DOCUMENT}
            icon={Icons.EXPORT}
            background={Colors.PRIMARY}
            color={Colors.WHITE}
            action={() => handleOpenUploadModal()}
          />
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleReload}
          />
        </MDBox>
      </MDBox>

      <MDBox mt={2} mb={2}>
        <DataTable
          table={{ columns: QHSEDocumentColumns, rows: QHSEDocumentRows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
          showTotalEntries={false}
          noEndBorder
          loading={loadingStatus}
          currentPage={tablePagination.page}
          handleTablePagination={handleTablePagination}
          handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
        />
      </MDBox>

      {/* Upload Document Modal */}
      <NewProject
        title={
          modaltype === Common.NEW
            ? ModalContent.NEW_DOCUMENT_TITLE
            : ModalContent.EDIT_DOCUMENT_TITLE
        }
        openNewProject={openUploadModal}
        handleCloseNewProject={handleCloseUploadModal}
        handleSave={() => (modaltype === Common.NEW ? handleCreate() : handleUpdate())}
        actionButton={loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
        disabled={disableSubmit}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <FTextField
              label={Common.DOCUMENT_NAME}
              name="title"
              placeholder={Common.DOCUMENT_NAME}
              type={BackendFrontend.TEXT}
              error={Boolean(error.qhseDocument.title)}
              helperText={error.qhseDocument.title}
              value={modaltype === Common.NEW ? "" : body.qhseDocument?.title || ""}
              handleChange={handleChange}
            />
          </Grid>

          <Grid item xs={12}>
            <MDBox mt={2}>
              <ImageUpload
                label={modaltype !== Common.NEW ? Common.UPDATE_DOCUMENT : Common.UPLOAD_DOCUMENT}
                name="document"
                data={
                  modaltype === Common.UPDATE && body.qhseDocument?.document
                    ? body.qhseDocument.document
                    : []
                }
                onImageUpload={(imageValues) => handleImageChange(imageValues, "document")}
                onImageCancel={(updatedImageUrl) => handleImageCancel("document", updatedImageUrl)}
                error={error.qhseDocument?.document}
                helperText={error.qhseDocument?.document}
                type="QHSE_Documents"
                formats={pattern.IMAGE_TYPES}
                acceptType=""
                maxImageCount={1}
                imageTypeError={Constants.IMAGE_UPLOAD_ERROR}
                sizeUpdate
              />
            </MDBox>
          </Grid>
        </Grid>
      </NewProject>

      {/* Delete Modal */}
      <DeleteModal
        open={deleteData.openDeleteModal}
        title={ModalContent.DELETE_DOCUMENT_TITLE}
        message={ModalContent.DELETE_DOCUMENT_MESSAGE}
        handleClose={handleCloseDeleteModal}
        handleDelete={handleDeleteDocument}
        loading={deleteData.deleteLoading}
      />

      {/* View Image Modal */}
      <FullScreenImageComponent
        fullScreenImage={fullScreenImage}
        handleCloseFullView={handleCloseFullView}
        src={fullScreenImage}
      />
    </DashboardLayout>
  );
}

export default QHSEDocuments;

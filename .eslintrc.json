{"env": {"browser": true, "es2021": true}, "extends": ["plugin:react/recommended", "airbnb", "prettier"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "prettier"], "rules": {"no-param-reassign": ["error", {"props": true, "ignorePropertyModificationsFor": ["state"]}], "prettier/prettier": ["error", {"endOfLine": "auto"}], "default-param-last": "off", "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/jsx-props-no-spreading": [1, {"custom": "ignore"}], "react/jsx-curly-spacing": [2, "never"]}, "settings": {"import/resolver": {"node": {"paths": ["src"]}}}}
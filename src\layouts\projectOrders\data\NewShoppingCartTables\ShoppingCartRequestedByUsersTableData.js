import React, { useMemo, useState } from "react";
import PropTypes from "prop-types";

// 3rd Party libraries
import { I<PERSON><PERSON><PERSON><PERSON>, Popover } from "@mui/material";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";

// Utils
import { Icons } from "utils/Constants";
import { getFormattedCallingFirstName } from "utils/methods/methods";

// Assets
import User from "assets/images/UserDefault.png";
import pxToRem from "assets/theme/functions/pxToRem";

// Comment Popover for long comments
function CommentPopover({ comment = [] }) {
  const newComment = comment?.[0]?.comment || "";

  const [anchorEl, setAnchorEl] = useState(null);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handlePopoverOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <MDBox display="flex" alignItems="center" maxWidth="180px" justifyContent="space-between">
        {/* Truncate text dynamically */}
        <MDTypography
          sx={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            maxWidth: "150px",
            display: "inline-block",
          }}
        >
          {newComment}
        </MDTypography>

        {/* Show info icon only if comment is long */}
        {(comment.length > 1 || newComment.length > 10) && (
          <IconButton size="small" onClick={handlePopoverOpen}>
            {Icons.INFO}
          </IconButton>
        )}
      </MDBox>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        PaperProps={{
          sx: {
            mt: 3,
            backgroundColor: "#fff !important", // Ensure visibility
            boxShadow: 3, // Elevation
            maxWidth: "300px",
            borderRadius: "8px", // Smooth corners
            border: "1px solid #ddd", // Subtle border
          },
        }}
      >
        {comment.map((item, index) => (
          <MDBox key={item.time}>
            <MDTypography>{`${index + 1}. ${item.comment}`}</MDTypography>
          </MDBox>
        ))}
      </Popover>
    </>
  );
}

CommentPopover.propTypes = {
  comment: PropTypes.string.isRequired,
};

export default function ShoppingCartRequestedByUsersTableData({ dataList = [] }) {
  const requestedByUsersRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      requestedBy: (
        <MDBox display="flex" justifyContent="center" alignItems="center">
          <img
            src={item?.profileImage ? item?.profileImage : User}
            alt="User"
            key={item?.profileImage}
            style={{
              width: pxToRem(50),
              height: pxToRem(50),
              marginRight: pxToRem(10),
              borderRadius: "8px",
            }}
          />

          <Author name={getFormattedCallingFirstName(item)} />
        </MDBox>
      ),
      requestedQuantity: <Author name={item.engineerRequestedQuantity} />,

      temporaryProductName: <Author name={item.temporaryProductName} />,
      comments: <CommentPopover comment={item?.engineerComment || []} />,
    }));
  }, [dataList]);

  const requestedByUsersColumns = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Requested By", accessor: "requestedBy" },
    { Header: "Requested Qty", accessor: "requestedQuantity" },
    { Header: "Comments", accessor: "comments" },
  ];

  const tableData = {
    requestedByUsersColumns,
    requestedByUsersRows,
  };

  return tableData;
}

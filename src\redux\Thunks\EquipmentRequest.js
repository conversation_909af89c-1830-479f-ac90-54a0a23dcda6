import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import { filterProjectStatusFunc, normalizeParamsAndAddValues } from "utils/methods/methods";
import ApiService from "../ApiService/ApiService";

const projectOrderRequestListing = createAsyncThunk(
  "project-order-Request-List/api",
  async (param) => {
    const projectStatus = filterProjectStatusFunc();
    const projectStatusObj = { projectStatus };

    const updatedParams = normalizeParamsAndAddValues(param, projectStatusObj);

    const res = await ApiService.get(`equipment-orders/orders?${updatedParams}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((error) => error.response);
    const params = new URLSearchParams(updatedParams);
    const page = params.get("page");
    const status = params.get("status");
    if (res.status === 200)
      return page === "0"
        ? { data: res.data, type: "add", status: res.status, currentStatus: status }
        : { data: res.data, type: "append", status: res.status, currentStatus: status };
    throw res;
  }
);
export const getPMOrders = createAsyncThunk("get-pm-orders/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`pm-order?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((error) => error.response);
  return res;
});

export const getPMUserOrders = createAsyncThunk("get-pm-user-orders/api", async (param) => {
  const res = await ApiService.get(`pm-order-request/user-ordered-equipments?${param}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((error) => error.response);
  const params = new URLSearchParams(param);
  const page = params.get("page");
  return page === "0"
    ? { data: res.data, type: "add", status: res.status }
    : { data: res.data, type: "append", status: res.status };
});
export const orderNumberListThunk = createAsyncThunk("order-number/api", async () => {
  const res = await ApiService.get(`pm-order-request/order-number-list`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});
export const equipmentPermissionThunk = createAsyncThunk(
  "equipment-order-requestss/api",
  async (param) => {
    const res = await ApiService.patch(
      `v2/equipment-orders/change-status?${param.id}`,
      { ...param.status },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const addToQueueEquipment = createAsyncThunk("add-queue/api", async (body) => {
  const res = await ApiService.post(
    `v2/pm-order/add-to-queue`,
    { ...body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const orderEquipment = createAsyncThunk("order-equipment/api", async (body) => {
  const res = await ApiService.patch(
    `v2/pm-order/request/${body.id}`,
    { ...body.body },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const updateApproverCommentThunk = createAsyncThunk(
  "update-approver-comment-queue",
  async (body) => {
    const res = await ApiService.patch(
      `pm-order/order-details/${body.id}`,
      { ...body.body },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const getPMReturnOrders = createAsyncThunk("get-pm-return-orders/api", async (param) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(param, projectStatusObj);

  const res = await ApiService.get(`return-order/pm-return-order/projects?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((error) => error.response);

  const params = new URLSearchParams(updatedParams);
  const page = params.get("page");
  const status = params.get("status");

  if (res.status === 200)
    return page === "0"
      ? { data: res.data, type: "add", status: res.status, currentStatus: status }
      : { data: res.data, type: "append", status: res.status, currentStatus: status };
  throw res;
});

export const getProjectReturnOrderById = createAsyncThunk(
  "get-project-return-order-by-id/api",
  async (payload) => {
    let url = `return-order/pm-return-order/projects/${payload.id}`;
    if (payload.search) {
      url = `${url}?search=${payload.search}`;
    }
    const res = await ApiService.get(url, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const getPMOrderDetails = createAsyncThunk("pm-order-details/api", async (data) => {
  const res = await ApiService.get(`equipment-orders/project/${data.id}?${data.params}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

// Project Order History
export const getProjectOrderHistory = createAsyncThunk(
  "get-project-order-history/api",
  async (params) => {
    const res = await ApiService.get(`pm-order/projects?${params}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const getProjectOrderHistoryById = createAsyncThunk(
  "get-project-order-history-by-id/api",
  async (payload) => {
    let url = `pm-order/projects/${payload.id}`;
    if (payload.search) {
      url = `${url}?search=${payload.search}`;
    }
    const res = await ApiService.get(url, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

// Equipment Request add API
export const engineerRequestedEquipmentThunk = createAsyncThunk(
  "engineer-req-equipment/api",
  async (body) => {
    const res = await ApiService.post(
      `v2/equipment-orders`,
      { ...body },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

// Delete Equipment Request API For Shopping List Delete Functionality
export const deleteRequestedEquipmentThunk = createAsyncThunk(
  "delete-Requested-equipment/api",
  async (deleteIdList, { rejectWithValue }) => {
    try {
      const res = await ApiService.delete(`v2/equipment-orders`, {
        data: { equipmentOrderIds: deleteIdList },
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      });
      return res;
    } catch (error) {
      return rejectWithValue(error.response?.data || "An error occurred");
    }
  }
);

// New Shopping cart create API
export const newShoppingListCreateThunk = createAsyncThunk(
  "new-shopping-list/api",
  async (body) => {
    const res = await ApiService.post(
      `v2/equipment-orders/shopping-cart`,
      { ...body },
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const projectOrderShoppingListing = createAsyncThunk(
  "project-order-Shopping-List/api",
  async (params) => {
    if (params.has("status")) {
      params.delete("status");
    }

    const res = await ApiService.get(
      `equipment-orders/shopping-cart-project-equipment-type?${params}`,
      {
        headers: { Authorization: `Bearer ${Sessions.userToken}` },
      }
    )
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

export const getShoppingListById = createAsyncThunk("get-shopping-list-by-id/api", async (id) => {
  const res = await ApiService.get(`shopping-cart/${id}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export const getShoppingDetailsById = createAsyncThunk(
  "get-shopping-details-by-id/api",
  async (id) => {
    const res = await ApiService.get(`equipment-orders/shopping-cart/${id}`, {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    })
      .then((r) => r)
      .catch((err) => err.response);
    return res;
  }
);

// Shopping Cart Reject API
export const shoppingCartReject = createAsyncThunk("shopping-cart-reject/api", async (body) => {
  const res = await ApiService.patch(
    `shopping-cart/${body.id}`,
    { ...body.data },
    {
      headers: { Authorization: `Bearer ${Sessions.userToken}` },
    }
  )
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export default projectOrderRequestListing;

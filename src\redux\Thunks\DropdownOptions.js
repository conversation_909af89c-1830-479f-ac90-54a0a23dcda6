import Sessions from "utils/Sessions";
import { createAsyncThunk } from "@reduxjs/toolkit";
import ApiService from "redux/ApiService/ApiService";

// For Certificate Dropdown Options
const getCertificateTypeOptions = createAsyncThunk("/certificates-options", async () => {
  const res = await ApiService.get("/certificate-type", {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

// For user Dropdown Options
export const UserOptionsListThunk = createAsyncThunk("userDropdownOptions/api", async (param) => {
  const queryString = param ? `?${param}` : "";
  const res = await ApiService.get(`users${queryString}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  return res;
});

export default getCertificateTypeOptions;

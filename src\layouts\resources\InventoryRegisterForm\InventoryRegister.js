import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";

// 3rd party library imports
import { IconButton } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";

// Material Dashboard React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import MDButton from "components/MDButton";
import FontComponent from "components/Responsive/fonts";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import { ButtonBreakPoint } from "components/Responsive/BreakPoints";

// Material Dashboard React example components
import CustomButton from "examples/NewDesign/CustomButton";

// Redux imports
import ProductListThunk, {
  equipmentByIdThunk,
  equipmentUpdateThunk,
  CreateNewProduct,
  equipmentDeleteThunk,
} from "redux/Thunks/Equipment";
import { openSnackbar } from "redux/Slice/Notification";
import { resetListState } from "redux/Slice/Equipment";
import { equipmentConfig } from "redux/Thunks/Config";
import exportImportSampleFileThunk from "redux/Thunks/Other";

// Function imports from assets
import pxToRem from "assets/theme/functions/pxToRem";

// Constant imports from utils
import Constants, {
  Icons,
  Common,
  PageTitles,
  ButtonTitles,
  Colors,
  BackendFrontend,
  ModalContent,
} from "utils/Constants";

// Tabs of this component
import EquipmentDetailsTab from "./InventoryRegisterTabs/EquipmentDetailsTab";
import EquipmentDocumentsTab from "./InventoryRegisterTabs/EquipmentDocumentsTab";
import WarehouseInfoTab from "./InventoryRegisterTabs/WarehouseInfoTab";

function InventoryRegister({ closeDrawerFunc, equipmentId, setShouldUpdateState }) {
  const fontSize = FontComponent({ sizes: ButtonBreakPoint.smallTitleBreakPoint });

  const dispatch = useDispatch();
  const configData = useSelector((state) => state.config);
  const permission = configData?.equipmentConfig?.agreement;

  const [formData, setFormData] = useState([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [formValues, setFormValues] = useState({});
  const [updatedFormValues, setUpdatedFormValues] = useState({});
  const [searchData, setSearchData] = useState({});
  const [updatedBody, setUpdatedBody] = useState({});
  const [currency, setCurrency] = useState(null);
  const [errors, setErrors] = useState({});

  const [disableSubmit, setDisableSubmit] = useState(false);
  const [loading, setLoading] = useState(false);

  // equipment document tab variable
  const [documentForm, setDocumentForm] = useState([]);
  const [equipDocumentData, setEquipDocumentData] = useState({
    errors: {},
  });

  // Equipment Warehouse tab variable
  const [quantityType, setQuantityType] = useState(false);
  const [warehouseData, setWarehouseData] = useState({
    errors: {},
  });

  // Utility Functions starts from here
  const handleCloseDrawerFunc = () => {
    closeDrawerFunc();
    setFormData([]);
    setFormValues({});
    setUpdatedFormValues({});
    setSearchData({});
    setUpdatedBody({});
    setCurrency(null);
    setErrors({});
    setDisableSubmit(false);
    setLoading(false);
    setDocumentForm([]);
    setEquipDocumentData({ errors: {} });
    setWarehouseData({ errors: {} });
    setQuantityType(false);
  };

  // Loader while the data loads
  const checkloader = () => {
    if (equipmentId) {
      return Object.keys(formValues).length > 0;
    }
    return formData?.length > 0;
  };

  // Export Sample file
  const handleEquipmentSampleFileExport = async (exportFileName, root) => {
    const currentDate = new Date();
    const fileName = `Reynard_${root}_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.${
      exportFileName.split(".")[1]
    }`;

    const res = await dispatch(exportImportSampleFileThunk(exportFileName));
    const url = window?.URL?.createObjectURL(res.payload);
    const link = document?.createElement("a");
    link.href = url;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    link.remove();
  };

  // Form Validation Functions
  // Equipment Details tab Valdiation
  const validateEquipmentForm = () => {
    const newErrors = {};
    const equipmentArr = configData?.equipmentConfig?.properties?.equipment;

    if (equipmentArr) {
      equipmentArr?.forEach((item) => {
        const checkValue =
          item?.parentFieldId !== "" ? item?.dependentIds.includes(formValues?.equipmentType) : {};
        const itemValue = formValues?.[item.id] || updatedBody?.[item.id];

        if (item?.IsRequired) {
          const dynamicField = formValues?.dynamicFields?.find(
            (val) => val?.title.replace(/\s/g, "") === item.id
          );

          if (!item?.questionId && !itemValue && item?.parentFieldId === "") {
            newErrors[item.id] = item?.hint;
          } else if (item?.questionId && !dynamicField) {
            newErrors[item.id] = item?.hint;
          } else if (
            !item?.questionId &&
            typeof itemValue === "string" &&
            itemValue.trim() === ""
          ) {
            newErrors[item.id] = Constants.INVALID_VALUE;
          } else if (
            !item?.questionId &&
            item?.type === BackendFrontend.OPTIONS &&
            item?.parentFieldId === "" &&
            (!itemValue || itemValue.length === 0)
          ) {
            newErrors[item.id] = item?.hint;
          } else if (
            !item?.questionId &&
            item?.type === BackendFrontend.OPTIONS &&
            item?.parentFieldId !== "" &&
            Object.keys(formValues).findIndex(
              (key) => key?.toLocaleLowerCase() === item?.parentFieldId?.toLocaleLowerCase()
            ) === -1
          ) {
            newErrors[item.id] = item?.hint;
          } else if (item?.questionId && dynamicField?.value[0]?.trim() === "") {
            newErrors[item.id] = Constants.INVALID_VALUE;
          } else if (
            item?.type === BackendFrontend.DECIMAL_NUMBER &&
            parseFloat(itemValue, 10) === 0
          ) {
            newErrors[item.id] = Constants.INVALID_VALUE;
          }
        }

        if (
          !item.IsRequired &&
          !itemValue &&
          checkValue === true &&
          item?.type === BackendFrontend.OPTIONS_VALUES
        ) {
          newErrors[item.id] = Constants.TOTAL_VALUE_REQUIRED;
        } else if (
          !item.questionId &&
          item.type === BackendFrontend.OPTIONS_VALUES &&
          itemValue <= 0
        ) {
          newErrors[item.id] = Constants.INVALID_TOTAL_VALUE;
        }
      });
    }

    setErrors(newErrors);
    return Object.values(newErrors).filter((val) => val !== "").length === 0;
  };

  // Document tab Valdiation
  const validateEquipmentDocForm = () => {
    const newErrors = {};
    const imagePriceArr = configData?.equipmentConfig?.properties?.imageSpecificationAndPrice;

    if (imagePriceArr) {
      imagePriceArr?.forEach((elem) => {
        if (elem.IsRequired) {
          const itemValue = equipDocumentData?.[elem.id];
          const dynamicField = equipDocumentData?.dynamicFields?.find(
            (val) => val?.title.replace(/\s/g, "") === elem.id
          );

          if (
            (!elem?.questionId && !itemValue && elem?.parentFieldId === "") ||
            (elem?.type === BackendFrontend.IMAGES && itemValue?.length === 0)
          ) {
            newErrors[elem.id] = elem?.hint;
          } else if (elem?.questionId && !dynamicField) {
            newErrors[elem.id] = elem?.hint;
          } else if (
            !elem?.questionId &&
            typeof itemValue === "string" &&
            itemValue?.trim() === ""
          ) {
            newErrors[elem.id] = Constants.INVALID_VALUE;
          } else if (
            !elem?.questionId &&
            elem?.type === BackendFrontend.OPTIONS &&
            elem?.parentFieldId === "" &&
            (!itemValue || itemValue.length === 0)
          ) {
            newErrors[elem.id] = elem?.hint;
          } else if (
            !elem?.questionId &&
            elem?.type === BackendFrontend.OPTIONS &&
            elem?.parentFieldId !== "" &&
            Object.keys(equipDocumentData).findIndex(
              (key) => key.toLocaleLowerCase() === elem.parentFieldId.toLocaleLowerCase()
            ) === -1
          ) {
            newErrors[elem.id] = elem.hint;
          } else if (elem.questionId && dynamicField?.value[0]?.trim() === "") {
            newErrors[elem.id] = Constants.INVALID_VALUE;
          } else if (!elem?.questionId && elem?.type === BackendFrontend.NUMBER && itemValue < 0) {
            newErrors[elem.id] = Constants.INVALID_VALUE;
          }
        }
      });
    }

    setEquipDocumentData((prev) => ({
      ...prev,
      errors: newErrors,
    }));
    return Object.values(newErrors).filter((val) => val !== "").length === 0;
  };

  // Warehouse tab Valdiation
  const validateWarehouseForm = () => {
    const newErrors = {};
    const warehouseArr = configData?.equipmentConfig?.properties?.warehouseInfo;

    if (warehouseArr) {
      warehouseArr.forEach((item) => {
        if (item?.IsRequired) {
          const itemValue = warehouseData?.[item.id];
          const dynamicField = warehouseData?.dynamicFields?.find(
            (val) => val?.title.replace(/\s/g, "") === item.id
          );

          if (!item?.questionId && !itemValue && item?.parentFieldId === "") {
            newErrors[item.id] = item?.hint;
          } else if (item?.questionId && !dynamicField) {
            newErrors[item.id] = item?.hint;
          } else if (
            !item?.questionId &&
            typeof itemValue === "string" &&
            itemValue?.trim() === ""
          ) {
            newErrors[item.id] = Constants.INVALID_VALUE;
          } else if (
            !item?.questionId &&
            item?.type === BackendFrontend.OPTIONS &&
            item?.parentFieldId === "" &&
            (!itemValue || itemValue?.length === 0)
          ) {
            newErrors[item.id] = item.hint;
          } else if (
            !item?.questionId &&
            item?.type === BackendFrontend.OPTIONS &&
            item?.parentFieldId !== "" &&
            Object.keys(warehouseData).findIndex(
              (key) => key.toLocaleLowerCase() === item.parentFieldId.toLocaleLowerCase()
            ) === -1
          ) {
            newErrors[item.id] = item?.hint;
          } else if (item?.questionId && dynamicField?.value[0]?.trim() === "") {
            newErrors[item.id] = Constants.INVALID_VALUE;
          } else if (!item?.questionId && item?.type === BackendFrontend.NUMBER) {
            if ((itemValue?.toString()?.includes(".") || itemValue <= 0) && !equipmentId) {
              newErrors[item.id] = Constants.INVALID_VALUE;
            } else if (!itemValue && !equipmentId) {
              newErrors[item.id] = item?.hint;
            }
          }
        }
      });
    }
    setWarehouseData((prev) => ({
      ...prev,
      errors: newErrors,
    }));
    return Object.values(newErrors).filter((val) => val !== "").length === 0;
  };

  const handleSubmitEquipmentFunc = async () => {
    setDisableSubmit(true);
    setLoading(true);
    const equipmentDetailForm = await validateEquipmentForm();
    const documentDetailForm = await validateEquipmentDocForm();
    const warehouseDetailForm = await validateWarehouseForm();

    if (equipmentDetailForm && documentDetailForm && warehouseDetailForm) {
      const { equipmentType, ...filteredBody } = updatedBody;

      const body = {
        ...updatedFormValues,
        ...filteredBody,
        equipmentType: equipmentType || updatedFormValues.equipmentType,
        name: updatedBody.name,
        serialNumber: updatedBody.serialNumber ? updatedBody.serialNumber : null,
      };

      const tempBody = { ...body };

      Object.entries(body).forEach(([key]) => {
        if (typeof tempBody[key] === "string") {
          tempBody[key] = tempBody[key].trim();
        } else if (typeof tempBody[key] === "object" && tempBody[key]?.length === 0) {
          tempBody[key] = null;
        }
      });

      try {
        if (equipmentId) {
          const data = {
            body: tempBody,
            id: equipmentId,
          };

          const res = await dispatch(equipmentUpdateThunk(data));
          if (res?.payload?.status === Common.API_STATUS_200) {
            await dispatch(
              openSnackbar({
                message: Constants.EQUIPMENT_UPDATE_SUCCESS,
                notificationType: Constants.NOTIFICATION_SUCCESS,
              })
            );
            setShouldUpdateState((prev) => !prev);
            handleCloseDrawerFunc();
          } else if (res?.payload?.status === Common.API_STATUS_422) {
            dispatch(
              openSnackbar({
                message: res.payload.data.data.error[0].equipmentType,
                notificationType: Constants.NOTIFICATION_ERROR,
              })
            );
          } else {
            dispatch(
              openSnackbar({
                message: Constants.SOMETHING_WENT_WRONG,
                notificationType: Constants.NOTIFICATION_ERROR,
              })
            );
          }
        } else {
          const res = await dispatch(CreateNewProduct(body));
          if (res?.payload?.status === Common.API_STATUS_200) {
            await dispatch(
              openSnackbar({
                message: Constants.EQUIPMENT_CREATE_SUCCESS,
                notificationType: Constants.NOTIFICATION_SUCCESS,
              })
            );
            dispatch(resetListState());
            setShouldUpdateState((prev) => !prev);
            handleCloseDrawerFunc();
          } else if (res?.payload?.status === Common.API_STATUS_422) {
            const newErrors = {};
            newErrors.serialNumber = res.payload.data.data.error[0].serialNumber;
            setErrors(newErrors);
            dispatch(
              openSnackbar({
                message: res.payload.data.data.error[0].serialNumber,
                notificationType: Constants.NOTIFICATION_ERROR,
              })
            );
          } else {
            dispatch(
              openSnackbar({
                message: Constants.SOMETHING_WENT_WRONG,
                notificationType: Constants.NOTIFICATION_ERROR,
              })
            );
          }
        }
      } catch (error) {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else {
      dispatch(
        openSnackbar({
          message: Constants.FILL_ALL_REQUIRED_FIELDS,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setLoading(false);
    setDisableSubmit(false);
  };

  const setCurrencyDetailFunc = (currencyValue = "") => {
    setCurrency(currencyValue);
  };

  const setEquipmentFormDetailsFunc = (
    parsedEquipmentArr = [],
    quantityTypeValue,
    resObj,
    fromSearchFunc = true
  ) => {
    const equipmentDetailsList = [...parsedEquipmentArr];
    const temp = { ...formValues };
    const equipmentDataList = configData?.equipmentConfig?.properties?.equipment || [];

    equipmentDataList.forEach((elem, index) => {
      switch (elem?.type) {
        case BackendFrontend.TEXT:
          {
            const textObj = { ...elem };

            if (
              textObj?.parentFieldId !== "" &&
              textObj?.id === Common.SERIAL_NUMBER &&
              quantityTypeValue === Common.UNIQUE
            ) {
              setQuantityType(true);
              textObj.isDefaultVisible = true;
              textObj.IsRequired = true;
              if (!fromSearchFunc) {
                temp[elem.id] = resObj?.[elem.id];
                setUpdatedBody((prev) => ({
                  ...prev,
                  [elem.id]: resObj?.[elem.id],
                }));
              }
            } else if (quantityType === false) {
              temp[elem.id] = resObj?.[elem.id];
            }
            equipmentDetailsList[index] = textObj;
          }
          break;

        case BackendFrontend.NUMBER:
        case BackendFrontend.SEARCH:
        case BackendFrontend.DECIMAL_NUMBER:
          temp[elem.id] = resObj?.[elem.id];
          setUpdatedBody((prev) => ({
            ...prev,
            [elem.id]: resObj?.[elem.id],
          }));
          break;

        case BackendFrontend.OPTIONS:
          {
            const optionsObj = { ...elem };

            if (elem?.parentFieldId?.trim() === "" && elem?.id === Common.EQUIPMENT_TYPE) {
              const equipmentType = resObj?.[elem?.id];

              if (equipmentType) {
                temp[elem.id] = resObj?.equipmentType?.type;
                const tempId = resObj?.[elem.id][Constants.MONGOOSE_ID];

                setUpdatedBody((prev) => ({
                  ...prev,
                  [elem.id]: tempId,
                }));
              } else {
                temp[elem.id] = resObj?.[elem.id]?.type;
              }
            } else if (elem?.parentFieldId?.trim() !== "" && elem?.id === Common.WEIGHT_FORM) {
              temp[elem.id] = !fromSearchFunc
                ? resObj?.equipmentType?.equipmentUnit?.title
                : resObj?.equipmentType?.equipmentUnit?.[0]?.title;
            } else if (elem?.parentFieldId !== "" && elem?.id === Common.QUANTITY_TYPE) {
              let quantityData = {};
              if (!fromSearchFunc) {
                quantityData = resObj?.equipmentType?.quantityType || {};
              } else {
                quantityData = resObj?.equipmentType?.quantityType?.[0] || {};
              }
              const { name = "", priceType = "", quantityType: quantType = "" } = quantityData;
              temp[elem.id] = `${name} (${priceType}, ${quantType})`;
            } else if (elem?.parentFieldId !== "" && elem?.id === Common.PRICE) {
              temp[elem?.id] = resObj?.equipmentType?.price;
            } else if (elem?.parentFieldId !== "" && elem?.id === Common.EQUIPMENT_CATEGORY) {
              let nameData = {};
              if (!fromSearchFunc) {
                nameData = resObj?.equipmentType?.equipmentCategory || {};
              } else {
                nameData = resObj?.equipmentType?.equipmentCategory?.[0] || {};
              }
              temp[elem?.id] = nameData?.name;
            } else if (elem?.parentFieldId !== "" && elem?.id === Common.HS_CODE) {
              let hsCodeObj = {};
              if (!fromSearchFunc) {
                hsCodeObj = resObj?.equipmentType?.hsCode || {};
              } else {
                hsCodeObj = resObj?.equipmentType?.hsCode?.[0] || {};
              }
              const { name = "", code = "" } = hsCodeObj;
              temp[elem?.id] = `${name} (${code})`;
            }
            if (optionsObj?.type === BackendFrontend.OPTIONS && optionsObj?.id === Common.PRICE) {
              const equipTypeId = resObj?.equipmentType?.[Constants.MONGOOSE_ID];

              if (optionsObj?.dependentIds?.includes(equipTypeId)) {
                optionsObj.isDefaultVisible = true;
                optionsObj.IsRequired = true;
              } else if (!optionsObj?.dependentIds?.includes(equipTypeId)) {
                optionsObj.isDefaultVisible = false;
                optionsObj.IsRequired = false;
              }
            }
            equipmentDetailsList[index] = optionsObj;
          }
          break;

        case BackendFrontend.OPTIONS_VALUES:
          {
            const updatedElem = { ...elem };
            let quantObj = {};

            if (!fromSearchFunc) {
              quantObj = resObj.equipmentType.quantityType || {};
            } else {
              quantObj = resObj.equipmentType.quantityType?.[0] || {};
            }

            const { priceType = "" } = quantObj;

            if (
              updatedElem.parentFieldId !== "" &&
              updatedElem?.type === BackendFrontend.OPTIONS_VALUES &&
              priceType === Common.RENTAL
            ) {
              updatedElem.isDefaultVisible = true;
              updatedElem.IsRequired = true;
              temp[elem.id] = resObj?.[elem.id];

              if (fromSearchFunc) {
                setUpdatedBody((prev) => ({
                  ...prev,
                  [elem.id]: resObj?.[elem.id],
                }));
              }
            }
            equipmentDetailsList[index] = updatedElem;
          }
          break;

        default:
          break;
      }
    });

    setFormValues({ ...temp });

    // Setting values for Equipment Section
    setFormData(equipmentDetailsList);
  };

  const setEquipmentDocumentFormFunc = (imagePriceArr = [], resObj = {}, fromSearchFunc = true) => {
    const parsedImagePriceArr = [...imagePriceArr];
    const tempEquipDocs = { ...equipDocumentData };
    const { imageSpecificationAndPrice = [] } = configData?.equipmentConfig?.properties || {};

    imageSpecificationAndPrice?.forEach((elem, index) => {
      switch (elem?.type) {
        case BackendFrontend.TEXT:
        case BackendFrontend.NUMBER:
        case BackendFrontend.DATE:
        case BackendFrontend.IMAGES:
          tempEquipDocs[elem.id] = resObj?.[elem.id];
          if (fromSearchFunc) {
            setUpdatedBody((prev) => ({
              ...prev,
              [elem.id]: resObj?.[elem?.id],
            }));
          }
          break;

        // case BackendFrontend.CERTIFICATE_TYPE && !fromSearchFunc: don't remove this comment to see future issue
        case BackendFrontend.CERTIFICATE_TYPE:
          if (elem?.id === BackendFrontend.CERTIFICATE_TYPE) {
            tempEquipDocs[elem.id] = resObj?.certificateType;
            setUpdatedBody((prev) => ({
              ...prev,
              [elem.id]: tempEquipDocs[elem?.id],
            }));
          }
          break;

        case BackendFrontend.VALIDITY_DATE && !fromSearchFunc:
          {
            const updatedItem = { ...elem };
            const certificateType = resObj?.certificateType;

            if (
              tempEquipDocs[elem.id] !== null &&
              updatedItem.parentFieldId !== "" &&
              certificateType !== null &&
              updatedItem.dependentIds.includes(certificateType[Constants.MONGOOSE_ID])
            ) {
              updatedItem.isDefaultVisible = true;
              tempEquipDocs[elem.id] = resObj?.[elem.id];
            }
            parsedImagePriceArr[index] = updatedItem;
          }
          break;

        default:
          break;
      }
    });
    setEquipDocumentData({ ...tempEquipDocs });

    // Setting Values for Equipment Documents Section
    setDocumentForm(parsedImagePriceArr);
  };

  const setWarehouseInfoFormFunc = (resObjData = {}, fromSearchFunc = true) => {
    const resObj = { ...resObjData };
    if (resObjData.quantity === 0 || !resObjData.quantity) {
      resObj.quantity = 1;
    }
    const tempWarehouse = { ...warehouseData };
    const { warehouseInfo = [] } = configData?.equipmentConfig?.properties || {};

    warehouseInfo.forEach((elem) => {
      switch (elem?.type) {
        case BackendFrontend.TEXT:
        case BackendFrontend.NUMBER:
        case BackendFrontend.DATE:
          tempWarehouse[elem.id] = resObj?.[elem?.id];
          if (fromSearchFunc) {
            setUpdatedBody((prev) => ({
              ...prev,
              [elem.id]: resObj?.[elem.id],
            }));
          }
          break;

        case BackendFrontend.OPTIONS:
          if (typeof resObj?.[elem?.id] === "object") {
            tempWarehouse[elem.id] = resObj?.[elem?.id]?.name;
            const tempId = resObj?.[elem.id]?.[Constants.MONGOOSE_ID];

            setUpdatedBody((prev) => ({
              ...prev,
              [elem.id]: tempId,
            }));
          } else {
            tempWarehouse[elem.id] = resObj?.[elem?.id];
            setUpdatedBody((prev) => ({
              ...prev,
              [elem.id]: resObj?.[elem.id],
            }));
          }
          break;

        default:
          break;
      }
    });
    setWarehouseData({ ...tempWarehouse });
  };

  const handleEditLoaderFunc = async (equipmentArr, imagePriceArr) => {
    const parsedEquipmentArr = equipmentArr ? JSON.parse(JSON.stringify(equipmentArr)) : [];
    const parsedImagePriceArr = imagePriceArr ? JSON.parse(JSON.stringify(imagePriceArr)) : [];

    if (equipmentId) {
      const res = await dispatch(equipmentByIdThunk(equipmentId));
      if (res?.payload?.status === Common.API_STATUS_200) {
        // Setting Currency Value by currencyFunc
        const currencyValue = res?.payload.data?.data?.equipmentType?.currencyUnit?.symbol;
        setCurrencyDetailFunc(currencyValue);

        const quantityTypeValue = res.payload.data.data?.equipmentType?.quantityType?.quantityType;
        const resObj = res.payload.data.data || {};

        // created common func for updating Equipment Details Data
        await setEquipmentFormDetailsFunc(parsedEquipmentArr, quantityTypeValue, resObj, false);

        // created common func for updating Equipment Documents Data
        await setEquipmentDocumentFormFunc(parsedImagePriceArr, resObj, false);

        // created common func for updating Equipment Warehouse info Data
        await setWarehouseInfoFormFunc(resObj, false);
      } else if (res?.payload?.status === Common.API_STATUS_404) {
        // close the drawer here
        handleCloseDrawerFunc();
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else {
      // Setting values for Equipment Information section
      setFormData(parsedEquipmentArr);

      // Setting Values for Equipment Documents Section
      setDocumentForm(parsedImagePriceArr);
    }
  };

  const handleSearchDataFunc = async (equipmentArr, imagePriceArr) => {
    const parsedEquipmentArr = equipmentArr ? JSON.parse(JSON.stringify(equipmentArr)) : [];
    const parsedImagePriceArr = imagePriceArr ? JSON.parse(JSON.stringify(imagePriceArr)) : [];
    if (searchData?.name && !equipmentId && configData?.equipmentLoading === Constants.FULFILLED) {
      await dispatch(ProductListThunk());

      // Setting Currency Value by currencyFunc
      const currencyValue = searchData?.equipmentType?.currencyUnit[0]?.symbol;
      setCurrencyDetailFunc(currencyValue);

      const quantityTypeValue = searchData?.equipmentType?.quantityType?.[0]?.quantityType;
      const resObj = { ...searchData };

      // created common func for updating Equipment Details Data
      setEquipmentFormDetailsFunc(parsedEquipmentArr, quantityTypeValue, resObj, true);

      // created common func for updating Equipment Documents Data
      setEquipmentDocumentFormFunc(parsedImagePriceArr, resObj, true);

      // created common func for updating Equipment Warehouse info Data
      setWarehouseInfoFormFunc(resObj, true);
    }
  };

  const equipmentConfigByIdFunc = async () => {
    try {
      await dispatch(equipmentConfig());
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    const equipmentDetailsArr = configData?.equipmentConfig?.properties?.equipment;
    const imageAndPriceArr = configData?.equipmentConfig?.properties?.imageSpecificationAndPrice;

    handleEditLoaderFunc(equipmentDetailsArr, imageAndPriceArr);
  }, [equipmentId]);

  useEffect(() => {
    const equipmentDetailsArr = configData?.equipmentConfig?.properties?.equipment;
    const imageAndPriceArr = configData?.equipmentConfig?.properties?.imageSpecificationAndPrice;

    handleSearchDataFunc(equipmentDetailsArr, imageAndPriceArr);
  }, [configData.equipmentLoading, searchData]);

  useEffect(() => {
    if (equipmentId) {
      equipmentConfigByIdFunc();
    } else {
      const tempForm = configData?.equipmentConfig?.properties?.equipment;
      setFormData(tempForm);
      setFormValues({});
      setUpdatedBody({});
      setEquipDocumentData({ errors: {} });
      setWarehouseData({ errors: {} });
    }
  }, [equipmentId]);

  const handleDeleteEquipment = async () => {
    const res = await dispatch(equipmentDeleteThunk(equipmentId));
    if (res.payload.status === Common.API_STATUS_200) {
      setShouldUpdateState((prev) => !prev);
      handleCloseDrawerFunc();

      await dispatch(
        openSnackbar({
          message: Constants.EQUIPMENT_DELETE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else if (res.payload.status === Common.API_STATUS_400) {
      await dispatch(
        openSnackbar({
          message: Constants.EQUIPMENT_DELETE_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  return (
    <MDBox display="flex" flexDirection="column" gap={1}>
      <MDBox
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        gap={2}
        py={1}
        px={3}
        borderBottom={Colors.PRIMARY}
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: Colors.PRIMARY,
          zIndex: 10,
        }}
      >
        <MDBox display="flex" gap={1} alignItems="center">
          <IconButton
            sx={{
              borderRadius: 0,
              fontSize: pxToRem(18),
              height: "min-content",
              color: "#fff",
            }}
            onClick={handleCloseDrawerFunc}
          >
            {Icons.CROSS}
          </IconButton>
          <MDTypography
            sx={{
              fontSize: pxToRem(16),
              fontWeight: 600,
              color: "#fff",
              padding: "12px 0px",
            }}
          >
            {equipmentId ? PageTitles.UPDATE_EQUIPMENT : PageTitles.REGISTER_EQUIPMENT}
          </MDTypography>
        </MDBox>

        <MDBox>
          {permission?.delete && (
            <BasicButton
              title="Delete"
              icon={Icons.DELETE3}
              background={Colors.WHITE}
              color={Colors.DARK_RED}
              borderColor={Colors.DARK_RED}
              border
              action={() => setDeleteModalOpen(true)}
            />
          )}
          {permission?.create && (
            <CustomButton
              key="equipment-sample-file-export"
              title={ButtonTitles.DOWNLOAD_IMPORT_SAMPLE}
              icon={Icons.DOWNLOAD2}
              background={Colors.WHITE}
              color={Colors.PRIMARY}
              fontWeight="bold"
              openModal={() =>
                handleEquipmentSampleFileExport(
                  process.env.REACT_APP_EQUIPMENT_IMPORT_SAMPLE_FILE_NAME,
                  process.env.REACT_APP_EQUIPMENT_IMPORT_SAMPLE_FILE_NAME.split(".")[0]
                )
              }
            />
          )}
        </MDBox>
      </MDBox>

      {checkloader() ? (
        <MDBox display="flex" flexDirection="column" gap={3} py={2} px={3}>
          <EquipmentDetailsTab
            equipmentId={equipmentId}
            updatedBody={updatedBody}
            setUpdatedBody={setUpdatedBody}
            setSearchData={setSearchData}
            formValues={formValues}
            setFormValues={setFormValues}
            errors={errors}
            currency={currency}
            setCurrency={setCurrency}
            updatedFormValues={updatedFormValues}
            setUpdatedFormValues={setUpdatedFormValues}
            setQuantityType={setQuantityType}
            formData={formData}
            setFormData={setFormData}
          />

          <EquipmentDocumentsTab
            data={equipDocumentData}
            setData={setEquipDocumentData}
            documentForm={documentForm}
            setUpdatedBody={setUpdatedBody}
            updatedBody={updatedBody}
          />

          <WarehouseInfoTab
            equipmentId={equipmentId}
            warehouseData={warehouseData}
            setWarehouseData={setWarehouseData}
            searchData={searchData}
            setUpdatedBody={setUpdatedBody}
            quantityType={quantityType}
          />

          <MDBox display="flex" justifyContent="flex-end" mt={2}>
            <MDButton
              variant="contained"
              disabled={disableSubmit}
              color="info"
              style={{ textTransform: "none", boxShadow: "none" }}
              onClick={handleSubmitEquipmentFunc}
            >
              <span style={{ fontSize }}>
                {loading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
              </span>
            </MDButton>
          </MDBox>

          <DeleteModal
            open={deleteModalOpen}
            title={ModalContent.DELETE_EQUIPMENT_TITLE}
            message={ModalContent.DELETE_EQUIPMENT_MESSAGE}
            handleClose={() => setDeleteModalOpen(false)}
            handleDelete={handleDeleteEquipment}
          />
        </MDBox>
      ) : (
        <MDBox
          px={3}
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          display="flex"
          justifyContent="center"
          alignItems="center"
        >
          {Icons.LOADING2}
        </MDBox>
      )}
    </MDBox>
  );
}

InventoryRegister.defaultProps = {
  equipmentId: null,
  setShouldUpdateState: () => {},
};

InventoryRegister.propTypes = {
  closeDrawerFunc: PropTypes.func.isRequired,
  equipmentId: PropTypes.string,
  setShouldUpdateState: PropTypes.func,
};

export default InventoryRegister;

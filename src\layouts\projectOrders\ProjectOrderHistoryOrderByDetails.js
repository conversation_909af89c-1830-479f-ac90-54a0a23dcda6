import React, { useEffect, useState } from "react";

// 3rd Party libraries
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { Divider } from "@mui/material";
import moment from "moment";

// Components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import CustomImage from "components/Table/GroupImage";
import FullScreenImageComponent from "components/ViewFullImage/ViewImage";

// Examples component
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";
import BasicModal from "examples/modal/BasicModal/BasicModal";
import CheckInCheckOutComments from "examples/modal/CheckInCheckOutComments/CheckInCheckOutComments";

// Layout
import ProjectOrderHistoryDetailsData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectOrderHistoryDetailsData";
import ProjectOrderHistoryEquipmentData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectOrderHistoryEquipmentData";

// Redux
import { openSnackbar } from "redux/Slice/Notification";
import { getProjectOrderHistoryById } from "redux/Thunks/EquipmentRequest";

// Constants
import Constants, { Icons, Colors, defaultData } from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";
import { getFormattedCallingFirstName } from "utils/methods/methods";
import Status from "components/Table/Status";

// Assets
import User from "assets/images/UserDefault.png";

const boxStylesForGridView = {
  flex: "1 1 calc(25% - 16px)", // Ensures 4 items per row
  minWidth: "150px", // Prevents boxes from shrinking too much
  display: "flex",
  flexDirection: "column",
  alignItems: "left",
  justifyContent: "left",
};

const cardBoxLabelStyle = {
  fontWeight: 400,
  fontSize: pxToRem(14),
  color: "#475467",
};

const cardBoxValueStyle = {
  fontWeight: 600,
  fontSize: pxToRem(14),
  color: "#191A51",
  textTransform: "capitalize",
};

// const commonStatusStyle = {
//   padding: "6px 12px",
//   borderRadius: "50px",
//   fontSize: "14px",
//   fontWeight: 600,
//   textTransform: "capitalize",
// };

function ProjectOrderHistoryOrderByDetails() {
  const dispatch = useDispatch();

  const location = useLocation();
  const { projectId = null, orderNumber, projectName } = location.state;

  const { projectOrderHistoryByIdList = [], projectOrderHistoryByIdLoading } = useSelector(
    (state) => state.equipmentRequest
  );

  const [wmCommentsData, setWMCommentsData] = useState({
    open: false,
    type: "",
    comments: [],
  });
  const [rejectRemarkData, setRejectRemarkData] = useState({
    open: false,
    type: "",
    comments: [],
  });
  // States
  const [fullScreenImage, setFullScreenImage] = useState(null);
  const [orderByDetailsObj, setOrderByDetailsObj] = useState({});
  const [shouldUpdateState, setShouldUpdateState] = useState(false);
  const [equipmentTypeList, setEquipmentTypeList] = useState([]);
  const [openEquipmentDetailsModal, setOpenEquipmentDetailsModal] = useState(false);
  // Table Functions
  const handleViewDetailsClick = (equipmentList) => {
    setEquipmentTypeList(equipmentList);
    setOpenEquipmentDetailsModal(true);
  };

  const handleCloseEquipmentModal = () => {
    setOpenEquipmentDetailsModal(false);
    setEquipmentTypeList([]);
  };

  const handleImageFullView = (imageUrl) => {
    setFullScreenImage(imageUrl);
  };

  const handleCloseFullView = () => {
    setFullScreenImage(null);
  };

  const openWarehouseCommentModal = (type, intialComments) => {
    const { open, ...rest } = wmCommentsData;
    setWMCommentsData({
      ...rest,
      open: true,
      type,
      comments: [...(intialComments.pmComments || []), ...(intialComments.wmComments || [])],
    });
  };

  const openRejectCommentModal = (type, remark) => {
    const { open, ...rest } = rejectRemarkData;
    setRejectRemarkData({
      ...rest,
      open: true,
      type,
      comments: [remark],
    });
  };

  // Table Columns
  const { projectHistoryByIdColumns, projectHistoryByIdRows } = ProjectOrderHistoryDetailsData({
    dataList: orderByDetailsObj?.equipmentTypeData || [],
    handleViewDetailsClick,
    openWarehouseCommentModal,
    openRejectCommentModal,
  });

  // Equipment Type Table Column Displayed in Modal
  const { equipmentTypeColumn, equipmentTypeRows } = ProjectOrderHistoryEquipmentData({
    dataList: equipmentTypeList,
  });

  const getProjectOrderHistoryByIdFunc = async (id) => {
    try {
      const payload = {
        id,
      };
      await dispatch(getProjectOrderHistoryById(payload));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    if (projectId) {
      getProjectOrderHistoryByIdFunc(projectId);
    }
  }, [projectId, shouldUpdateState]);

  useEffect(() => {
    const orderDataList = projectOrderHistoryByIdList?.[0]?.orderData || [];
    const orderObj = orderDataList.find((item) => item.orderNumber === orderNumber);

    if (orderObj) {
      setOrderByDetailsObj(orderObj);
    }
  }, [projectOrderHistoryByIdList]);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between">
        <PageTitle title={projectName || ""} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={() => setShouldUpdateState((prev) => !prev)}
          />
        </MDBox>
      </MDBox>

      <Divider sx={{ marginTop: 2 }} />

      <MDBox display="flex" flexDirection="column" gap={2} mt={2}>
        <MDBox
          display="flex"
          flexWrap="wrap"
          bgColor="white"
          gap={2}
          border="1px solid #E0E6F5"
          borderRadius="8px"
          padding="16px 24px"
          minHeight="140px"
        >
          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order No.</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>{orderByDetailsObj?.orderNumber}</MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order By</MDTypography>
            <MDBox display="flex" alignItems="center">
              <MDBox onClick={() => handleImageFullView(User)} sx={{ cursor: "pointer" }}>
                <CustomImage item={User} width={30} height={30} />
              </MDBox>
              <MDTypography sx={cardBoxValueStyle}>
                {getFormattedCallingFirstName(orderByDetailsObj?.orderBy)}
              </MDTypography>
            </MDBox>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Total Items</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {orderByDetailsObj?.totalApprovedItems}
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Total WM Approved Quantity</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {orderByDetailsObj?.totalApprovedQuantity}
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Status</MDTypography>
            <MDTypography sx={cardBoxValueStyle} mt={0.4}>
              <Status
                title={
                  orderByDetailsObj?.orderStatus
                    ? `${orderByDetailsObj?.orderStatus?.replace("-", " ")}`
                    : ""
                }
              />
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order Date</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {moment(orderByDetailsObj.createdAt).format("DD/MM/YYYY")}
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Total Requested Quantity</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {orderByDetailsObj?.totalRequestedQuantity}
            </MDTypography>
          </MDBox>
          <MDBox sx={boxStylesForGridView} visibility="hidden">
            It will keep it align
          </MDBox>
        </MDBox>

        <MDBox>
          <DataTable
            table={{ columns: projectHistoryByIdColumns, rows: projectHistoryByIdRows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={projectOrderHistoryByIdLoading}
            licenseRequired
          />
        </MDBox>

        {/* Equipment Type Modal */}
        <BasicModal
          title="Equipment Details"
          open={openEquipmentDetailsModal}
          handleClose={handleCloseEquipmentModal}
          displayButtons={false}
        >
          <DataTable
            backgroundColor={Colors.LIGHT_GRAY}
            textColor={Colors.BLACK}
            table={{ columns: equipmentTypeColumn, rows: equipmentTypeRows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={projectOrderHistoryByIdLoading}
            licenseRequired
          />
        </BasicModal>

        {/* Order History Comments */}
        {wmCommentsData.open && (
          <CheckInCheckOutComments
            open={wmCommentsData.open}
            type={wmCommentsData.type}
            intialComments={wmCommentsData.comments}
            handleClose={() =>
              setWMCommentsData({
                open: false,
                type: "",
                comments: [],
              })
            }
            minWidth={600}
          />
        )}

        {rejectRemarkData.open && (
          <CheckInCheckOutComments
            open={rejectRemarkData.open}
            type={rejectRemarkData.type}
            intialComments={rejectRemarkData.comments}
            handleClose={() =>
              setRejectRemarkData({
                open: false,
                type: "",
                comments: [],
              })
            }
            minWidth={600}
          />
        )}

        {/* View Order By Profile */}
        <FullScreenImageComponent
          fullScreenImage={fullScreenImage}
          handleCloseFullView={handleCloseFullView}
          src={fullScreenImage}
        />
      </MDBox>
    </DashboardLayout>
  );
}

export default ProjectOrderHistoryOrderByDetails;

// 3rd party library
import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";

// MUI components
import { Grid, Icon, Modal } from "@mui/material";
import ReactDatePicker from "react-datepicker";

// Custom components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import ModalTitle from "examples/NewDesign/ModalTitle";
import FontComponent from "components/Responsive/fonts";
import { ModalBreakPoint } from "components/Responsive/BreakPoints";
import DateTime from "components/DateTime/DateTime";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import ConfirmProjectStatusModal from "examples/modal/ProjectStatusModal/ConfirmProjectStatusModal";

// Redux
import { createDprThunk } from "redux/Thunks/Dpr";
import { openSnackbar } from "redux/Slice/Notification";

// Styles
import style from "assets/style/Modal";

// Constants
import Constants, { Icons, defaultData, Common, ButtonTitles } from "utils/Constants";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Methods
import { getVersion } from "utils/methods/methods";

function NewDpr({ openDprModal, setOpenDprModal, setShouldUpdateState }) {
  const dispatch = useDispatch();
  const configData = useSelector((state) => state.config);

  const [project, setProject] = useState();
  const [values, setValues] = useState({});
  const [errors, setErrors] = useState({});
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    if (e.target.value !== "") {
      setValues({
        ...values,
        [e.target.name]: e.target.value,
      });
    } else {
      const tempValue = { ...values };
      delete tempValue[e.target.name];
      setValues(tempValue);
    }
  };

  const validate = () => {
    const newErrors = {};

    if (!values) {
      return false;
    }

    if (!values.project) {
      newErrors.project = "Please select a project";
    }

    if (!values.dprDate) {
      newErrors.dprDate = "Please select a DPR date";
    }

    setErrors(newErrors);
    return Object.values(newErrors).filter((val) => val !== "").length === 0;
  };

  const handleCreateDprFunc = async () => {
    setIsLoading(true);

    const val = validate();

    if (val) {
      const body = {
        project: values.project?.trim(),
        dprDate: `${moment(values?.dprDate).format(defaultData.DATABSE_DATE_FORMAT)}T00:00:00`,
        version: getVersion(1),
        prevVersion: getVersion(1), // for first time both version and prevVersion will be same
      };

      const res = await dispatch(createDprThunk(body));
      if (res?.payload?.status === Common.API_STATUS_201) {
        setIsLoading(false);
        setOpenDprModal(false);

        dispatch(
          openSnackbar({
            message: res.payload?.data?.message,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        setShouldUpdateState((prev) => !prev);
      } else {
        setIsLoading(false);
        await dispatch(
          openSnackbar({
            message: res.payload?.data?.message
              ? res.payload?.data?.message
              : Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    }
    setIsLoading(false);
  };

  const handleConfirmModalFunc = () => {
    setOpenConfirmationModal(false);
    handleCreateDprFunc();
  };

  useEffect(() => {
    const nestedProjectArr = configData?.screens?.[5]?.screensInfo?.properties || [];
    if (nestedProjectArr.length === 0) return;
    const projectObj =
      nestedProjectArr.find((item) => item.type === "options" && item.id === "project") || {};

    const projectListArr = projectObj.options || [];
    const projectList = projectListArr.map((item) => ({
      [Constants.MONGOOSE_ID]: item.id,
      name: item.title,
    }));

    setProject(projectList);
  }, [configData]);

  return (
    <>
      <Modal
        open={openDprModal}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <MDBox sx={style}>
          <MDBox
            bgColor="info"
            p={3}
            mb={1}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            borderRadius="lg"
            sx={{ borderBottomRightRadius: 0, borderBottomLeftRadius: 0, height: pxToRem(72) }}
          >
            <ModalTitle title="New DPR" color="white" />

            <Icon
              sx={{ cursor: "pointer", color: "beige" }}
              fontSize="medium"
              onClick={() => setOpenDprModal(false)}
            >
              {Icons.CROSS}
            </Icon>
          </MDBox>
          <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
            px={3}
            sx={{
              maxHeight: 500,
              overflowY: "scroll",
              overflowX: "hidden",
              "::-webkit-scrollbar": { display: "none" },
              scrollbarWidth: "none",
            }}
          >
            <Grid container spacing={2}>
              <Grid item lg={6} xs={12}>
                <CustomAutoComplete
                  label="Project"
                  id="project"
                  name="project"
                  hint="Enter Project Name"
                  handleChange={handleChange}
                  menu={project}
                  error={Boolean(errors.project)}
                  helperText={errors.project}
                  getOptionLabel={(option) => option.name || ""}
                  value={{
                    name: project?.find((pfunc) => pfunc[Constants.MONGOOSE_ID] === values.project)
                      ?.name,
                  }}
                />
              </Grid>
              <Grid item lg={6} sm={12}>
                <MDBox sx={{ minWidth: "100%", mt: 0, mr: 1 }} display="flex">
                  <ReactDatePicker
                    selected={values.dprDate || null}
                    onChange={(e) =>
                      handleChange({
                        target: {
                          name: "dprDate",
                          value: e,
                        },
                      })
                    }
                    customInput={
                      <DateTime
                        item={{
                          hint: "Select DPR Date",
                        }}
                        label="DPR Date"
                        errors={errors.dprDate}
                        helperText={errors.dprDate}
                      />
                    }
                    placeholderText="mm/dd/yyyy"
                    dateFormat={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
                    popperProps={{
                      style: { zIndex: 1500 }, // Set a high zIndex to ensure it appears above the modal
                    }}
                  />
                </MDBox>
              </Grid>
            </Grid>
          </MDBox>
          <MDBox px={2} mb={2} mr={1}>
            <Grid container direction="row" justifyContent="flex-end" alignItems="center">
              <Grid item xs={0}>
                <MDButton
                  variant="contained"
                  color="info"
                  onClick={() => setOpenConfirmationModal(true)}
                  disabled={isLoading}
                  style={{ boxShadow: "none", textTransform: "none" }}
                >
                  <span
                    style={{
                      fontSize: FontComponent({ sizes: ModalBreakPoint.extraSmallTitleBreakPoint }),
                    }}
                  >
                    {isLoading ? ButtonTitles.LOADING : ButtonTitles.SUBMIT}
                  </span>
                </MDButton>
              </Grid>
            </Grid>
          </MDBox>
        </MDBox>
      </Modal>

      {/* Project Status Confirmation Modal */}
      {openConfirmationModal && (
        <ConfirmProjectStatusModal
          open={openConfirmationModal}
          handleClose={() => setOpenConfirmationModal(false)}
          handleAction={handleConfirmModalFunc}
          modalWidth="40%"
        />
      )}
    </>
  );
}

NewDpr.propTypes = {
  openDprModal: PropTypes.bool.isRequired,
  setOpenDprModal: PropTypes.func.isRequired,
  setShouldUpdateState: PropTypes.func.isRequired,
};

export default NewDpr;

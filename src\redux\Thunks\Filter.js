import { createAsyncThunk } from "@reduxjs/toolkit";
import Sessions from "utils/Sessions";
import ApiService, { fetchAPIService } from "redux/ApiService/ApiService";
import { filterProjectStatusFunc, normalizeParamsAndAddValues } from "utils/methods/methods";

const filterThunk = createAsyncThunk("filter-safetycards/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`safety-cards?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const exportSafetyCardThunk = createAsyncThunk("export/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await fetchAPIService(`/safety-cards/export?${updatedParams}`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportProjectTrackerExcel = createAsyncThunk(
  "export-project-tracker-excel/api",
  async ({ projectId, browserDateTime }) => {
    const res = await fetchAPIService(
      `/reports/project-tracker/export-excel/${projectId}?projectDate=${browserDateTime}`,
      {
        method: "GET",
      }
    ).then((response) => response.blob());
    return res;
  }
);

export const exportIndividualSafetyCardThunk = createAsyncThunk(
  "export-individual-safety-card/api",
  async (id) => {
    const res = await fetchAPIService(`/safety-cards/${id}/export-pdf`, {
      method: "GET",
    }).then((response) => response.blob());
    return res;
  }
);

export const exportShiftPdf = createAsyncThunk("export-shift/api", async (id) => {
  const res = await fetchAPIService(`/shifts/${id}/export-pdf`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportAllShiftPdf = createAsyncThunk("export-all-shift/api", async (queryParams) => {
  const res = await fetchAPIService(`/shifts/export-excel?${queryParams}`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportToolBoxTalkPdf = createAsyncThunk("export-toolbox-talk/api", async (id) => {
  const res = await fetchAPIService(`/toolbox-talk/${id}/export-pdf`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportOrderPdf = createAsyncThunk("export-order-pdf/api", async (orderId) => {
  const res = await fetchAPIService(`/wm-order/order/${orderId}/export-pdf`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportLocationProgressPdf = createAsyncThunk(
  "export-location-progress-pdf/api",
  async (locationId) => {
    const res = await fetchAPIService(`/locations/progress-pdf/${locationId}`, {
      method: "GET",
    }).then((response) => response.blob());
    return res;
  }
);

export const projectListThunk = createAsyncThunk("project/api", async (params) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { status: projectStatus };

  const updatedParams = normalizeParamsAndAddValues(params, projectStatusObj);

  const res = await ApiService.get(`v2/projects?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const locationListThunk = createAsyncThunk("location/api", async () => {
  const res = await ApiService.get(`/locations`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const severityListThunk = createAsyncThunk("severity/api", async () => {
  const res = await ApiService.get(`severities`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const likelihoodThunk = createAsyncThunk("likelihood/api", async () => {
  const res = await ApiService.get(`likelihoods`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export const exportThunk = createAsyncThunk("export/api", async () => {
  const res = await ApiService.get(`/safety-card/export`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res;
});

export const dalShitFiltersThunk = createAsyncThunk("dal-shift-filter/api", async (param) => {
  const projectStatus = filterProjectStatusFunc();
  const projectStatusObj = { projectStatus };

  const updatedParams = normalizeParamsAndAddValues(param, projectStatusObj);

  const res = await ApiService.get(`shifts?${updatedParams}`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  })
    .then((r) => r)
    .catch((err) => err.response);
  const params = new URLSearchParams(updatedParams);
  const page = params.get("page");
  return page === "0"
    ? { data: res.data, type: "add", status: res.status, page }
    : { data: res.data, type: "append", status: res.status, page };
});

export const exportReportQuestionsPdf = createAsyncThunk(
  "export-report-questions/api",
  async (data) => {
    const res = await fetchAPIService(
      `/reports/get-printable-report-titles/${data?.id}?status=${data?.status}`,
      {
        method: "GET",
      }
    ).then((response) => response.blob());
    return res;
  }
);

export const exportDprPdf = createAsyncThunk("export-dpr-data/api", async ({ dprId, version }) => {
  const res = await fetchAPIService(`/dpr/${dprId}/${version}/export-pdf`, {
    method: "GET",
  }).then((response) => response.blob());
  return res;
});

export const exportAllDprExcel = createAsyncThunk(
  "export-all-dpr-excel/api",
  async (queryParams) => {
    const res = await fetchAPIService(`/dpr/excel-export?${queryParams}`, {
      method: "GET",
    }).then((response) => response.blob());
    return res;
  }
);

export const categoryThunk = createAsyncThunk("category/api", async () => {
  const res = await ApiService.get(`categories?page=0&perPage=2000`, {
    headers: { Authorization: `Bearer ${Sessions.userToken}` },
  });
  return res.data;
});

export default filterThunk;

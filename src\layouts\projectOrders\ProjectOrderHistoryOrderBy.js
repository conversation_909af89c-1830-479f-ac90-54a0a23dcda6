import React, { useEffect, useState } from "react";

// 3rd party libraries
import { Divider } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";

// Components
import MDBox from "components/MDBox";
import SearchBar from "components/Search/SearchInTable";
import ResetFilterButton from "components/Buttons/ResetButton";

// Examples component
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";

// Layout
import ProjectOrderHistoryOrderByData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectOrderHistoryOrderByData";

// Redux
import { getProjectOrderHistoryById } from "redux/Thunks/EquipmentRequest";
import { openSnackbar } from "redux/Slice/Notification";

// Constants
import Constants, { Icons, Colors, defaultData } from "utils/Constants";

// assest
import pxToRem from "assets/theme/functions/pxToRem";

const initialFilters = [
  {
    inputLabel: "Search",
    list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
    selectedValue: "all",
    isLoading: false,
  },
];

function ProjectOrderHistoryOrderBy() {
  const dispatch = useDispatch();

  const location = useLocation();
  const { projectId = null, projectName } = location.state;

  const { projectOrderHistoryByIdList, projectOrderHistoryByIdLoading } = useSelector(
    (state) => state.equipmentRequest
  );
  const orderByList = projectOrderHistoryByIdList?.[0]?.orderData || [];

  const { projectHistoryByIdColumns, projectHistoryByIdRows } = ProjectOrderHistoryOrderByData({
    dataList: orderByList,
  });
  let debounceTimeout;

  // states
  const [filters, setFilters] = useState(initialFilters);
  const [shouldUpdateState, setShouldUpdateState] = useState(false);

  const getProjectOrderHistoryByIdFunc = async (id, searchValue) => {
    try {
      const payload = {
        id,
      };
      if (searchValue) {
        payload.search = searchValue === "all" || searchValue === "All" ? "" : searchValue;
      }
      await dispatch(getProjectOrderHistoryById(payload));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleResetFilters = () => {
    setFilters((prev) => {
      const updatedFilters = [...prev];
      updatedFilters[0].selectedValue = "all";
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  // Search Func
  const handleSearch = async (searchValue) => {
    if (searchValue === "" || !searchValue) return;
    setFilters(searchValue);
    setShouldUpdateState((prev) => !prev);
  };

  const debounce =
    (func, delay) =>
    (...args) => {
      const context = this;
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => func.apply(context, args), delay);
    };

  const debouncedHandleSearch = debounce((e) => {
    const temp = [...filters];
    temp[0].selectedValue = e.target.value;
    handleSearch(temp);
  }, 300);

  useEffect(() => {
    if (projectId) {
      getProjectOrderHistoryByIdFunc(projectId, filters[0].selectedValue);
    }
  }, [projectId, shouldUpdateState]);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between">
        <PageTitle title={projectName || ""} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={() => setShouldUpdateState((prev) => !prev)}
          />
        </MDBox>
      </MDBox>

      <Divider sx={{ marginTop: 2 }} />

      <MDBox display="flex" flexDirection="column" gap={2}>
        <MDBox
          display="flex"
          alignItems="start"
          flexWrap="wrap"
          sx={{ flexDirection: "row", mr: 2 }}
          style={{ width: "100%" }}
        >
          {filters.map((item) => (
            <SearchBar
              freeSolos
              width={pxToRem(200)}
              key={item.inputLabel.replace(" ", "")}
              options={item?.list.map((val) => val?.title) || []}
              filters={filters}
              label={item?.inputLabel}
              value={
                item?.selectedValue
                  ? item.selectedValue.charAt(0).toUpperCase() +
                    item.selectedValue.substring(1).toLowerCase()
                  : ""
              }
              placeholder={item?.inputLabel}
              isLoading={item?.isLoading}
              debouncedHandleSearch={(e) => debouncedHandleSearch(e)}
              handleFilterChange={(e, value) =>
                debouncedHandleSearch({ target: { name: item.inputLabel, value } })
              }
            />
          ))}

          <MDBox mt="5px">
            <ResetFilterButton handleReset={handleResetFilters} style={{ marginLeft: "1rem" }} />
          </MDBox>
        </MDBox>

        <MDBox>
          <DataTable
            table={{ columns: projectHistoryByIdColumns, rows: projectHistoryByIdRows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={projectOrderHistoryByIdLoading}
            licenseRequired
          />
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}

export default ProjectOrderHistoryOrderBy;

import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import moment from "moment";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import Status from "components/Table/Status";
import By from "components/Table/By";

// Material Design
import { IconButton } from "@mui/material";

import { exportDprPdf } from "redux/Thunks/Filter";
import { openSnackbar } from "redux/Slice/Notification";

// Utilities
import Constants, { Icons, defaultData } from "utils/Constants";

import { handlePdfExport, getFormattedProjectName } from "utils/methods/methods";

function DprData({ dprList, handleOpenDprDetailDrawer, handleOpenDprDelete }) {
  const dispatch = useDispatch();
  const [exportDprId, setExportDprId] = useState(null);
  const [rows, setRows] = useState([]);
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens?.[12]?.screensInfo?.agreement;

  const handleExportDprData = async (item) => {
    setExportDprId(item[Constants.MONGOOSE_ID]);
    const res = await dispatch(
      exportDprPdf({ dprId: item[Constants.MONGOOSE_ID], version: item?.version })
    );
    if (res.error) {
      dispatch(
        openSnackbar({
          message: Constants.NO_DPR_VERSION_SUBMITTED,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    await handlePdfExport(`DPR_${item.dprNo}_${item.project?.title}_${item.version}`, res);
    setExportDprId(null);
  };

  useEffect(() => {
    if (dprList) {
      const list = dprList.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          dprNo: <Author name={item.dprNo} />,
          project: <Author name={getFormattedProjectName(item?.project)} />,
          version: (
            <Author
              name={item?.prevVersion || item?.version}
              style={{ textTransform: "initial" }}
            />
          ),
          date: (
            <Author
              name={
                item?.dprDate
                  ? moment(item?.dprDate).utc().format(defaultData.WEB_DATE_FORMAT)
                  : "---"
              }
            />
          ),
          status: <Status title={item?.status && item?.status?.replace("_", " ")} />,
          createdBy: (
            <By
              name={`${
                (item?.createdBy?.callingName
                  ? item?.createdBy?.callingName
                  : item?.createdBy?.firstName) ?? ""
              } ${item?.createdBy?.lastName ?? ""}`}
              isSuperAdmin={item?.createdBy?.role?.[0]?.title === defaultData.SUPER_ADMIN_ROLE}
            />
          ),
          createdAt: (
            <Author
              name={
                item?.createdAt
                  ? moment(item?.createdAt).format(defaultData.WEB_DATE_FORMAT)
                  : "---"
              }
            />
          ),
          action: (
            <MDBox>
              {permission?.read && (
                <IconButton
                  aria-label="View DPR"
                  color="info"
                  onClick={() => handleOpenDprDetailDrawer(item?.[Constants.MONGOOSE_ID])}
                >
                  {Icons.VIEW}
                </IconButton>
              )}
              {permission?.update && (
                <Link
                  to={`/client/dpr/${item?.[Constants.MONGOOSE_ID]}?projectId=${
                    item?.project?.[Constants.MONGOOSE_ID]
                  }`}
                  state={{
                    dprId: item?.[Constants.MONGOOSE_ID],
                    projectId: item?.project?.[Constants.MONGOOSE_ID],
                    projectStatus: item?.status,
                  }}
                >
                  <IconButton aria-label="Edit DPR" color="info">
                    {Icons.EDIT}
                  </IconButton>
                </Link>
              )}
              {permission?.delete && (
                <IconButton
                  aria-label="Delete DPR"
                  color="info"
                  onClick={() => handleOpenDprDelete(item?.[Constants.MONGOOSE_ID])}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
              <IconButton
                aria-label="Export DPR"
                color="info"
                onClick={() => handleExportDprData(item)}
              >
                {exportDprId === item[Constants.MONGOOSE_ID] ? Icons.LOADING : Icons.EXPORT}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [dprList, permission]);

  return {
    columns: [
      { Header: "No.", accessor: "srNo", width: "2%" },
      { Header: "DPR", accessor: "dprNo", width: "2%" },
      { Header: "Project", accessor: "project", align: "left", width: "25%" },
      { Header: "Version", accessor: "version", align: "left", width: "5%" },
      { Header: "Date", accessor: "date", align: "left", width: "10%" },
      { Header: "Status", accessor: "status", align: "left", width: "10%" },
      { Header: "Created By", accessor: "createdBy", align: "left", width: "15%" },
      { Header: "Created At", accessor: "createdAt", align: "left", width: "10%" },
      { Header: "Action", accessor: "action", width: "10%", align: "left" },
    ],

    rows,
  };
}

export default DprData;

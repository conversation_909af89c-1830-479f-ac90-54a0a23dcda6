import React from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box } from "@mui/material";

// Material Dashboard React example components
import ProfileInfoCard from "examples/Cards/InfoCards/ProfileInfoCard";

// Constantd from Utils
import Constants from "utils/Constants";

function OrganizationProfileTab({ tabValue, profile = {} }) {
  const infoData = {
    Organization: profile?.account?.name || Constants.NA,
    Status: profile.isActive ? "Active" : "Not Active",
    Country: profile?.account.organizationCountry || Constants.NA,
    address: profile.account.organizationAddress || Constants.NA,
  };

  return (
    <Box>
      <ProfileInfoCard
        tabChangeValue={tabValue}
        key={profile[Constants.mongooseID]}
        title="Organization Profile"
        info={infoData}
        logo={profile?.account?.logo}
        action={{ route: "", tooltip: "Edit Profile" }}
        shadow={false}
      />
    </Box>
  );
}

OrganizationProfileTab.propTypes = {
  tabValue: PropTypes.number.isRequired,
  profile: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default OrganizationProfileTab;

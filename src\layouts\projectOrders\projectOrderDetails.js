import React, { useEffect, useState } from "react";

// 3rd party libraries
import moment from "moment";
import { Box, Card, Divider, Grid, Tooltip } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

// Custom Components
import MDBox from "components/MDBox";
import Status from "components/Table/Status";
import MDTypography from "components/MDTypography";
import FormTextField from "components/Form/FTextField";
import CustomAutoComeplete from "components/Dropdown/CustomAutoComeplete";

// Examples
import BasicModal from "examples/modal/BasicModal/BasicModal";
import BasicModal2 from "examples/modal/BasicModal/BasicModal2";
import PageTitleComponent from "examples/NewDesign/PageTitle";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import ConfirmationModal from "examples/modal/Confirmation/Confirmation";
import CheckInCheckOutComments from "examples/modal/CheckInCheckOutComments/CheckInCheckOutComments";
import DataTable from "examples/Tables/DataTable";

// Layouts
import AddShoppingCartItems from "layouts/projectOrders/ProjectEquipment/AddShoppingCartItems";

// Utils
import Constants, {
  Icons,
  Colors,
  defaultData,
  ButtonTitles,
  ModalContent,
  Common,
} from "utils/Constants";

// Assets
import bgIm from "assets/images/t2CCNu53AN.gif";
import pxToRem from "assets/theme/functions/pxToRem";

// Redux
import { openSnackbar } from "redux/Slice/Notification";
import {
  getApproverComments,
  getCartPMComments,
  reloadData,
  reloadRequestDetails,
  rejectOrder,
} from "redux/Slice/EquipmentRequest";
import {
  getPMOrderDetails,
  addToQueueEquipment,
  equipmentPermissionThunk,
  getShoppingListById,
  updateApproverCommentThunk,
  getShoppingDetailsById,
  orderEquipment,
  shoppingCartReject,
  newShoppingListCreateThunk,
} from "redux/Thunks/EquipmentRequest";

// Utils
import { paramCreater } from "utils/methods/methods";

// DataTable
import RequestUserData from "./data/requestUserData";
import ProjectOrderDetailsData from "./data/projectOrderDetailsData";

export default function projectOrderDetails() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const location = useLocation();
  const currentDate = new Date();

  const ConfigData = useSelector((state) => state.config);
  const [requestOrderDetails, setRequestOrderDetails] = useState({
    data: [],
    loading: false,
  });

  const [openUserModal, setOpenUserModal] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [openQueueModal, setOpenQueueModal] = useState(false);
  const [openPMOrderRequest, setOpenPMOrderRequest] = useState(false);
  const [requestUserData, setRequestUserData] = useState({
    users: [],
    equipId: "",
  });

  const [userComments, setUserComments] = useState(false);
  const [openrejectConfirmation, setOpenRejectConfirmation] = useState(false);
  const [rejectModalData, setRejectModalData] = useState({
    title: "",
    message: "",
    type: "",
    rejectData: {},
    reason: "",
  });

  const [approverComments, setApproverComments] = useState(false);
  const [approverCommentsData, setApproverCommentsData] = useState({
    type: "",
    comments: [],
    id: "",
    commentId: "",
    equipStatus: "",
  });

  const [userCommentsData, setUserCommentsData] = useState({
    type: "",
    comments: [],
  });

  const [values, setValues] = useState({});
  const [queueData, setQueueData] = useState({});
  const [orderRemark, setOrderRemark] = useState("");
  const [shoppingList, setShoppingList] = useState([]);
  const { currentStatus, requestDetails, shoppingDetailsByIdList } = useSelector(
    (state) => state.equipmentRequest
  );

  // Add Shopping Cart Modal State
  const [openAddShoppingCartModal, setOpenAddShoppingCartModal] = useState(false);
  const [addShoppingCartObjList, setAddShoppingCartObjList] = useState([]);
  const [addShoppingCartLoading, setAddShoppingCartLoading] = useState(false);
  const [fromDetailedViewPage, setFromDetailedViewPage] = useState(false);
  const [headerItemsObj, setHeaderItemsObj] = useState({});
  const [shoppingCartItemsErrors, setShoppingCartItemsErrors] = useState({});

  // Error handler Function
  const somethingWentWrongErrorFunc = () => {
    dispatch(
      openSnackbar({
        message: Constants.SOMETHING_WENT_WRONG,
        notificationType: Constants.NOTIFICATION_ERROR,
      })
    );
  };

  // Shopping Cart Modal Open Function
  const handleOpenShoppingCartModal = () => {
    setFromDetailedViewPage(true);
    setOpenAddShoppingCartModal(true);
  };

  // Shopping Cart Modal Close Function
  const handleCloseAddShoppingCartModal = () => {
    setOpenAddShoppingCartModal(false);
    setFromDetailedViewPage(false);
    setAddShoppingCartObjList([]);
    setShoppingCartItemsErrors({});
  };

  const getRequestDetailsListing = async () => {
    const params = {
      status: currentStatus,
    };

    const queryString = new URLSearchParams(params).toString();

    const data = {
      id: location.state.projectId,
      params: queryString,
    };
    await dispatch(reloadData());
    setRequestOrderDetails({
      ...requestOrderDetails,
      loading: true,
    });
    if (currentStatus === Constants.QUEUE) {
      const res = await dispatch(getShoppingDetailsById(location.state.projectId));
      if (res.payload.status === Common.API_STATUS_200) {
        setRequestOrderDetails({
          data: res.payload.data.data,
        });
      } else {
        navigate("/client/project-orders");
        dispatch(
          openSnackbar({
            message: res.payload.data.message,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    } else {
      const res = await dispatch(getPMOrderDetails(data));
      setRequestOrderDetails({
        ...requestOrderDetails,
        loading: false,
      });
      if (res.payload.status === Common.API_STATUS_200) {
        setRequestOrderDetails({
          data: res.payload.data.data,
        });
      }
    }
  };

  const shoppingCartListing = async () => {
    const dropdownFormat = {
      [Constants.MONGOOSE_ID]: "",
      title: "",
    };
    const res = await dispatch(getShoppingListById(location.state.projectId));
    if (res.payload.status === Common.API_STATUS_200) {
      const tempShoppingList = res.payload.data.data.map((item) => {
        const temp = { ...dropdownFormat };
        temp[Constants.MONGOOSE_ID] = item?.[Constants.MONGOOSE_ID];
        temp.title = item.title;
        return temp;
      });
      setShoppingList(tempShoppingList);
    }
  };

  // Submit Shopping Cart Function
  const handleAddShoppingCartFunc = async () => {
    if (addShoppingCartObjList.length === 0) {
      dispatch(
        openSnackbar({
          message: Constants.SHOPPING_CART_CREATE_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }
    setAddShoppingCartLoading(true);

    const payload = {
      ...headerItemsObj,
      shoppingCart: requestOrderDetails?.data?.shoppingCartId,
      equipmentOrders: addShoppingCartObjList,
    };

    try {
      const res = await dispatch(newShoppingListCreateThunk(payload));

      if (res?.payload?.status === Common.API_STATUS_200) {
        setAddShoppingCartLoading(false);
        setOpenAddShoppingCartModal(false);
        setFromDetailedViewPage(false);
        setHeaderItemsObj({});
        setAddShoppingCartObjList([]);
        setShoppingCartItemsErrors({});

        dispatch(
          openSnackbar({
            message: Constants.SHOPPING_CART_UPDATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );

        // Get Shopping Listing
        await reloadRequestDetails();
        await getRequestDetailsListing();
      } else {
        setAddShoppingCartLoading(false);
        somethingWentWrongErrorFunc();
      }
    } catch (error) {
      setAddShoppingCartLoading(false);
      somethingWentWrongErrorFunc();
    }
    setAddShoppingCartLoading(false);
  };

  useEffect(() => {
    getRequestDetailsListing();
    if (currentStatus === Constants.STATUS_PENDING) {
      shoppingCartListing();
    }
  }, [currentStatus]);

  useEffect(() => {
    if (currentStatus === Constants.QUEUE) {
      setHeaderItemsObj({
        ...headerItemsObj,
        project: shoppingDetailsByIdList[Constants.MONGOOSE_ID],
      });
    }
  }, [shoppingDetailsByIdList]);

  const handleDetailsReload = async () => {
    await reloadRequestDetails();
    await getRequestDetailsListing();
  };

  const handleOpenUserModal = (userData) => {
    setOpenUserModal(true);
    setRequestUserData({ users: userData?.users, equipments: userData });
  };
  const handleCloseUserModal = () => {
    setOpenUserModal(false);
    setRequestUserData([]);
  };

  // PM Open Comment Model
  const openApproverCommentModal = (type, comment, id, commentId, equipStatus) => {
    setApproverComments(true);
    setApproverCommentsData((prevData) => ({
      ...prevData,
      type,
      comments: comment,
      id,
      commentId,
      equipStatus,
    }));
  };

  // Engineer Open Comment Model
  const openEngineerCommentModal = (type, comments) => {
    setUserComments(true);
    setUserCommentsData((prevData) => ({ ...prevData, type, comments }));
  };

  // PM Add Comment Model Close
  const handleApproverCommentsClose = () => {
    setApproverComments(false);
    setApproverCommentsData({
      type: "",
      comments: "",
    });
  };

  // Engineer View Comment Model Close
  const handleUserCommentsClose = () => {
    setUserComments(false);
    setUserCommentsData({
      type: "",
      comments: [],
    });
  };

  // PM Add Comments function
  const handleAddApproverComments = async (body) => {
    if (currentStatus === Constants.STATUS_PENDING) {
      await dispatch(
        getApproverComments({
          comments: body,
          equipmentId: approverCommentsData.id,
        })
      );
      setApproverComments(false);
      setApproverCommentsData((prevData) => ({
        ...prevData,
        comments: body,
      }));
    } else {
      const approverComment = {
        pmComments: body,
      };
      const dataPayload = {
        id: approverCommentsData.commentId,
        body: approverComment,
      };
      setButtonLoading(true);
      const res = await dispatch(updateApproverCommentThunk(dataPayload));
      setButtonLoading(false);
      if (res.payload.status === Common.API_STATUS_200) {
        await dispatch(
          getCartPMComments({
            comments: body,
            equipmentId: approverCommentsData.id,
          })
        );
        setApproverCommentsData((prevData) => ({
          ...prevData,
          comments: body,
        }));
        setApproverComments(false);
        dispatch(
          openSnackbar({
            message: Constants.COMMENT_UPDATED_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      } else {
        dispatch(
          openSnackbar({
            message: Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      }
    }
  };

  // Open Add to Queue Modal
  const openAddToQueueModal = (equipId, typeData, type) => {
    setOpenQueueModal(true);
    setQueueData({
      equipId,
      typeData,
      type,
    });
  };

  const handleCloseQueueModal = () => {
    setOpenQueueModal(false);
    setValues({});
  };

  const handleChange = (event, name) => {
    const selectedOption = shoppingList.find(
      (list) => list[Constants.MONGOOSE_ID] === event.target.value
    );
    setValues({ ...values, [name]: selectedOption });
  };

  // Function to Add to queue equipment wise
  const addToQueueEquipments = async () => {
    if (!Object.keys(values).length || values?.shoppingList === undefined) {
      dispatch(
        openSnackbar({
          message: Constants.SHOPPING_LIST_REQUIRED,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }
    const queueEquipmentBody = {
      project: requestDetails[Constants.MONGOOSE_ID],
      shoppingCart: values?.shoppingList[Constants.MONGOOSE_ID],
      equipmentType: [
        {
          typeId: queueData?.equipId,
          users: queueData?.typeData?.users?.map((us) => ({
            requestId: us?.equipmentRequestId,
            pmApprovedQuantity: us?.approvedQuantity.toString(),
          })),
          pmRequestedQuantity: queueData?.typeData?.totalApprovedQuantity.toString(),
          pmComments: queueData?.typeData?.pmComments,
        },
      ],
    };

    const bulkRequestBody = {
      project: requestDetails[Constants.MONGOOSE_ID],
      shoppingCart: values?.shoppingList[Constants.MONGOOSE_ID],
      equipmentType: requestDetails?.equipmentTypes?.map((equipData) => ({
        typeId: equipData[Constants.MONGOOSE_ID],
        users: equipData?.users?.map((us) => ({
          requestId: us?.equipmentRequestId,
          pmApprovedQuantity: us?.approvedQuantity.toString(),
        })),
        pmRequestedQuantity: equipData?.users
          ?.reduce((total, user) => total + parseInt(user?.approvedQuantity, 10), 0)
          .toString(),
        pmComments: equipData?.pmComments.filter((comment) => comment !== ""),
      })),
    };
    const body =
      queueData.type === Common.PROJECT_ORDER_QUEUE_SINGLE ? queueEquipmentBody : bulkRequestBody;
    setButtonLoading(true);
    const res = await dispatch(addToQueueEquipment(body));
    setButtonLoading(false);
    if (res?.payload?.status === Common.API_STATUS_200) {
      handleCloseQueueModal();
      if (
        queueData.type === Common.PROJECT_ORDER_QUEUE_SINGLE &&
        requestDetails?.equipmentTypes?.length > 1
      ) {
        handleDetailsReload();
      } else {
        navigate("/client/project-orders");
      }
      dispatch(
        openSnackbar({
          message: Constants.EQUIPMENT_QUEUE_SUCCESS_MESSAGE,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  // Reject Order Modal
  const openRejectOrderConfirmationBox = (title, message, type, rejectData) => {
    setOpenRejectConfirmation(true);
    setRejectModalData({
      title,
      message,
      type,
      rejectData,
    });
  };

  // Reject Order Function
  const handleRejectOrder = async () => {
    const generateRemark = () => [
      {
        user: ConfigData.config[0]?.id,
        time: moment(currentDate).format(defaultData.DATABASE_24_HOURS_FORMAT),
        currentStatus,
        comment: rejectModalData.reason,
      },
    ];

    const handleResponse = (res, successMessage) => {
      setOpenRejectConfirmation(false);
      dispatch(
        openSnackbar({
          message:
            res.payload.status === Common.API_STATUS_200
              ? successMessage
              : Constants.SOMETHING_WENT_WRONG,
          notificationType:
            res.payload.status === Common.API_STATUS_200
              ? Constants.NOTIFICATION_SUCCESS
              : Constants.NOTIFICATION_ERROR,
        })
      );
      return res.payload.status === Common.API_STATUS_200;
    };

    let paramData;
    let res;

    if (rejectModalData.type === Common.PROJECT_TEXT) {
      if (currentStatus === Constants.STATUS_PENDING) {
        paramData = {
          id: paramCreater({ project: requestDetails[Constants.MONGOOSE_ID] }),
          status: {
            status: Constants.STATUS_REJECTED,
            existingStatus: currentStatus,
            remark: generateRemark(),
          },
        };
        res = await dispatch(equipmentPermissionThunk(paramData));
      } else if (currentStatus === Constants.QUEUE) {
        paramData = {
          id: shoppingDetailsByIdList?.shoppingCartId,
          data: { remark: generateRemark() },
        };
        res = await dispatch(shoppingCartReject(paramData));
      }
      if (handleResponse(res, Constants.PROJECT_REJECT_SUCCESS_MESSAGE))
        navigate("/client/project-orders");
    } else if (rejectModalData.type === Common.EQUIPMENT_TEXT) {
      if (currentStatus === Constants.STATUS_PENDING) {
        paramData = {
          id: paramCreater({
            project: requestDetails[Constants.MONGOOSE_ID],
            equipmentType: rejectModalData.rejectData[Constants.MONGOOSE_ID],
          }),
          status: {
            status: Constants.STATUS_REJECTED,
            existingStatus: currentStatus,
            remark: generateRemark(),
          },
        };
        res = await dispatch(equipmentPermissionThunk(paramData));
        if (handleResponse(res, Constants.EQUIPMENT_REJECT_SUCCESS_MESSAGE)) {
          await dispatch(
            rejectOrder({
              type: Common.EQUIPMENT_TEXT,
              dataId: rejectModalData.rejectData[Constants.MONGOOSE_ID],
            })
          );
        }
      } else if (currentStatus === Constants.QUEUE) {
        paramData = {
          id: shoppingDetailsByIdList?.shoppingCartId,
          data: {
            equipmentType: rejectModalData.rejectData[Constants.MONGOOSE_ID],
            remark: generateRemark(),
          },
        };
        res = await dispatch(shoppingCartReject(paramData));
        if (handleResponse(res, Constants.EQUIPMENT_REJECT_SUCCESS_MESSAGE)) handleDetailsReload();
      }
    } else if (rejectModalData.type === Common.USER_TEXT) {
      paramData = {
        id: paramCreater({ equipmentRequestId: rejectModalData.rejectData.equipmentRequestId }),
        status: { status: Constants.STATUS_REJECTED, remark: generateRemark() },
      };
      res = await dispatch(equipmentPermissionThunk(paramData));
      if (handleResponse(res, Constants.USER_REJECT_SUCCESS_MESSAGE)) {
        await dispatch(
          rejectOrder({
            type: Common.USER_TEXT,
            dataId: rejectModalData.rejectData.equipmentRequestId,
          })
        );
      }
    }
  };

  const handleClosePMOrderRequest = () => {
    setOpenPMOrderRequest(false);
    setOrderRemark("");
  };

  const handleOpenPMOrderRequest = () => {
    setOpenPMOrderRequest(true);
  };

  // PM Order Function
  const handlePMOrderRequest = async () => {
    const databody = {
      shoppingCart: requestOrderDetails?.data?.shoppingCartId,
      fromDate: moment(requestOrderDetails?.data?.shoppingCartFromDate)
        .format(defaultData.DATABSE_DATE_FORMAT)
        .toString(),
      toDate: moment(requestOrderDetails?.data?.shoppingCartToDate)
        .format(defaultData.DATABSE_DATE_FORMAT)
        .toString(),
      comments: [
        {
          user: ConfigData.config[0]?.id,
          time: moment(currentDate).format(defaultData.DATABASE_24_HOURS_FORMAT),
          status: currentStatus,
          comment: orderRemark,
        },
      ],
      pmOrderManageData: shoppingDetailsByIdList?.equipmentTypes?.map((element) => ({
        id: element?.pmOrderManageDetails[Constants.MONGOOSE_ID],
        pmRequestedQuantity: element?.pmOrderManageDetails?.pmRequestedQuantity,
        comments: element?.pmOrderManageDetails?.comments?.pmComments?.filter(
          (comment) => comment !== ""
        ),
      })),
    };
    const body = {
      id: requestOrderDetails?.data?.pmOrderId,
      body: databody,
    };
    setButtonLoading(true);
    const res = await dispatch(orderEquipment(body));
    if (res.payload.status === Common.API_STATUS_200) {
      setOpenPMOrderRequest(false);
      navigate("/client/project-orders");
      dispatch(
        openSnackbar({
          message: Constants.ORDER_PLACED_SUCCESS_MESSAGE,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else {
      setOpenPMOrderRequest(false);
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setOrderRemark("");
    setButtonLoading(false);
  };
  const billingComponent = (label, value) => (
    <MDBox display="grid" gridTemplateColumns="1fr auto" alignItems="center" width="100%" gap={2}>
      <MDTypography variant="caption" fontWeight="regular">
        {label}
      </MDTypography>
      <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
        {value || 0}
      </MDTypography>
    </MDBox>
  );
  const { requestOrderColumns, requestOrderRows } = ProjectOrderDetailsData(
    currentStatus === Constants.QUEUE
      ? shoppingDetailsByIdList?.equipmentTypes
      : requestDetails?.equipmentTypes,
    currentStatus,
    handleOpenUserModal,
    openApproverCommentModal,
    openAddToQueueModal,
    openRejectOrderConfirmationBox
  );

  const { requestUserColumns, requestUserRows } = RequestUserData(
    requestUserData?.users,
    openEngineerCommentModal,
    requestUserData?.equipments,
    currentStatus === Constants.QUEUE ? shoppingDetailsByIdList : requestDetails,
    currentStatus,
    openRejectOrderConfirmationBox
  );
  return (
    <DashboardLayout>
      <DashboardNavbar />
      {!requestOrderDetails.loading ? (
        <>
          <MDBox display="flex" justifyContent="space-between" alignItems="center">
            <PageTitleComponent
              title={
                currentStatus === Constants.QUEUE
                  ? requestOrderDetails?.data?.shoppingCartTitle
                  : requestOrderDetails?.data?.projectName
              }
            />
            <MDBox>
              {currentStatus !== Constants.STATUS_REJECTED && (
                <>
                  <BasicButton
                    title={
                      currentStatus === Constants.STATUS_PENDING
                        ? ButtonTitles.ADD_TO_SHOPPING_CART
                        : ButtonTitles.ORDER
                    }
                    icon={currentStatus === Constants.STATUS_PENDING && Icons.ADD}
                    background={Colors.WHITE}
                    color={Colors.PRIMARY}
                    border
                    action={
                      currentStatus === Constants.STATUS_PENDING
                        ? openAddToQueueModal
                        : handleOpenPMOrderRequest
                    }
                  />
                  {currentStatus === Constants.QUEUE && (
                    <BasicButton
                      title={ButtonTitles.ADD_ITEM}
                      icon={Icons.ADD}
                      background={Colors.WHITE}
                      color={Colors.PRIMARY}
                      border
                      action={handleOpenShoppingCartModal}
                    />
                  )}
                  <BasicButton
                    title={ButtonTitles.REJECT}
                    background={Colors.WHITE}
                    color={Colors.DARK_RED}
                    borderColor={Colors.DARK_RED}
                    border
                    action={() =>
                      openRejectOrderConfirmationBox(
                        Constants.REJECT_PROJECT_TITLE,
                        Constants.REJECT_PROJECT_MESSAGE,
                        Common.PROJECT_TEXT
                      )
                    }
                  />
                </>
              )}
              <BasicButton
                icon={Icons.RELOAD}
                background={Colors.WHITE}
                border
                color={Colors.BLACK}
                action={handleDetailsReload}
              />
            </MDBox>
          </MDBox>
          <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
          <Card sx={{ borderRadius: 2, boxShadow: 1, padding: 2 }}>
            <Grid container spacing={1}>
              {currentStatus === Constants.QUEUE && (
                <Grid item xs={6} sm={2.5}>
                  <MDTypography
                    sx={{
                      color: "var(--gray-600, #475467)",
                      fontFamily: "Inter",
                      fontSize: pxToRem(14),
                      fontStyle: "normal",
                      fontWeight: 400,
                    }}
                  >
                    Project Name
                  </MDTypography>
                  <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
                    {requestOrderDetails?.data?.projectName?.length >
                    defaultData.SMALLER_CONTENT_LENGTH ? (
                      <Tooltip title={requestOrderDetails?.data?.projectName || "-"} arrow>
                        <span>
                          {`${requestOrderDetails?.data?.projectName?.slice(
                            0,
                            defaultData.SMALLER_CONTENT_LENGTH
                          )}...`}
                        </span>
                      </Tooltip>
                    ) : (
                      requestOrderDetails?.data?.projectName || "-"
                    )}
                  </MDTypography>
                </Grid>
              )}

              <Grid item xs={6} sm={2.5}>
                <MDTypography
                  sx={{
                    color: "var(--gray-600, #475467)",
                    fontFamily: "Inter",
                    fontSize: pxToRem(14),
                    fontStyle: "normal",
                    fontWeight: 400,
                  }}
                >
                  Requested On
                </MDTypography>
                <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
                  {moment(requestOrderDetails?.data?.createdAt).format(defaultData.WEB_DATE_FORMAT)}
                </MDTypography>
              </Grid>

              <Grid item xs={6} sm={2.5}>
                <MDTypography
                  sx={{
                    color: "var(--gray-600, #475467)",
                    fontFamily: "Inter",
                    fontSize: pxToRem(14),
                    fontStyle: "normal",
                    fontWeight: 400,
                  }}
                >
                  {currentStatus === Constants.QUEUE ? "From" : "Status"}
                </MDTypography>
                {currentStatus === Constants.QUEUE ? (
                  <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
                    {moment(requestOrderDetails?.data?.shoppingCartFromDate).format(
                      defaultData.WEB_DATE_FORMAT
                    )}
                  </MDTypography>
                ) : (
                  <Box display="flex" alignItems="center">
                    <Status title={`${currentStatus?.replace("-", " ")}`} />
                  </Box>
                )}
              </Grid>

              <Grid item xs={6} sm={2.5}>
                <MDTypography
                  sx={{
                    color: "var(--gray-600, #475467)",
                    fontFamily: "Inter",
                    fontSize: pxToRem(14),
                    fontStyle: "normal",
                    fontWeight: 400,
                  }}
                >
                  {currentStatus === Constants.QUEUE ? "To" : "Total Requested Items"}
                </MDTypography>
                <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
                  {currentStatus === Constants.QUEUE
                    ? moment(requestOrderDetails?.data?.shoppingCartToDate).format(
                        defaultData.WEB_DATE_FORMAT
                      )
                    : requestOrderDetails?.data?.totalEquipmentType}
                </MDTypography>
              </Grid>

              <Grid item xs={6} sm={2}>
                <MDTypography
                  sx={{
                    color: "var(--gray-600, #475467)",
                    fontFamily: "Inter",
                    fontSize: pxToRem(14),
                    fontStyle: "normal",
                    fontWeight: 400,
                  }}
                >
                  {currentStatus === Constants.QUEUE ? "Duration" : "Total Requested Quantity"}
                </MDTypography>
                <MDTypography fontWeight="medium" fontSize={pxToRem(16)}>
                  {currentStatus === Constants.QUEUE
                    ? `${requestOrderDetails?.data?.totalDays} Days`
                    : requestOrderDetails?.data?.totalQuantity}
                </MDTypography>
              </Grid>
            </Grid>
          </Card>

          <MDBox mt={3} mb={5}>
            <DataTable
              table={{ columns: requestOrderColumns, rows: requestOrderRows }}
              isSorted={false}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
              licenseRequired
            />
            {currentStatus === Constants.QUEUE && (
              <MDBox
                width="100%"
                display="flex"
                justifyContent="flex-end"
                alignItems="flex-end"
                mt={1}
              >
                <MDBox width="30%" minWidth="300px" maxWidth="400px" px={3} pb={3}>
                  {billingComponent(
                    "Total Requested Items",
                    requestOrderDetails?.data?.totalEquipmentType
                  )}
                  {billingComponent(
                    "Total Requested Quantity",
                    requestOrderDetails?.data?.totalQuantity
                  )}
                  {billingComponent(
                    "Total Approved Quantity",
                    shoppingDetailsByIdList?.totalApprovedQuantity
                  )}
                  {billingComponent("Total Amount", shoppingDetailsByIdList?.totalAmount)}
                </MDBox>
              </MDBox>
            )}
          </MDBox>

          <BasicModal
            title={ModalContent.USER_DETAILS}
            open={openUserModal}
            handleClose={handleCloseUserModal}
            actionButton={
              currentStatus === Constants.STATUS_REJECTED ? ButtonTitles.CLOSE : ButtonTitles.SUBMIT
            }
            handleAction={handleCloseUserModal}
            py={3}
            minWidth={900}
          >
            <DataTable
              table={{ columns: requestUserColumns, rows: requestUserRows }}
              isSorted={false}
              backgroundColor={Colors.LIGHT_GRAY}
              textColor={Colors.BLACK}
              entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
              showTotalEntries={false}
              pagination={{ variant: "gradient", color: "info" }}
              loading={Constants.FULFILLED}
              licenseRequired
            />
          </BasicModal>

          {/* Queue Modal */}
          <BasicModal2
            title={ModalContent.MOVE_TO_SHOPPING_CART}
            open={openQueueModal}
            handleClose={handleCloseQueueModal}
            disabled={buttonLoading || shoppingList?.length === 0}
            actionButton={buttonLoading ? ButtonTitles.LOADING : ButtonTitles.MOVE}
            handleAction={addToQueueEquipments}
            py={3}
          >
            {shoppingList?.length === 0 && (
              <MDTypography variant="h6" fontWeight="medium" color="error">
                You need to create a shopping list first before proceeding.
              </MDTypography>
            )}

            <MDBox mb={2}>
              <CustomAutoComeplete
                label="Shopping List*"
                id="shoppingList"
                name="shoppingList"
                hint="Enter Shopping List*"
                handleChange={(e) => handleChange(e, "shoppingList")}
                menu={shoppingList}
                getOptionLabel={(option) => option.title || ""}
                value={values.shoppingList || null}
              />
            </MDBox>
          </BasicModal2>

          {/* New Shopping List Modal */}
          <BasicModal2
            open={openAddShoppingCartModal}
            handleClose={handleCloseAddShoppingCartModal}
            title={ModalContent.ADD_ITEM_TO_SHOPPING_CART}
            actionButton={ButtonTitles.SUBMIT}
            btnLoading={addShoppingCartLoading}
            btnLoadingText={ButtonTitles.SUBMIT_LOADING}
            disabled={addShoppingCartLoading}
            handleAction={handleAddShoppingCartFunc}
            width="90%"
            isAdditionalBtnRequired
            additionalBtnTitle={ButtonTitles.CANCEL}
            additionalBtnAction={handleCloseAddShoppingCartModal}
          >
            <AddShoppingCartItems
              addShoppingCartObjList={addShoppingCartObjList}
              setAddShoppingCartObjList={setAddShoppingCartObjList}
              fromDetailedViewPage={fromDetailedViewPage}
              headerItemsObj={headerItemsObj}
              setHeaderItemsObj={setHeaderItemsObj}
              shoppingCartItemsErrors={shoppingCartItemsErrors}
              setShoppingCartItemsErrors={setShoppingCartItemsErrors}
            />
          </BasicModal2>

          {/* PM Order Request Modal */}
          <BasicModal2
            title={Constants.ORDER_PROJECT_TITLE}
            open={openPMOrderRequest}
            handleClose={handleClosePMOrderRequest}
            handleAction={handlePMOrderRequest}
            disabled={buttonLoading}
            actionButton={buttonLoading === false ? ButtonTitles.ORDER : ButtonTitles.LOADING}
            py={0}
          >
            <FormTextField
              type="textarea"
              marginTop={3}
              marginBottom={3}
              title="Remark"
              value={orderRemark}
              name="remark"
              label="Remark"
              placeholder="Add remark here"
              handleChange={(e) => setOrderRemark(e.target.value)}
            />
          </BasicModal2>

          {/* Check in check out Comments for Approver */}
          {approverComments && (
            <CheckInCheckOutComments
              open={approverComments}
              type="approver"
              intialComments={approverCommentsData.comments}
              equipStatus={approverCommentsData.equipStatus}
              handleClose={handleApproverCommentsClose}
              handleAction={handleAddApproverComments}
              buttonLoading={buttonLoading}
              minWidth={600}
            />
          )}

          {/* Check in check out Comments for User */}
          {userComments && (
            <CheckInCheckOutComments
              open={userComments}
              type={userCommentsData.type}
              intialComments={userCommentsData.comments}
              handleClose={handleUserCommentsClose}
              handleAction={handleUserCommentsClose}
              minWidth={600}
            />
          )}

          {/* Reject order modal */}
          <ConfirmationModal
            title={rejectModalData.title}
            message={rejectModalData.message}
            open={openrejectConfirmation}
            handleClose={() => setOpenRejectConfirmation(false)}
            handleAction={handleRejectOrder}
            positiveButton={ButtonTitles.REJECT}
            width={pxToRem(85)}
          >
            <FormTextField
              type="textarea"
              marginTop={3}
              title="Reason"
              value={rejectModalData.reason}
              name="remark"
              label="Reason"
              placeholder="Add reason here"
              handleChange={(e) =>
                setRejectModalData({ ...rejectModalData, reason: e.target.value })
              }
            />
          </ConfirmationModal>
        </>
      ) : (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Box component="img" src={bgIm} alt="Wind Turbine" />
        </Box>
      )}
    </DashboardLayout>
  );
}

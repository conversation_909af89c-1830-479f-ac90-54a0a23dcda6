import { createSlice } from "@reduxjs/toolkit";

import {
  dprListThunk,
  getContactDetailsDPR,
  getQHSEDprData,
  getPersonnelDprListData,
  getDetailedTimeAnalysisData,
  getdailyActivityLogsData,
  getProjectTrackerDprData,
  getDetailsProgressData,
  getDprApplicationDocumentThunk,
  getDprEquipmentData,
  getDprDetails,
  // updateDprData,
  deleteDpr,
} from "redux/Thunks/Dpr";

// Constants
import Constants from "utils/Constants";
import dprDataFormat from "utils/Types/Dpr";

const initialValuesOfDprData = {
  reportingPeriod: "",
  date: "",
  dprNumber: "",
  project: "",
  client: "",
  version: "",
  prevVersion: "",
  status: "",
  progressData: {
    dprMembers: [],
    progressSummary: [],
    detailProgress: [],
    last24Hours: "",
    next24Hours: "",
    remarks: "",
    lastSyncTime: "",
  },
  qhse: {
    qhseSummary: [],
    remarks: "",
    lastSyncTime: "",
  },
  applicableDocuments: {
    applicableDocumentsSummary: [],
    remarks: "",
    lastSyncTime: "",
  },
  timeAnalysis: {
    detailedTimeAnalysis: [],
    dailyActivityLog: [],
    remarks: "",
    lastSyncTime: "",
  },
  personnelList: {
    personnelList: [],
    remarks: "",
    lastSyncTime: "",
  },
  equipmentList: {
    equipmentList: [],
    remarks: "",
    lastSyncTime: "",
  },
  commentsAndSignatures: {
    commentOrRemarks: {
      endClientRepresentative: "",
      clientRepresentative: "",
      reynardRepresentative: "",
    },
    namesAndSignature: {
      endClientRepresentative: "",
      clientRepresentative: "",
      reynardRepresentative: "",
    },
  },
  tabTextColor: {},
};

const initailValueOfTabsForDpr = {
  progressTab: 0,
  qhseTab: 0,
  applicableDocsTab: 0,
  timeAnalysisTab: 0,
  personnelListTab: 0,
  equipmentTab: 0,
  commentAndSignaturesTab: 0,
};

const initialState = {
  dpr: false,
  allDprVersionData: [],

  displayedDprTabsObj: initailValueOfTabsForDpr,
  isDprDetailReqCompleted: false,
  isLatestDataApiCompleted: false,

  dprData: initialValuesOfDprData,
  dprList: [],
  dprListpagination: {
    totalRecords: 0,
    completed: [],
  },
  loading: false,
  updatedPersonnelList: [],
};

export const DprSlices = createSlice({
  name: "DPR",
  initialState,
  reducers: {
    openDpr(state) {
      state.dpr = true;
    },
    closeDpr(state) {
      state.dpr = false;
    },
    setProgressData(state, action) {
      state.dprData.progressData = action.payload;
    },
    setQhseRemarks(state, action) {
      state.dprData.qhse.remarks = action.payload;
    },
    setApplicableDocumentsRemarks(state, action) {
      state.dprData.applicableDocuments.remarks = action.payload;
    },
    setTimeAnalysisRemarks(state, action) {
      state.dprData.timeAnalysis.remarks = action.payload;
    },
    setEquipmentRemarks(state, action) {
      state.dprData.equipmentList.remarks = action.payload;
    },
    setPersonnelListRemarks(state, action) {
      state.dprData.personnelList.remarks = action.payload;
    },
    setCommentOrRemarks(state, action) {
      state.dprData.commentsAndSignatures.commentOrRemarks = action.payload;
    },
    setNamesAndSignature(state, action) {
      state.dprData.commentsAndSignatures.namesAndSignature = action.payload;
    },

    setEditDprStatus(state, action) {
      state.dprData.status = action.payload;
    },

    // Whenever add a new variable please check if it need to be reset at the time of resetDPR
    resetDPR(state) {
      state.dprData = initialValuesOfDprData;
      state.displayedDprTabsObj = initailValueOfTabsForDpr;
      state.isDprDetailReqCompleted = false;
      state.isLatestDataApiCompleted = false;
      state.allDprVersionData = [];
    },
    setDprPersonnelList(state, action) {
      state.dprData.personnelList.personnelList = action.payload;
    },

    updateDisplayedDprTabsObjAtReload(state, action) {
      const { tabKeyName, value } = action.payload;
      state.displayedDprTabsObj = {
        ...initailValueOfTabsForDpr,
        [tabKeyName]: state.displayedDprTabsObj[tabKeyName] + value,
      };
    },

    resetDisplayedDprTabsObj(state) {
      state.displayedDprTabsObj = initailValueOfTabsForDpr;
    },

    updateIsLatestDataApiCompleted(state, action) {
      state.isLatestDataApiCompleted = action.payload;
    },

    updateSyncUpTimeForTabs(state, action) {
      const { dprKeyName, syncTime } = action.payload;
      state.dprData[dprKeyName].lastSyncTime = syncTime;
    },

    updatePersonnelList(state, { payload }) {
      const index = state.updatedPersonnelList.findIndex(
        (item) => item?.indexNum === payload?.indexNum
      );

      if (index !== -1) {
        // Update the existing object if founded.
        state.updatedPersonnelList[index] = payload;
      } else {
        // Add the new object if not founded.
        state.updatedPersonnelList.push(payload);
      }
    },
    updateTabTextColor(state, { payload }) {
      state.dprData.tabTextColor = payload;
    },
  },

  extraReducers: {
    [dprListThunk.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [dprListThunk.fulfilled]: (state, { payload, meta }) => {
      const { dprs = [], totalRecords } = payload?.data || {};

      state.loading = Constants.FULFILLED;

      // Get pagination info from the meta argument
      const params = new URLSearchParams(meta.arg);
      const page = parseInt(params.get("page"), 10);
      const perPage = parseInt(params.get("perPage"), 10);

      // If dprs array is empty, initialize it with the required format
      const dprsArrList =
        page === 0
          ? Array.from({ length: totalRecords }, () => ({ ...dprDataFormat }))
          : [...state.dprList];

      const startIndex = page * perPage;
      dprsArrList.splice(startIndex, perPage, ...dprs);

      // Update state with new data
      state.dprList = dprsArrList;
      state.dprListpagination = {
        totalRecords,
        completed: page === 0 ? [page] : [...state.dprListpagination.completed, page],
      };
    },

    [dprListThunk.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },

    [getDprDetails.pending]: (state) => {
      state.loading = Constants.PENDING;
      state.isDprDetailReqCompleted = false;
    },
    [getDprDetails.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;

      // First putting the current version data to dpr data
      state.dprData = payload?.data?.currentVersionData || initialValuesOfDprData;

      // then updating the dpr data with response data
      state.dprData = { ...state.dprData, version: payload?.data?.version };
      state.dprData.dprNumber = payload?.data?.dprNo;
      state.dprData.date = payload?.data?.dprDate;
      state.dprData.project = payload?.data?.project; // Saving Id here
      state.dprData.client = payload?.data?.client;
      state.dprData.status = payload?.data?.status;
      state.dprData.prevVersion = payload?.data?.prevVersion || payload?.data?.version;

      state.allDprVersionData = payload?.data?.allDprVersionData;
      state.isDprDetailReqCompleted = true;

      if (!payload?.data?.currentVersionData && payload?.data?.allDprVersionData?.length > 0) {
        state.dprData = payload?.data?.allDprVersionData?.[0];
        state.dprData = { ...state.dprData, version: payload?.data?.version };
        state.dprData.dprNumber = payload?.data?.dprNo;
        state.dprData.date = payload?.data?.dprDate;
        // state.dprData.project = payload?.data?.project; // Saving Id here
        state.dprData.status = payload?.data?.status;
        state.dprData.prevVersion = payload?.data?.prevVersion || payload?.data?.version;
      }
    },
    [getDprDetails.rejected]: (state) => {
      state.isDprDetailReqCompleted = true;
      state.loading = Constants.REJECTED;
    },

    // Tab-1 Progress Tab Request Starts here
    [getContactDetailsDPR.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getContactDetailsDPR.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      const dprMemberData = payload?.data;

      state.dprData.reportingPeriod = dprMemberData.reportingPeriod;
      state.dprData.date = dprMemberData.date;
      state.dprData.client = dprMemberData.client;
      state.dprData.project = dprMemberData.project; // It saving Name here
      state.dprData.dprNumber = dprMemberData.dprNumber;
      state.dprData.version = dprMemberData.version;
      state.dprData.status = dprMemberData.status;
      state.dprData.progressData.dprMembers = dprMemberData.dprMembers;
    },
    [getContactDetailsDPR.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    [getProjectTrackerDprData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getProjectTrackerDprData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.progressData.progressSummary = payload?.data;
    },
    [getProjectTrackerDprData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },

    [getDetailsProgressData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getDetailsProgressData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.progressData.detailProgress = payload?.data;
    },
    [getDetailsProgressData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-1 Progress tab API Request Ends here

    // Tab-2 QHSE Tab API Request Starts here
    [getQHSEDprData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getQHSEDprData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.qhse.qhseSummary = payload?.data;
    },
    [getQHSEDprData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-2 QHSE Tab API Request Ends here

    // Tab-3 Applicable Documents Tab API Request Starts here
    [getDprApplicationDocumentThunk.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getDprApplicationDocumentThunk.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.applicableDocuments.applicableDocumentsSummary = payload?.data?.data;
    },
    [getDprApplicationDocumentThunk.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-3 Applicable Documents Tab API Request Ends here

    // Tab-4 Time Analysis Tab API Request Starts here
    [getDetailedTimeAnalysisData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getDetailedTimeAnalysisData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.timeAnalysis.detailedTimeAnalysis = payload?.data;
    },
    [getDetailedTimeAnalysisData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },

    [getdailyActivityLogsData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getdailyActivityLogsData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.timeAnalysis.dailyActivityLog = payload?.data;
    },
    [getdailyActivityLogsData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-4 Time Analysis Tab API Request Starts here

    // Tab-5 ==> Personnel Tab API Request Starts here
    [getPersonnelDprListData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getPersonnelDprListData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.personnelList.personnelList = payload?.data;
    },
    [getPersonnelDprListData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-5 Personnel Tab API Request Ends here

    // Tab-6 Equipment Tab API Request Starts here
    [getDprEquipmentData.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getDprEquipmentData.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprData.equipmentList.equipmentList = payload?.data;
    },
    [getDprEquipmentData.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    // Tab-6 Equipment Tab API Request Ends here
    // Delete DPR
    [deleteDpr.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [deleteDpr.fulfilled]: (state, { payload }) => {
      state.loading = Constants.FULFILLED;
      state.dprList = state?.dprList?.filter(
        (item) => item[Constants.MONGOOSE_ID] !== payload?.data?.data[Constants.MONGOOSE_ID]
      );
    },
    [deleteDpr.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
  },
});

export const {
  openDpr,
  closeDpr,
  setProgressData,
  setQhseRemarks,
  setApplicableDocumentsRemarks,
  setTimeAnalysisRemarks,
  setEquipmentRemarks,
  setPersonnelListRemarks,
  setCommentOrRemarks,
  setNamesAndSignature,
  setEditDprStatus,
  resetDPR,
  setDprPersonnelList,
  updateDisplayedDprTabsObjAtReload,
  resetDisplayedDprTabsObj,
  updateIsLatestDataApiCompleted,
  updateSyncUpTimeForTabs,
  updatePersonnelList,
  updateTabTextColor,
} = DprSlices.actions;

export default DprSlices.reducer;

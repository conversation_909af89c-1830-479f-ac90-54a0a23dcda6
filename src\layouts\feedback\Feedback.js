import React, { useEffect, useState } from "react";

// MUI Components
import { Button, FormControl } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import FeedbackData from "layouts/feedback/data/FeedbackData";
import DataTable from "examples/Tables/DataTable";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";

// Constant
import Constants, {
  ButtonTitles,
  Icons,
  ModalContent,
  defaultData,
  Colors,
  PageTitles,
} from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";

// Redux component
import { openSnackbar } from "redux/Slice/Notification";
import { useDispatch, useSelector } from "react-redux";
import getAllfeedbacks, { deleterFeedback } from "redux/Thunks/feedback";
import UserListThunk from "redux/Thunks/UserManagement";
import { reloadData } from "redux/Slice/Feedback";

function Feedbacks() {
  const [filters, setFilters] = useState([
    {
      inputLabel: "Types",
      list: [
        "All",
        "Complaint",
        "Compliment",
        "Feature Request",
        "Harassment",
        "Idea",
        "Problem",
        "Question",
      ],
      selectedValue: "All",
    },
    {
      inputLabel: "Subjects",
      list: ["All", "Employment", "Hardware", "Software"],
      selectedValue: "All",
    },
    {
      inputLabel: "Submitted By",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
    },
  ]);
  const feedbackList = useSelector((state) => state.feedback);

  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.PER_PAGE,
  });

  const [feedbackData, setFeedbackData] = useState({
    openDeleteModal: false,
    deleteId: "",
    deleteloading: false,
    listLoading: Constants.PENDING,
  });
  const dispatch = useDispatch();

  const handleCloseDeleteModal = () => setFeedbackData({ ...feedbackData, openDeleteModal: false });
  const handleOpenDeleteModal = (feedbackId) =>
    setFeedbackData({ ...feedbackData, openDeleteModal: true, deleteId: feedbackId });

  const { columns, rows } = FeedbackData(feedbackList.list, handleOpenDeleteModal);

  const handleFilter = async (filterVale = filters) => {
    setTablePagination({ ...tablePagination, page: 0 });
    const paramData = {
      page: 0,
      perPage: tablePagination.perPage,
      type: filterVale[0].selectedValue.toLowerCase().replace(/ /g, "_"),
      subject: filterVale[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      createdBy: filterVale[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    };
    Object.keys(paramData).forEach((key) => {
      if (paramData[key] === "") {
        delete paramData[key];
      }
    });

    const data = new URLSearchParams(paramData);
    await dispatch(getAllfeedbacks(data));
  };

  useEffect(() => {
    (async () => {
      handleFilter();
      const temp = [...filters];
      const users = await dispatch(UserListThunk());
      const userList = users.payload.data.data.usersData.reduce((acc, user) => {
        if (
          user.role?.accessType !== defaultData.WEB_ACCESSTYPE &&
          user.role?.title !== defaultData.SUPER_ADMIN_ROLE &&
          user.role?.title !== defaultData.ADMIN_ROLE
        ) {
          acc.push({
            [Constants.MONGOOSE_ID]: user[Constants.MONGOOSE_ID],
            title: `${user?.callingName ? user.callingName : user.firstName} ${user.lastName}`,
          });
        }
        return acc;
      }, []);
      filters[2].list = [...filters[2].list, ...userList];
      setFilters(temp);
    })();
  }, []);

  const handleFilterChange = (e) => {
    const temp = [...filters];
    const { value } = e.target;
    const index = filters.findIndex((filter) => filter.inputLabel === e.target.name);
    temp[index].selectedValue = value;
    setFilters(temp);
    handleFilter(temp);
  };

  const handleResetFilter = () => {
    const temp = filters.map((filter) => ({
      ...filter,
      selectedValue: filter.list[0][Constants.MONGOOSE_ID] || filter.list[0],
    }));
    setFilters(temp);
    handleFilter(temp);
  };

  const handleDeleteFeedback = async () => {
    setFeedbackData({ ...feedbackData, deleteloading: true });
    const res = await dispatch(deleterFeedback(feedbackData.deleteId));
    if (res.status === 200) {
      dispatch(
        openSnackbar({
          message: Constants.FEEDBACK_DELETE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      setFeedbackData({ ...feedbackData, deleteloading: false, openDeleteModal: false });
      handleFilter();
    } else {
      dispatch(
        openSnackbar({
          message: Constants.FEEDBACK_DELETE_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      setFeedbackData({ ...feedbackData, deleteloading: false, openDeleteModal: false });
    }
  };

  const handleTablePagination = async (value) => {
    if (feedbackList.feedbackListingPagiantion.completed.includes(value)) return;
    const paramData = {
      page: value,
      perPage: tablePagination.perPage,
      type: filters[0].selectedValue.toLowerCase().replace(/ /g, "_"),
      subject: filters[1].selectedValue.toLowerCase().replace(/ /g, "_"),
      createdBy: filters[2].selectedValue.toLowerCase().replace(/ /g, "_"),
    };
    Object.keys(paramData).forEach((key) => {
      if (paramData[key] === "") {
        delete paramData[key];
      }
    });

    const data = new URLSearchParams(paramData);
    await dispatch(reloadData());
    await dispatch(getAllfeedbacks(data));
  };

  const handleReload = async () => {
    await dispatch(reloadData());
    handleFilter(filters, tablePagination.page);
  };
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between">
        <PageTitle title={PageTitles.FEEDBACK} />
        <BasicButton
          icon={Icons.RELOAD}
          background={Colors.WHITE}
          border
          color={Colors.BLACK}
          action={handleReload}
        />
      </MDBox>
      <MDBox display="flex" justifyContent="space-between" mt={2} mx={0}>
        <MDBox display="flex wrap" flexWrap="wrap" justifyContent="start" mt={2} mx={0}>
          {filters?.map((val) => {
            const isSubmittedBy = val?.inputLabel === "Submitted By";
            return isSubmittedBy ? (
              <FormControl
                key={val?.inputLabel}
                variant="standard"
                size="medium"
                style={{ marginTop: "22px", width: 180, marginRight: "15px" }}
              >
                <CustomAutoComplete
                  label={val?.inputLabel}
                  name={val?.inputLabel}
                  id={val?.inputLabel}
                  getOptionLabel={(option) => option.title || ""}
                  menu={val?.list}
                  value={{
                    title:
                      val?.list.find((item) => item[Constants.MONGOOSE_ID] === val?.selectedValue)
                        ?.title || "",
                  }}
                  handleChange={handleFilterChange}
                  valueStyle={{
                    backgroundColor: Colors.WHITE,
                    height: pxToRem(40),
                    verticalMarginTop: pxToRem(4),
                    menuWidth: 400,
                    inputWidth: 250,
                    padding: pxToRem(1),
                  }}
                  labelStyle={{
                    fontSize: pxToRem(14),
                    fontWeight: 600,
                    color: Colors.BLACK,
                  }}
                />
              </FormControl>
            ) : (
              <FilterDropdown
                label={val.inputLabel}
                name={val.inputLabel}
                defaultValue={val?.selectedValue}
                value={val?.selectedValue}
                handleChange={handleFilterChange}
                menu={val.list}
                key={val.inputLabel}
              />
            );
          })}
          <Button
            sx={{
              mt: pxToRem(45),
              mr: 1,
              backgroundColor: "#fff",
              "&:hover": {
                backgroundColor: "#fff",
              },
              fontSize: pxToRem(14),
              textTransform: "capitalize",
            }}
            variant="outlined"
            color="info"
            startIcon={Icons.RESET_FILTER}
            onClick={handleResetFilter}
          >
            {ButtonTitles.RESET_FILTER}
          </Button>
        </MDBox>
      </MDBox>
      <MDBox mt={2}>
        <DataTable
          table={{ columns, rows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
          showTotalEntries={false}
          noEndBorder
          loading={feedbackList?.loading}
          currentPage={tablePagination.page}
          handleTablePagination={handleTablePagination}
          handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
          isGotoVisisble
        />
      </MDBox>

      {/* Delete Modal */}
      <DeleteModal
        open={feedbackData.openDeleteModal}
        title={ModalContent.FEEDBACK_DELETE_TITLE}
        message={ModalContent.FEEDBACK_DELETE_MESSAGE}
        handleClose={handleCloseDeleteModal}
        handleDelete={handleDeleteFeedback}
      />
    </DashboardLayout>
  );
}

export default Feedbacks;

import { useEffect, useState } from "react";

// Custom Components
import MDBox from "components/MDBox";
import Author from "components/Table/Author";
import User from "assets/images/UserDefault.png";
import pxToRem from "assets/theme/functions/pxToRem";

// MUI Components
import { Badge, IconButton, TextField } from "@mui/material";

// Redux
import { useDispatch } from "react-redux";
import { updateEquipmentOrderQuantity } from "redux/Slice/EquipmentRequest";

// Utlis
import Constants, { Icons, Common } from "utils/Constants";

// Methods
import { numericFieldValidation } from "utils/methods/methods";

export default function RequestUserData(
  requestUserData,
  openEngineerCommentModal,
  equipment,
  detailsList,
  currentStatus,
  openRejectOrderConfirmationBox
) {
  const [requestUserRows, setRequestUserRows] = useState([]);

  const requestUserData1 = detailsList?.equipmentTypes?.find(
    (eq) => eq?.[Constants.MONGOOSE_ID] === equipment?.[Constants.MONGOOSE_ID]
  )?.users;

  const dispatch = useDispatch();
  useEffect(() => {
    if (requestUserData1) {
      const list = requestUserData1?.map((element, index) => {
        const eType = detailsList?.equipmentTypes?.find(
          (eq) => eq[Constants.MONGOOSE_ID] === equipment[Constants.MONGOOSE_ID]
        );

        const userQty = eType?.users?.find(
          (user1) => user1?.equipmentRequestId === element.equipmentRequestId
        );

        const handleQuantityChange = (type) => {
          dispatch(
            updateEquipmentOrderQuantity({
              userId: element.equipmentRequestId,
              equipId: equipment[Constants.MONGOOSE_ID],
              type,
              currentStatus,
            })
          );
        };

        const handleInputChange = (e) => {
          const newValue = e.target.value.replace(/\D/g, "");
          dispatch(
            updateEquipmentOrderQuantity({
              userId: element.equipmentRequestId,
              equipId: equipment[Constants.MONGOOSE_ID],
              type: "input",
              newValue,
              currentStatus,
            })
          );
        };

        return {
          srNo: <Author name={index + 1} />,
          tempName: <Author name={element?.temporaryProductName} />,
          requestedBy: (
            <MDBox display="flex" justifyContent="start" alignItems="center">
              <img
                src={element?.profileImage || User}
                alt="User Profile"
                style={{
                  width: pxToRem(50),
                  height: pxToRem(50),
                  marginRight: pxToRem(10),
                  borderRadius: "8px",
                }}
              />
              <Author name={`${element?.callingName || element?.firstName} ${element?.lastName}`} />
            </MDBox>
          ),
          reqQty: <Author name={element?.engineerRequestedQuantity} />,
          appQty: (
            <MDBox display="flex" justifyContent="center" alignItems="center">
              <IconButton
                color="info"
                onClick={() => handleQuantityChange("decrement")}
                disabled={
                  element?.approvedQuantity <= 0 || currentStatus === Constants.STATUS_REJECTED
                }
              >
                {Icons.DASH2}
              </IconButton>
              <TextField
                type="number"
                disabled={currentStatus === Constants.STATUS_REJECTED}
                name="totalRequestedQuantity"
                id="totalRequestedQuantity"
                value={
                  currentStatus === Constants.STATUS_PENDING
                    ? userQty?.approvedQuantity
                    : userQty?.pmApprovedQuantity
                }
                variant="standard"
                onKeyDown={(e) => numericFieldValidation(e)}
                onChange={handleInputChange}
                sx={{
                  width: "50px",
                  "& .MuiInputBase-input": {
                    textAlign: "center",
                  },
                }}
              />
              <IconButton
                color="info"
                disabled={currentStatus === Constants.STATUS_REJECTED}
                onClick={() => handleQuantityChange("increment")}
              >
                {Icons.NEW}
              </IconButton>
            </MDBox>
          ),
          action: (
            <MDBox display="flex" justifyContent="center" alignItems="center">
              <IconButton
                aria-label="fingerprint"
                color="info"
                disabled={!element?.engineerComment?.length}
                onClick={() => openEngineerCommentModal(Common.USER_TEXT, element?.engineerComment)}
              >
                <Badge
                  badgeContent={element?.engineerComment?.length}
                  color="error"
                  max={9}
                  sx={{ "& .MuiBadge-badge": { fontSize: 10, height: 15, minWidth: 15 } }}
                >
                  {element?.engineerComment?.length === 0 ? Icons.COMMENT_DISABLE : Icons.COMMENT}
                </Badge>
              </IconButton>
              {currentStatus !== Constants.STATUS_REJECTED && (
                <IconButton
                  aria-label="fingerprint"
                  color="info"
                  onClick={() =>
                    openRejectOrderConfirmationBox(
                      Constants.REJECT_USER_TITLE,
                      Constants.REJECT_USER_MESSAGE,
                      Common.USER_TEXT,
                      element
                    )
                  }
                >
                  {Icons.REJECTED}
                </IconButton>
              )}
            </MDBox>
          ),
        };
      });

      setRequestUserRows([...list]);
    }
  }, [requestUserData1, detailsList]);

  return {
    requestUserColumns: [
      { Header: "No.", accessor: "srNo", width: "3%" },
      { Header: "Requested By", accessor: "requestedBy", align: "left" },
      ...(equipment?.isTemporary
        ? [{ Header: "Temp Equip. Name", accessor: "tempName", align: "left" }]
        : []),
      { Header: "Requested Quantity", accessor: "reqQty", align: "left", width: "5%" },
      { Header: "Approved Quantity", accessor: "appQty", align: "left" },
      { Header: "Action", accessor: "action", width: "13%", align: "left" },
    ],
    requestUserRows,
  };
}

import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// @mui material components
import { IconButton } from "@mui/material";

// 3rd party libraries
import moment from "moment-timezone";
import { useDispatch, useSelector } from "react-redux";

// matrial Dashboard React Components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import ImageUpload from "components/ImageUpload/imageUpload";
import CustomCheckbox from "components/CustomCheckbox/CustomCheckbox";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import FTextField from "components/Form/FTextField";

// Material Dashboard React example components
import BasicModal from "examples/modal/BasicModal/BasicModal";

// Redux Store
import { openSnackbar } from "redux/Slice/Notification";

// Utils Constants
import Constants, { ButtonTitles, Icons, defaultData, Common } from "utils/Constants";

// Assets
import pxToRem from "assets/theme/functions/pxToRem";

// Function to filter empty values
const filterEmptyValues = (obj = {}) =>
  Object.fromEntries(
    Object.entries(obj).filter(([key, certiValue]) => {
      if (certiValue === null || certiValue === "") {
        if (
          obj.isValidityDate === false &&
          (key === Common.START_DATE || key === Common.END_DATE)
        ) {
          return false;
        }
        return true;
      }
      return false;
    })
  );

function UserCertificateUploadModal({
  openModal,
  closeModal,
  handleAllCertificateUpload,
  uploadCertificateInitialValues,
  fromUserDashboard,
}) {
  const dispatch = useDispatch();

  // Getting Certificate Type Optios List from Redux
  const certificateTypeList = useSelector((state) => state.dropdownOptions.userCertificateTypeList);
  const usersDropdownList = useSelector((state) => state.dropdownOptions.usersDropdownList);

  // States
  const [loading, setLoading] = useState(false);
  const [certificateTypeOptions, setCertificateTypeOptions] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [userCertificateCount, setUserCertificateCount] = useState([]);
  const [currentCertificateIndex, setCurrentCertificateIndex] = useState(0);
  const [certificateValidation, setCertificateValidation] = useState({});
  const [renderImageField, setRenderImageField] = useState(false);
  const [uploadedCertificatesArr, setUploadedCertificatesArr] = useState([
    uploadCertificateInitialValues,
  ]);

  const getCertificateTypeValueFunc = (certificateTypeId, userId) => {
    if (fromUserDashboard) {
      const optionsArrObj = certificateTypeOptions?.find(
        (item) => item[Constants.MONGOOSE_ID] === userId
      );
      const certificateObj = optionsArrObj?.certificateOptions?.find(
        (item) => item[Constants.MONGOOSE_ID] === certificateTypeId
      );
      return certificateObj?.title;
    }
    const certificateObj = certificateTypeOptions.find(
      (item) => item[Constants.MONGOOSE_ID] === certificateTypeId
    );
    return certificateObj?.title;
  };

  // Function to update the uploaded certificate array using the index
  const updateSetUploadCertificateArr = (index, updateObj) => {
    setUploadedCertificatesArr((prev) =>
      prev.map((item, i) => (i === index ? { ...item, ...updateObj } : item))
    );
  };

  // Function to handle certificate dropdown value change
  const handleChangeUploadCertificateType = (e, index, userId) => {
    const { value: certificateTypeId } = e.target;

    let certificateTypeObj = {};

    if (fromUserDashboard && userId) {
      const certificateOptionsObj = certificateTypeOptions.find(
        (item) => item?.[Constants.MONGOOSE_ID] === userId
      );
      certificateTypeObj = certificateOptionsObj?.certificateOptions?.find(
        (item) => item?.[Constants.MONGOOSE_ID] === certificateTypeId
      );
    } else {
      certificateTypeObj = certificateTypeOptions.find(
        (item) => item?.[Constants.MONGOOSE_ID] === certificateTypeId
      );
    }

    if (certificateTypeId) {
      setCertificateValidation((prev) => {
        const { certificateType, ...rest } = prev;
        return rest;
      });
    }

    if (certificateTypeObj?.validityDate === false || !certificateTypeId) {
      if (uploadedCertificatesArr[index]?.isValidityDate === true) {
        updateSetUploadCertificateArr(index, {
          isValidityDate: false,
          startDate: null,
          endDate: null,
        });
      }
    }

    // Updating the Certificate Type using the index value
    updateSetUploadCertificateArr(index, {
      certificateType: certificateTypeId,
      name: certificateTypeObj?.title,
      isValidityDate: certificateTypeObj?.validityDate,
    });
  };

  const handleUserChangeFunc = (e, index) => {
    const { value: selectedUserId } = e.target;

    if (selectedUserId) {
      setCertificateValidation((prev) => {
        const { userId, ...rest } = prev; // keyName can ve changed
        return rest;
      });
    }

    updateSetUploadCertificateArr(index, {
      userId: selectedUserId,
    });
  };

  // Function to handle certificate upload
  const handleCertificateUploadFunc = async (imageValues, index) => {
    updateSetUploadCertificateArr(index, {
      link: imageValues?.[0]?.url || "",
      fileName: imageValues?.[0]?.name || "",
      size: imageValues?.[0]?.size || "",
    });

    // deleting the empty values by key name
    if (certificateValidation?.link === "") {
      setCertificateValidation((prev) => {
        const { link, ...rest } = prev;
        return rest;
      });
    }
  };

  // Function to handle certificate image cancel
  const handleCertificateCancelFunc = (e, index) => {
    updateSetUploadCertificateArr(index, {
      link: "",
      fileName: "",
      size: "",
    });
  };

  // Function to handle certificate date change
  const handleCertificateDateChange = (val, index, type) => {
    let adjustedDate = null;

    // Only format the date if val is not empty/null
    if (val && val.trim() !== "") {
      adjustedDate = moment(val)
        .tz(Common.CURRENT_TIME_ZONE)
        .format(Common.CURRENT_TIME_ZONE_FORMAT);
    }

    if (type === Common.START && val) {
      setCertificateValidation((prev) => {
        const { startDate, ...rest } = prev;
        return rest;
      });
    } else if (type === Common.END && val) {
      setCertificateValidation((prev) => {
        const { endDate, ...rest } = prev;
        return rest;
      });
    }
    updateSetUploadCertificateArr(index, {
      ...(type === Common.START && { startDate: adjustedDate }),
      ...(type === Common.END && { endDate: adjustedDate }),
    });
  };

  // Function to handle certificate internal check
  const handleCertificateInternalCheckChange = (e, index) => {
    updateSetUploadCertificateArr(index, { internal: e.target.checked });
  };

  // Function to add another certificate
  const handleAddAnotherCertificate = () => {
    const getEmptyFields = filterEmptyValues(uploadedCertificatesArr[currentCertificateIndex]);

    if (Object.entries(getEmptyFields).length) {
      setCertificateValidation({
        ...getEmptyFields,
        index: currentCertificateIndex,
      });
      return;
    }

    // If validation is successful ==>
    const recentlyAddedObj = uploadedCertificatesArr[currentCertificateIndex];

    if (fromUserDashboard) {
      // Getting the User Certificate Count Object
      const countObj = userCertificateCount.find(
        (item) => item?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.userId
      );

      // Updating the Certificate Type Options
      setCertificateTypeOptions((prev) => {
        const updatedArr = prev.map((user) => {
          if (user?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.userId) {
            const updatedCertificateOptions = user?.certificateOptions?.map((certificate) => {
              if (certificate?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.certificateType) {
                return { ...certificate, hide: true }; // Updating the certificate
              }
              return certificate; // Keep other certificates unchanged
            });
            return { ...user, certificateOptions: updatedCertificateOptions }; // Return the updated user
          }
          return user; // Return unchanged user
        });

        return updatedArr; // Return the updated array to update the state
      });

      // Updating the Certificate Count Options
      setUserCertificateCount((prev) => {
        const updatedArr = prev.map((user) => {
          if (user?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.userId) {
            return {
              ...user,
              currentCount: user.currentCount + 1,
            };
          }
          return user;
        });
        return updatedArr;
      });

      // Updating the User Options array once all certificates are added for a user
      if (countObj && countObj.currentCount + 1 === countObj.totalCount) {
        setUserOptions((prev) => {
          const updatedArr = prev.map((user) => {
            if (user?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.userId) {
              return { ...user, hide: true };
            }
            return user;
          });
          return updatedArr;
        });
      }
    } else {
      // Updating the Certificate Type Options
      setCertificateTypeOptions((prev) => {
        const updatedArr = prev.map((item) => {
          if (item?.[Constants.MONGOOSE_ID] === recentlyAddedObj?.certificateType) {
            return { ...item, hide: true };
          }
          return item;
        });
        return updatedArr;
      });
    }

    setCertificateValidation({});
    updateSetUploadCertificateArr(currentCertificateIndex, { disabled: true });

    // Updating the Uploaded Certificate Array then incrementing the current index value
    setUploadedCertificatesArr((prev) => [...prev, uploadCertificateInitialValues]);
    setCurrentCertificateIndex(currentCertificateIndex + 1);
  };

  // Function to remove single certificate
  const handleRemoveAnotherCertificate = (itemObj) => {
    const currentCertiVal = currentCertificateIndex - 1;
    setCurrentCertificateIndex(currentCertiVal); // set current index

    if (fromUserDashboard) {
      // Getting the User Certificate Count Object
      const countObj = userCertificateCount.find(
        (item) => item?.[Constants.MONGOOSE_ID] === itemObj?.userId
      );

      // Updating the Certificate Type Options
      setCertificateTypeOptions((prev) => {
        const updatedArr = prev.map((user) => {
          if (user?.[Constants.MONGOOSE_ID] === itemObj?.userId) {
            const updatedCertificateOptions = user?.certificateOptions?.map((certificate) => {
              if (certificate?.[Constants.MONGOOSE_ID] === itemObj?.certificateType) {
                return { ...certificate, hide: false }; // Updating the certificate
              }
              return certificate; // Keeping other certificates unchanged
            });
            return { ...user, certificateOptions: updatedCertificateOptions }; // Returning the updated user
          }
          return user; // Returning unchanged user
        });

        return updatedArr; // Returning the updated array to update the state
      });

      // Updating the Certificate Count Options
      setUserCertificateCount((prev) => {
        const updatedArr = prev.map((user) => {
          if (user?.[Constants.MONGOOSE_ID] === itemObj?.userId) {
            return {
              ...user,
              currentCount: user.currentCount - 1,
            };
          }
          return user;
        });
        return updatedArr;
      });

      // Updating the User Options array once any certificates are removed for a user
      if (countObj && countObj?.currentCount === countObj?.totalCount) {
        setUserOptions((prev) => {
          const updatedArr = prev.map((user) => {
            if (user?.[Constants.MONGOOSE_ID] === itemObj?.userId) {
              return { ...user, hide: false };
            }
            return user;
          });
          return updatedArr;
        });
      }

      // Removing the deleted item by id and updating the Upoaded Certificate Array
      setUploadedCertificatesArr((prev) =>
        prev.filter(
          (item) =>
            !(
              item?.userId === itemObj?.userId && item?.certificateType === itemObj?.certificateType
            )
        )
      );
    } else {
      // Updating the Certificate Type Options once a certificate is removed
      setCertificateTypeOptions((prev) => {
        const updatedArr = prev.map((item) => {
          if (item?.[Constants.MONGOOSE_ID] === itemObj?.certificateType) {
            return { ...item, hide: false };
          }
          return item;
        });
        return updatedArr;
      });

      // Removing the deleted item by id and updating the Upoaded Certificate Array
      setUploadedCertificatesArr((prev) => {
        const updatedArr = prev.filter(
          (item) => item?.certificateType !== itemObj?.certificateType
        );
        return updatedArr;
      });
    }

    // Variable for rendering the ImageUpload Component
    setRenderImageField((prev) => !prev);
  };

  // func to upload all certificates
  const handleUploadCertificateFunc = async () => {
    setLoading(true);
    const getEmptyFields = filterEmptyValues(uploadedCertificatesArr[currentCertificateIndex]);

    if (Object.entries(getEmptyFields).length) {
      setCertificateValidation({
        ...getEmptyFields,
        index: currentCertificateIndex,
      });
      setLoading(false);
      return;
    }
    setCertificateValidation({});

    const res = await handleAllCertificateUpload(uploadedCertificatesArr);
    if (res) {
      setCurrentCertificateIndex(0);
      setUploadedCertificatesArr([uploadCertificateInitialValues]);
      setCertificateTypeOptions([]);
      setUserOptions([]);
      setUserCertificateCount([]);
      dispatch(
        openSnackbar({
          message: Constants.CERTIFICATE_ADDED_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setLoading(false);
  };

  // Modal Close Func
  const handleCloseModalFunc = () => {
    closeModal();
    setUploadedCertificatesArr([uploadCertificateInitialValues]);
    setCertificateTypeOptions([]);
    setUserOptions([]);
    setUserCertificateCount([]);
    setCurrentCertificateIndex(0);
    setCertificateValidation({});
  };

  useEffect(() => {
    if (fromUserDashboard && certificateTypeList?.length > 0 && usersDropdownList?.length > 0) {
      const updatedArr = usersDropdownList?.map((item) => ({
        [Constants.MONGOOSE_ID]: item?.[Constants.MONGOOSE_ID],
        certificateOptions: certificateTypeList,
      }));
      setCertificateTypeOptions(updatedArr);

      const newUpdatedArr = usersDropdownList?.map((item) => ({
        [Constants.MONGOOSE_ID]: item?.[Constants.MONGOOSE_ID],
        currentCount: 0,
        totalCount: certificateTypeList?.length,
      }));

      setUserCertificateCount(newUpdatedArr);
    } else {
      setCertificateTypeOptions(certificateTypeList);
    }
  }, [certificateTypeList, usersDropdownList, fromUserDashboard]);

  useEffect(() => {
    setUserOptions(usersDropdownList);
  }, [usersDropdownList, fromUserDashboard]);

  // Replace ReactDatePicker with FTextField for date selection
  const renderDatePicker = (
    selectedDate,
    index,
    type,
    label,
    error,
    helperText,
    minDate,
    maxDate,
    isDisable
  ) => (
    <FTextField
      label={`${label}*`}
      placeholder={defaultData.REACTDATETIMEPICKER_DATE_FORMAT}
      name={type}
      id={`${type}_${index}`}
      type="date"
      value={selectedDate ? moment(selectedDate).format(defaultData.DATABSE_DATE_FORMAT) : ""}
      error={error}
      helperText={helperText}
      handleChange={(e) => handleCertificateDateChange(e.target.value, index, type)}
      InputProps={{
        inputProps: {
          min: minDate ? moment(minDate).format(defaultData.DATABSE_DATE_FORMAT) : undefined,
          max: maxDate ? moment(maxDate).format(defaultData.DATABSE_DATE_FORMAT) : undefined,
          style: { textTransform: "uppercase" },
        },
      }}
      onKeyPress={(e) => {
        // Prevent manual typing, only allow date picker selection
        e.preventDefault();
      }}
      disabled={isDisable}
    />
  );

  return (
    <BasicModal
      open={openModal}
      handleClose={handleCloseModalFunc}
      title={ButtonTitles.UPLOAD_CERTIFICATES}
      actionButton={ButtonTitles.SAVE}
      btnIcon={Icons.SAVE}
      handleAction={handleUploadCertificateFunc}
      disabled={loading}
      minWidth={fromUserDashboard ? 1200 : 1100}
      isAdditionalBtnRequired
      additionalBtnTitle={ButtonTitles.ADD_ANOTHER}
      additionalBtnIcon={Icons.NEW}
      additionalBtnAction={handleAddAnotherCertificate}
    >
      {uploadedCertificatesArr.map((elem, index) => (
        <MDBox key={elem?.link} id={`certificate_${index}`}>
          <MDBox display="flex" justifyContent="space-between" flexWrap="wrap" sx={{ gap: 2 }}>
            {/* User List Option for User Dashboard */}
            {fromUserDashboard && (
              <MDBox sx={{ display: "flex", flex: 0.8, flexDirection: "column" }} mt={pxToRem(8)}>
                <CustomAutoComplete
                  label={Common.USERS_LABEL}
                  id={`user${index}`}
                  name="userId"
                  hint="Select User"
                  mt={pxToRem(8)}
                  disabled={elem.disabled}
                  handleChange={(e) => handleUserChangeFunc(e, index)}
                  menu={userOptions}
                  error={
                    certificateValidation?.index === index && certificateValidation?.userId === "" // KeyName can be changed
                  }
                  helperText={
                    certificateValidation?.index === index && certificateValidation?.userId === "" // KeyName can be changed
                      ? Constants.USER_NOT_SELECTED
                      : ""
                  }
                  getOptionLabel={(option) => option.title || ""}
                  value={{
                    title: userOptions.find((item) => item[Constants.MONGOOSE_ID] === elem.userId)
                      ?.title,
                  }}
                />
              </MDBox>
            )}

            <MDBox sx={{ display: "flex", flex: 0.8, flexDirection: "column" }} mt={pxToRem(8)}>
              <CustomAutoComplete
                label={Common.CERTIFICATE_TYPE_LABEL}
                id={`certificateType_${index}`}
                name="certificateType"
                hint="Select Certificate Type"
                mt={pxToRem(8)}
                disabled={elem.disabled}
                handleChange={(e) => handleChangeUploadCertificateType(e, index, elem?.userId)}
                menu={
                  fromUserDashboard
                    ? certificateTypeOptions?.find(
                        (item) => item?.[Constants.MONGOOSE_ID] === elem?.userId
                      )?.certificateOptions || []
                    : certificateTypeOptions
                }
                error={
                  certificateValidation?.index === index &&
                  certificateValidation?.certificateType === ""
                }
                helperText={
                  certificateValidation?.index === index &&
                  certificateValidation?.certificateType === ""
                    ? Constants.CERTIFICATE_TYPE_NOT_SELECTED
                    : fromUserDashboard && !elem?.userId && "Please first select a user"
                }
                getOptionLabel={(option) => option.title || ""}
                value={{
                  title: getCertificateTypeValueFunc(elem?.certificateType, elem?.userId),
                }}
              />
            </MDBox>

            <MDBox
              sx={{
                display: "flex",
                flex: 0.8,
                flexDirection: "column",
              }}
              mt={pxToRem(8)}
            >
              <ImageUpload
                key={renderImageField}
                name="certificate"
                label={Common.CERTIFICATE_UPLOAD_LABEL}
                onImageUpload={(e) => handleCertificateUploadFunc(e, index)}
                onImageCancel={(e) => handleCertificateCancelFunc(e, index)}
                data={
                  elem?.link
                    ? [
                        {
                          name: elem?.fileName,
                          size: elem?.size,
                          url: elem?.link,
                        },
                      ]
                    : []
                }
                type="uploadCertificate"
                formats={[
                  Common.IMAGE_JPEG,
                  Common.IMAGE_JPG,
                  Common.IMAGE_PNG,
                  Common.IMAGE_SVG,
                  Common.PDF_FILE,
                ]}
                acceptType="image/*, application/pdf"
                maxImageCount={1}
                isBottomMarginRequired={false}
                imageTypeError={Constants.IMAGE_FILE_TYPE_NOT_ALLOWED}
                direction="row"
                disabled={elem.disabled}
                error={certificateValidation?.index === index && certificateValidation?.link === ""}
                helperText={
                  certificateValidation?.index === index && certificateValidation?.link === ""
                    ? Constants.CERTIFICATE_REQUIRED
                    : ""
                }
                sizeUpdate={false}
              />
            </MDBox>

            <MDBox sx={{ display: "flex", flex: 0.8 }}>
              {renderDatePicker(
                elem?.startDate,
                index,
                Common.START,
                Common.START_DATE_LABEL,
                certificateValidation?.index === index && certificateValidation?.startDate === null,
                certificateValidation?.index === index && certificateValidation?.startDate === null
                  ? Constants.START_DATE_REQUIRED
                  : "",
                null,
                elem?.endDate,
                elem?.disabled || !elem?.isValidityDate
              )}
            </MDBox>

            <MDBox sx={{ display: "flex", flex: 0.8 }}>
              {renderDatePicker(
                elem?.endDate,
                index,
                Common.END,
                Common.END_DATE_LABEL,
                certificateValidation?.index === index && certificateValidation?.endDate === null,
                certificateValidation?.index === index && certificateValidation?.endDate === null
                  ? Constants.END_DATE_REQUIRED
                  : "",
                elem?.startDate,
                null,
                elem?.disabled || !elem?.isValidityDate
              )}
            </MDBox>

            <MDBox
              sx={{
                display: "flex",
                flex: 0.3,
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <MDTypography variant="subtitle2">Internal</MDTypography>
              <MDBox mt={pxToRem(20)}>
                <CustomCheckbox
                  name="Internal"
                  checked={elem.internal}
                  onChange={(e) => handleCertificateInternalCheckChange(e, index)}
                />
              </MDBox>
            </MDBox>

            {uploadedCertificatesArr?.length > 1 && (
              <MDBox
                sx={{
                  display: "flex",
                  flex: 0.3,
                  flexDirection: "column",
                  alignItems: "center",
                }}
                mt={pxToRem(34)}
              >
                <IconButton
                  id={index}
                  aria-label="delete options"
                  color="error"
                  onClick={() => handleRemoveAnotherCertificate(elem)}
                >
                  {Icons.DELETE}
                </IconButton>
              </MDBox>
            )}
          </MDBox>
        </MDBox>
      ))}
    </BasicModal>
  );
}

UserCertificateUploadModal.defaultProps = {
  fromUserDashboard: false,
};

UserCertificateUploadModal.propTypes = {
  openModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  handleAllCertificateUpload: PropTypes.func.isRequired,
  uploadCertificateInitialValues: PropTypes.objectOf(PropTypes.string).isRequired,
  fromUserDashboard: PropTypes.bool,
};

export default UserCertificateUploadModal;

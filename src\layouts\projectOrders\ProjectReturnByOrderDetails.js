import React, { useEffect, useState } from "react";

// 3rd Party libraries
import { useLocation } from "react-router-dom";
import { Divider, Tooltip } from "@mui/material";
import moment from "moment";

// Components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import CustomImage from "components/Table/GroupImage";
import FullScreenImageComponent from "components/ViewFullImage/ViewImage";

// Examples component
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DataTable from "examples/Tables/DataTable";
import CheckInCheckOutComments from "examples/modal/CheckInCheckOutComments/CheckInCheckOutComments";

// Layout
import ProjectReturnOrderEquipmentData from "layouts/projectOrders/data/ProjectOrderStatus/ProjectReturnOrderEquipmentData";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { openSnackbar } from "redux/Slice/Notification";
import { getProjectReturnOrderById } from "redux/Thunks/EquipmentRequest";

// Constants
import Constants, { Icons, Colors, defaultData } from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";
import { getFormattedCallingFirstName } from "utils/methods/methods";

// Assets
import User from "assets/images/UserDefault.png";

const boxStylesForGridView = {
  flex: "1 1 calc(25% - 16px)", // Ensures 4 items per row
  minWidth: "150px", // Prevents boxes from shrinking too much
  display: "flex",
  flexDirection: "column",
  alignItems: "left",
  justifyContent: "left",
};

const cardBoxLabelStyle = {
  fontWeight: 400,
  fontSize: pxToRem(14),
  color: "#475467",
};

const cardBoxValueStyle = {
  fontWeight: 600,
  fontSize: pxToRem(14),
  color: "#191A51",
  textTransform: "capitalize",
};

function ProjectReturnByOrderDetails() {
  const dispatch = useDispatch();

  const location = useLocation();
  const { projectId = null, orderNumber, projectName } = location.state;

  const { projectReturnOrderByIdList = [], projectReturnOrderByIdLoading } = useSelector(
    (state) => state.equipmentRequest
  );

  const [wmCommentsData, setWMCommentsData] = useState({
    open: false,
    type: "",
    comments: [],
  });

  // States
  const [fullScreenImage, setFullScreenImage] = useState(null);
  const [returnByDetailsObj, setReturnByDetailsObj] = useState({});
  const [shouldUpdateState, setShouldUpdateState] = useState(false);

  // Table Functions
  const handleImageFullView = (imageUrl) => {
    setFullScreenImage(imageUrl);
  };

  const handleCloseFullView = () => {
    setFullScreenImage(null);
  };

  const openRemarkModal = (type, remark) => {
    const { open, ...rest } = wmCommentsData;
    setWMCommentsData({
      ...rest,
      open: true,
      type,
      comments: remark,
    });
  };

  // Table Columns
  const { equipmentTypeColumn, equipmentTypeRows } = ProjectReturnOrderEquipmentData({
    dataList: returnByDetailsObj?.returnOrderHistory || [],
    openRemarkModal,
  });

  const getProjectReturnOrderByIdFunc = async (id) => {
    try {
      const payload = {
        id,
      };
      await dispatch(getProjectReturnOrderById(payload));
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    if (projectId) {
      getProjectReturnOrderByIdFunc(projectId);
    }
  }, [projectId, shouldUpdateState]);

  useEffect(() => {
    const returnOrderDataList = projectReturnOrderByIdList?.returnOrderList || [];
    const returnOrderObj = returnOrderDataList.find((item) => item.orderNumber === orderNumber);

    if (returnOrderObj) {
      setReturnByDetailsObj(returnOrderObj);
    }
  }, [projectReturnOrderByIdList]);
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between">
        <PageTitle title={projectName || ""} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={() => setShouldUpdateState((prev) => !prev)}
          />
        </MDBox>
      </MDBox>

      <Divider sx={{ marginTop: 2 }} />

      <MDBox display="flex" flexDirection="column" gap={2} mt={2}>
        <MDBox
          display="flex"
          flexWrap="wrap"
          bgColor="white"
          gap={2}
          border="1px solid #E0E6F5"
          borderRadius="8px"
          padding="16px 24px"
          minHeight="140px"
        >
          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order No.</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>{returnByDetailsObj?.orderNumber}</MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order By</MDTypography>
            <MDBox display="flex" alignItems="center">
              <MDBox onClick={() => handleImageFullView(User)} sx={{ cursor: "pointer" }}>
                <CustomImage item={User} width={30} height={30} />
              </MDBox>
              <MDTypography sx={cardBoxValueStyle}>
                {getFormattedCallingFirstName(returnByDetailsObj?.orderBy)}
              </MDTypography>
            </MDBox>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Total Items</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>{returnByDetailsObj?.totalItems}</MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>PM Remark</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {returnByDetailsObj?.remark?.pmRemark?.length > 0 ? (
                <Tooltip title={returnByDetailsObj?.remark?.pmRemark?.[0]?.comment || ""} arrow>
                  <span>
                    {returnByDetailsObj?.remark?.pmRemark?.[0]?.comment &&
                    returnByDetailsObj.remark.pmRemark[0].comment.length >
                      defaultData.SMALLER_CONTENT_LENGTH
                      ? `${returnByDetailsObj.remark.pmRemark[0].comment.slice(
                          0,
                          defaultData.SMALLER_CONTENT_LENGTH
                        )}...`
                      : returnByDetailsObj?.remark?.pmRemark?.[0]?.comment}
                  </span>
                </Tooltip>
              ) : (
                <MDTypography sx={cardBoxValueStyle}> </MDTypography>
              )}
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Order Date</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {moment(returnByDetailsObj.createdAt).format("DD/MM/YYYY")}
            </MDTypography>
          </MDBox>

          <MDBox sx={boxStylesForGridView}>
            <MDTypography sx={cardBoxLabelStyle}>Total Returned Quantity</MDTypography>
            <MDTypography sx={cardBoxValueStyle}>
              {returnByDetailsObj?.totalReturnedQuantity}
            </MDTypography>
          </MDBox>
          <MDBox sx={boxStylesForGridView} visibility="hidden">
            It will keep it align
          </MDBox>
          <MDBox sx={boxStylesForGridView} visibility="hidden">
            It will keep it align
          </MDBox>
        </MDBox>

        <MDBox>
          <DataTable
            table={{ columns: equipmentTypeColumn, rows: equipmentTypeRows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE_4 }}
            showTotalEntries={false}
            pagination={{ variant: "gradient", color: "info" }}
            loading={projectReturnOrderByIdLoading}
            licenseRequired
          />
        </MDBox>

        {/* Order History Comments */}
        {wmCommentsData.open && (
          <CheckInCheckOutComments
            open={wmCommentsData.open}
            type={wmCommentsData.type}
            intialComments={wmCommentsData.comments}
            handleClose={() =>
              setWMCommentsData({
                open: false,
                type: "",
                comments: [],
              })
            }
            minWidth={600}
          />
        )}

        {/* View Order By Profile */}
        <FullScreenImageComponent
          fullScreenImage={fullScreenImage}
          handleCloseFullView={handleCloseFullView}
          src={fullScreenImage}
        />
      </MDBox>
    </DashboardLayout>
  );
}

export default ProjectReturnByOrderDetails;

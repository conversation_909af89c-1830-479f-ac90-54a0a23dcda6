import React, { useMemo } from "react";

// 3rd Party libraries
import { IconButton } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";

// Utils
import { Icons, defaultData } from "utils/Constants";
import { getFormattedCallingFirstName } from "utils/methods/methods";
import moment from "moment";

export default function ProjectReturnByOrderData({ dataList = [] }) {
  const navigate = useNavigate();

  const location = useLocation();
  const { projectId = null } = location.state;

  const navigateToAnotherPageFunc = (orderNumber) => {
    navigate("/client/project-orders/returnby/details", {
      state: { projectId, orderNumber, projectName: location?.state?.projectName },
    });
  };

  const projectReturnByIdRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList?.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      orderNumber: <Author name={item?.orderNumber} />,
      returnOrderDate: <Author name={moment(item.createdAt).format(defaultData.WEB_DATE_FORMAT)} />,
      orderBy: <Author name={getFormattedCallingFirstName(item?.orderBy)} />,
      totalItems: <Author name={item.totalItems} />,
      returnQuantity: <Author name={item.totalReturnedQuantity} />,
      action: (
        <MDBox>
          <IconButton
            aria-label="view"
            color="error"
            onClick={() => navigateToAnotherPageFunc(item?.orderNumber)}
          >
            {Icons.VIEW}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList]);

  const projectReturnByIdColumns = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Return Order No.", accessor: "orderNumber" },
    { Header: "Return Order Date", accessor: "returnOrderDate" },
    { Header: "Return By", accessor: "orderBy" },
    { Header: "Total Items", accessor: "totalItems", align: "center" },
    { Header: "Returned Qty", accessor: "returnQuantity", align: "center" },
    { Header: "Action", accessor: "action" },
  ];

  const tableData = {
    projectReturnByIdColumns,
    projectReturnByIdRows,
  };

  return tableData;
}

import MDTypography from "components/MDTypography";
import MDBox from "components/MDBox";
import { useEffect, useState } from "react";
import { Icons } from "utils/Constants";
import { Icon, IconButton } from "@mui/material";

export default function ProjectString(
  projectStringLists,
  handleOpenNewModal,
  setModalType,
  editLists,
  setEditLists,
  handleDelete,
  handleStringSort,
  sorted,
  permission
) {
  const [rows, setRows] = useState([]);
  const mongooseId = "_id";

  const handleEdit = (item) => {
    setModalType("Update");
    setEditLists({ ...editLists, projectString: item });
    handleOpenNewModal("Project String");
  };

  useEffect(() => {
    if (projectStringLists) {
      const list = projectStringLists.map((item) => {
        const temp = {
          name: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.name}
            </MDTypography>
          ),
          from: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.fromLocation?.title}
            </MDTypography>
          ),
          to: (
            <MDTypography variant="caption" color="text" fontWeight="medium">
              {item?.toLocation?.title}
            </MDTypography>
          ),
          action: (
            <MDBox>
              {permission?.update && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleEdit(item)}
                  sx={{ cursor: "pointer" }}
                  disabled={!item?.isDeletable || false}
                >
                  {Icons.EDIT}
                </IconButton>
              )}{" "}
              {permission?.delete && (
                <IconButton
                  color="secondary"
                  fontSize="medium"
                  onClick={() => handleDelete("ProjectString", item[mongooseId])}
                  disabled={!item?.isDeletable || false}
                >
                  {Icons.DELETE}
                </IconButton>
              )}
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [projectStringLists, permission]);

  return {
    ProjectStringColumns: [
      {
        Header: () => (
          <div
            onClick={handleStringSort}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Enter" && handleStringSort()}
            style={{ cursor: "pointer" }}
          >
            Name
            <MDBox
              position="absolute"
              top={-3}
              left="20%"
              color={sorted === "asc" ? "text" : "secondary"}
              opacity={sorted === "asc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_up</Icon>
            </MDBox>
            <MDBox
              position="absolute"
              top={3}
              left="20%"
              color={sorted === "desc" ? "text" : "secondary"}
              opacity={sorted === "desc" ? 1 : 0.5}
            >
              <Icon fontSize="medium">arrow_drop_down</Icon>
            </MDBox>
          </div>
        ),
        accessor: "name",
        width: "30%",
        align: "left",
      },
      { Header: "From", accessor: "from", width: "20%", align: "left" },
      { Header: "To", accessor: "to", width: "20%", align: "left" },
      ...(permission?.update || permission?.delete
        ? [{ Header: "Action", accessor: "action", align: "right", width: "10%" }]
        : []),
    ],
    ProjectStringRows: rows,
  };
}

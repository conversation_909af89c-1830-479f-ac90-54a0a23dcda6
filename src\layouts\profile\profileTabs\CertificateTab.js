import React, { useState } from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box, styled, Tabs, Tab, Grid, Tooltip, Icon, IconButton } from "@mui/material";

// 3rd party libraries
import moment from "moment";
import { useDispatch } from "react-redux";

// Matrial Dashboard React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import Status from "components/Table/Status";
import FullScreenImageComponent from "components/ViewFullImage/ViewImage";
import UserCertificateUploadModal from "components/UserCertificateUpload/UserCertificateUploadModal";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import FTextField from "components/Form/FTextField";

// Material Dashboard React example components
import CustomButton from "examples/NewDesign/CustomButton";

// Redux Thunk
import { createUserCertificate, deleteCertificate } from "redux/Thunks/certificateApproval";

// Images import from assets
import Pdf from "assets/images/pdf.svg";

// Constants from Utils
import Constants, {
  Colors,
  Icons,
  ButtonTitles,
  defaultData,
  Common,
  DropdownOptions,
  ModalContent,
} from "utils/Constants";
import { downloadFile } from "utils/methods/methods";
import { openSnackbar } from "redux/Slice/Notification";

// Custom Styles for Tabs
const CustomTabs = styled(Tabs)({
  backgroundColor: "white",
  border: "1px solid #E0E2E7",
});

const CustomTab = styled((props) => <Tab disableRipple {...props} />)(({ theme }) => ({
  textTransform: "none",
  minWidth: 0,
  [theme.breakpoints.up("sm")]: {
    minWidth: 0,
  },
  color: "#667085",
  "&.Mui-selected": {
    color: "#667085",
    fontWeight: 600,
    backgroundColor: "#DEDEFA",
  },
}));

// Initial values for certificate upload payload.
const uploadCertificateInitialValues = {
  startDate: null,
  endDate: null,
  certificateType: "",
  name: "",
  link: "",
  fileName: "",
  size: "",
  internal: true,
  disabled: false,
  isValidityDate: true,
};

// Fucntional component Starts here
function CertificateTab({ userId, permission, profile, setUpdate }) {
  const dispatch = useDispatch();
  const [certificateTabValue, setCertificateTabValue] = useState(0);
  const [fullScreenImage, setFullScreenImage] = useState(null);
  const [openCertificateUploadModal, setOpenCertificateUploadModal] = useState(false);
  const [certificateDelete, setCertificateDelete] = useState({
    open: false,
    id: "",
  });
  const [deleteText, setDeleteText] = useState("");

  const certificatesList = profile?.userCertificate || [];

  const filterCertificates = () => {
    switch (certificateTabValue) {
      case 0:
        return certificatesList.filter(
          (cert) => cert.status === Constants.APPROVED && cert.isActive
        );
      case 1:
        return certificatesList.filter((cert) => cert.status === Constants.STATUS_EXPIRED);
      case 2:
        return certificatesList.filter((cert) => cert.status === Constants.STATUS_PENDING);
      case 3:
        return certificatesList.filter((cert) => cert.status === Constants.STATUS_REJECTED);
      case 4:
        return certificatesList.filter(
          (cert) => !cert.isActive && cert.status !== Constants.STATUS_PENDING
        );
      case 5:
      default:
        return certificatesList;
    }
  };

  const handleAllCertificateUpload = async (certificatesArr) => {
    const requestBody = {
      user: userId,
      files: certificatesArr,
    };
    const res = await dispatch(createUserCertificate(requestBody));
    if (res?.payload?.status === Common.API_STATUS_200) {
      setOpenCertificateUploadModal(false);
      setUpdate((prev) => !prev);
    }
    return res?.payload?.status === Common.API_STATUS_200;
  };

  const handleCertificateDelete = async () => {
    const res = await dispatch(deleteCertificate(certificateDelete.id));
    if (res.payload.status === 200) {
      await dispatch(
        openSnackbar({ message: Constants.CERTIFICATE_DELETE_SUCCESS, notificationType: "success" })
      );
      setCertificateDelete({ open: false, id: "" });
      setUpdate((prev) => !prev);
    } else {
      await dispatch(
        openSnackbar({ message: Constants.CERTIFICATE_DELETE_ERROR, notificationType: "error" })
      );
      setCertificateDelete({ open: false, id: "" });
    }
  };

  const handleOpenCertificateDelete = (id) => {
    setDeleteText("");
    setCertificateDelete({
      open: true,
      id,
    });
  };

  const renderCertificates = (certificatesArr = []) => (
    <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 1, md: 3 }} mt={2}>
      {certificatesArr.length > 0 ? (
        certificatesArr.map((element) => (
          <Grid key={element?.[Constants.MONGOOSE_ID]} item xs={10} xl={5}>
            <MDTypography
              display="block"
              variant="caption"
              textTransform="capitalize"
              fontWeight="medium"
              mb={1}
              mt={1}
            >
              {element?.certificateType?.name?.length > defaultData.MEDIUM_CONTENT_LENGTH ? (
                <Tooltip title={element?.certificateType?.name}>
                  <span>
                    {`${element?.certificateType?.name.slice(
                      0,
                      defaultData.MEDIUM_CONTENT_LENGTH
                    )}...`}
                  </span>
                </Tooltip>
              ) : (
                <span>{element?.certificateType?.name}</span>
              )}
            </MDTypography>
            <Box
              display="flex"
              border="1px solid #E0E6F5"
              borderRadius="8px"
              p={1}
              sx={{ cursor: "pointer", position: "relative" }}
              onClick={() => setFullScreenImage(element?.link)}
            >
              {/* Delete Icon */}
              {permission?.delete && (
                <MDBox position="absolute" top={-10} right={-10} zIndex={2}>
                  <Icon
                    sx={{
                      color: "white",
                      width: 30,
                      height: 30,
                      cursor: "pointer",
                      zIndex: 1,
                    }}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent full-screen view
                      handleOpenCertificateDelete(element?.[Constants.MONGOOSE_ID]);
                    }}
                  >
                    {Icons.CROSS2}
                  </Icon>
                </MDBox>
              )}

              {!element?.link.includes(Common.PDF) ? (
                <img
                  src={element?.link || process.env.REACT_APP_IMAGE_NOT_FOUND}
                  alt="Preview"
                  height="60px"
                  width="60px"
                  style={{
                    border: "1px solid #D0D5DD",
                    borderRadius: "8px",
                    marginTop: "5px",
                    marginLeft: "4px",
                  }}
                />
              ) : (
                <img
                  src={Pdf}
                  alt="Preview"
                  height="60px"
                  width="60px"
                  style={{
                    marginTop: "5px",
                    marginLeft: "4px",
                  }}
                />
              )}
              <Box ml={2}>
                <MDTypography
                  display="block"
                  variant="caption"
                  sx={{ textTransform: "capitalize" }}
                >
                  {element?.fileName?.length > defaultData.SMALLER_CONTENT_LENGTH ? (
                    <Tooltip title={element?.fileName}>
                      <span>{`${element?.fileName.slice(
                        0,
                        defaultData.SMALLER_CONTENT_LENGTH
                      )}...`}</span>
                    </Tooltip>
                  ) : (
                    <span>{element?.fileName}</span>
                  )}
                </MDTypography>
                {element?.endDate && (
                  <MDTypography display="block" variant="caption" color="text">
                    End date: {moment(element?.endDate).format(defaultData.WEB_DATE_FORMAT)}
                  </MDTypography>
                )}
                <MDTypography display="block" variant="caption" color="text">
                  {element?.size ? `${element?.size} KB` : Constants.NA}
                </MDTypography>
                <Box mt={1}>
                  <Status title={`${element?.status.replace("_", " ")}`} />
                </Box>
              </Box>
              <IconButton
                sx={{ ml: "auto" }}
                onClick={(event) => downloadFile(element?.link, event, element?.fileName)}
              >
                {Icons.DOWNLOAD}
              </IconButton>
            </Box>
          </Grid>
        ))
      ) : (
        <MDTypography
          mt="25%"
          ml="30%"
          variant="h6"
          fontWeight="medium"
          color="text"
          textTransform="capitalize"
        >
          No Certificates Uploaded
        </MDTypography>
      )}
    </Grid>
  );

  return (
    <Box ml={2} sx={{ px: 2 }}>
      <Box display="flex" justifyContent="space-between">
        <MDTypography variant="h5" fontWeight="medium" color="text" textTransform="capitalize">
          Certificates
        </MDTypography>

        {permission?.create && (
          <CustomButton
            title={ButtonTitles.UPLOAD_CERTIFICATES}
            icon={Icons.NEW}
            background={Colors.PRIMARY}
            color={Colors.WHITE}
            openModal={setOpenCertificateUploadModal}
          />
        )}
      </Box>

      {/* User Certificate Upload Modal */}
      {openCertificateUploadModal && (
        <UserCertificateUploadModal
          openModal={openCertificateUploadModal}
          closeModal={() => setOpenCertificateUploadModal(false)}
          handleAllCertificateUpload={handleAllCertificateUpload}
          uploadCertificateInitialValues={uploadCertificateInitialValues}
        />
      )}

      {/* Full Screen Image Component For Certificates View */}
      <FullScreenImageComponent
        fullScreenImage={fullScreenImage}
        handleCloseFullView={() => setFullScreenImage(null)}
        src={fullScreenImage}
      />

      <MDBox mt={2}>
        <CustomTabs
          value={certificateTabValue}
          onChange={(e, val) => setCertificateTabValue(val)}
          aria-label="certificate tabs"
        >
          {DropdownOptions.USER_CERTIFICATE_TAB.map((label) => (
            <CustomTab key={label} label={label} />
          ))}
        </CustomTabs>
      </MDBox>
      {renderCertificates(filterCertificates())}
      <DeleteModal
        open={certificateDelete.open}
        title={ModalContent.CERTIFICATE_DELETE_TITLE}
        message={ModalContent.CERTIFICATE_DELETE_MESSAGE}
        handleClose={() => setCertificateDelete({ open: false, id: "" })}
        handleDelete={handleCertificateDelete}
        confirmText="DELETE"
        confirmInput={deleteText}
      >
        <FTextField
          placeholder="Enter Message"
          name="delete"
          id="delete"
          type="text"
          value={deleteText}
          handleChange={(e) => setDeleteText(e.target.value)}
        />
      </DeleteModal>
    </Box>
  );
}

CertificateTab.propTypes = {
  permission: PropTypes.objectOf(PropTypes.string).isRequired,
  profile: PropTypes.objectOf(PropTypes.any).isRequired,
  setUpdate: PropTypes.func.isRequired,
  userId: PropTypes.number.isRequired,
};

export default CertificateTab;

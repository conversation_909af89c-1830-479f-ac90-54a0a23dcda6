import React, { useState, useEffect } from "react";

// Custom Components
import MDBox from "components/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import DataTable from "examples/Tables/DataTable";
import licenseData from "layouts/superadmin/LicenseApprovals/data/licenseData";
import ConfirmationModal from "examples/modal/ConfirmationModal";

// Redux
import { useDispatch } from "react-redux";
import { pendingLicenseThunk, pendingActionThunk } from "redux/Thunks/License";
import { openSnackbar } from "redux/Slice/Notification";

// Constants
import Constants, { PageTitles, defaultData } from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

function LicenseApproval() {
  const dispatch = useDispatch();
  const [pendingRequest, setPendingRequest] = useState({
    list: [],
    loadingStatus: Constants.PENDING,
    refresh: false,
  });
  const [openApprove, setOpenApprove] = useState(false);
  const [openReject, setOpenReject] = useState(false);
  const [selectedId, setSelectedId] = useState("");
  const [isBtnDisabled, setIsBtnDisabled] = useState(false);
  const [tablePagination, setTablePagination] = useState({
    page: defaultData.PAGE,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });
  const [next, setNext] = useState(0);

  const handleApproveClose = () => setOpenApprove(false);
  const handleRejectClose = () => setOpenReject(false);

  const fetchPendingLicenses = async () => {
    setTablePagination({ ...tablePagination, page: 0 });
    const paramData = {
      page: 0,
      perPage: tablePagination.perPage,
    };
    const res = await dispatch(pendingLicenseThunk(paramCreater(paramData)));
    if (res.payload?.status === 200) {
      setPendingRequest({
        list: res.payload.data.data,
        loadingStatus: Constants.FULFILLED,
      });
    } else {
      setPendingRequest({
        list: [],
        loadingStatus: Constants.REJECTED,
      });
    }
  };

  useEffect(() => {
    fetchPendingLicenses();
  }, []);

  const openConfirmationBox = (id, type) => {
    setSelectedId(id);
    if (type === "approve") {
      setOpenApprove(true);
    } else {
      setOpenReject(true);
    }
  };

  const handlePendingAction = async (body) => {
    setIsBtnDisabled(true);
    const data = { id: selectedId, body };
    const res = await dispatch(pendingActionThunk(data));
    if (res.payload?.status === 200) {
      dispatch(
        openSnackbar({
          message: res.payload?.data.message,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      setTablePagination({ ...tablePagination, page: 0 });
      setNext(0);
      await fetchPendingLicenses();
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    if (body.action === "approve") {
      setOpenApprove(false);
    } else {
      setOpenReject(false);
    }
    setIsBtnDisabled(false);
  };

  const handleTablePagination = async () => {
    const paramData = {
      page: next + 1,
      perPage: tablePagination.perPage,
    };
    const res = await dispatch(pendingLicenseThunk(paramCreater(paramData)));
    if (res.payload?.status === 200) {
      setPendingRequest({
        ...pendingRequest,
        list: [...pendingRequest.list, ...res.payload.data.data],
      });
      setNext(res.payload.data.data.length > 0 ? next + 1 : next);
    }
  };

  const { columns, rows } = licenseData(pendingRequest.list, openConfirmationBox);
  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between" alignItems="center" fullWidth>
        <PageTitle title={PageTitles.LICENSE_APPROVAL} />
      </MDBox>

      <MDBox mt={3}>
        <DataTable
          table={{ columns, rows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
          showTotalEntries={false}
          pagination={{ variant: "gradient", color: "info" }}
          loading={pendingRequest.loadingStatus}
          licenseRequired
          currentPage={tablePagination.page}
          handleTablePagination={handleTablePagination}
          handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
        />
      </MDBox>
      <ConfirmationModal
        title="Approve Permission"
        open={openApprove}
        handleClose={handleApproveClose}
        handleAction={handlePendingAction}
        isBtnDisabled={isBtnDisabled}
      />

      <ConfirmationModal
        title="Reject Permission"
        open={openReject}
        handleClose={handleRejectClose}
        handleAction={handlePendingAction}
        isBtnDisabled={isBtnDisabled}
      />
    </DashboardLayout>
  );
}

export default LicenseApproval;

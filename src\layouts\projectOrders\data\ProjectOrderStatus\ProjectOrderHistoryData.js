import React, { useMemo } from "react";

// 3rd Party libraries
import { IconButton } from "@mui/material";
import { useNavigate } from "react-router-dom";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";

// Utils
import Constants, { Icons } from "utils/Constants";

export default function ProjectOrderHistoryData({ dataList = [] }) {
  const navigate = useNavigate();

  const navigateToAnotherPageFunc = (item) => {
    navigate("/client/project-orders/orderby", {
      state: { projectId: item?.[Constants.MONGOOSE_ID], projectName: item?.projectName },
    });
  };

  const projectHistoryRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      project: <Author name={item?.projectName} />,
      totalOrders: <Author name={item.totalOrders} />,
      action: (
        <MDBox>
          <IconButton
            aria-label="view"
            color="error"
            onClick={() => navigateToAnotherPageFunc(item)}
          >
            {Icons.VIEW}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList]);

  const projectHistoryColumns = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Project", accessor: "project" },
    { Header: "Total Orders", accessor: "totalOrders", width: "15%" },
    { Header: "Action", accessor: "action", width: "15%" },
  ];

  const tableData = {
    projectHistoryColumns,
    projectHistoryRows,
  };

  return tableData;
}

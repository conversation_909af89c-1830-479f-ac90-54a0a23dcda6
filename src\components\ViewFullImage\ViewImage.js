import React, { useEffect } from "react";
import ReactD<PERSON> from "react-dom";

// MUI Components
import { Icon } from "@mui/material";

// Custom Components
import MDBox from "components/MDBox";

// Constants
import { Icons, Colors, Common } from "utils/Constants";

// 3rd party library
import PropTypes from "prop-types";
import pxToRem from "assets/theme/functions/pxToRem";

export default function FullScreenImageComponent({
  fullScreenImage,
  handleCloseFullView,
  handlePreviousImage,
  handleNextImage,
  image,
  src,
  width,
  height,
  imageData,
  type,
}) {
  const isPdf = typeof src === "string" && src.includes(".pdf");

  const images = Array.isArray(image) ? image : [image];

  useEffect(() => {
    if (isPdf && fullScreenImage) {
      window.open(src, "_blank");
      handleCloseFullView();
    }
  }, [isPdf, fullScreenImage, src, handleCloseFullView]);

  if (!fullScreenImage || isPdf) return null;

  const content = (
    <MDBox
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "column",
      }}
    >
      <Icon
        style={{
          position: "fixed",
          top: "1rem",
          right: "1rem",
          width: "30px",
          height: "30px",
          display: "flex",
          justifyContent: "flex-end",
        }}
        onClick={handleCloseFullView}
      >
        {Icons.REJECT}
      </Icon>
      {images && handlePreviousImage && images.length > 1 ? (
        <Icon
          style={{
            position: "absolute",
            top: "50%",
            left: "1rem",
            transform: "translateY(-50%)",
            display: "flex",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={handlePreviousImage}
          fontSize="large"
        >
          {Icons.PREVIOUS}
        </Icon>
      ) : null}
      <img
        src={src}
        alt="Full Screen"
        style={{
          position: "relative",
          width,
          height,
          maxWidth: "90%",
          maxHeight: "90%",
          objectFit: "contain",
          marginBottom: "1rem",
          ...(type === Common.SMALL_SIGNATURE && {
            backgroundColor: Colors.WHITE,
          }),
        }}
      />

      <MDBox>{imageData()}</MDBox>

      {images && handleNextImage && images.length > 1 ? (
        <Icon
          style={{
            position: "absolute",
            top: "50%",
            right: "1rem",
            transform: "translateY(-50%)",
            display: "flex",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={handleNextImage}
          fontSize="large"
        >
          {Icons.NEXT}
        </Icon>
      ) : null}
    </MDBox>
  );

  return ReactDOM.createPortal(content, document.body);
}

FullScreenImageComponent.propTypes = {
  fullScreenImage: PropTypes.bool,
  handleCloseFullView: PropTypes.func,
  handlePreviousImage: PropTypes.func,
  handleNextImage: PropTypes.func,
  image: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.object)]),
  src: PropTypes.string,
  width: PropTypes.string,
  height: PropTypes.string,
  imageData: PropTypes.func,
  type: PropTypes.string,
};

FullScreenImageComponent.defaultProps = {
  fullScreenImage: false,
  handleCloseFullView: () => {},
  handlePreviousImage: () => {},
  handleNextImage: () => {},
  image: [],
  src: "",
  width: pxToRem(1113),
  height: pxToRem(542),
  imageData: () => {},
  type: Common.SMALL_IMAGE,
};

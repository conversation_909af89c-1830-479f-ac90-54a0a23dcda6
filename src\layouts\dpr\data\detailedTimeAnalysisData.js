import Author from "components/Table/Author";
import { useEffect, useState } from "react";

function DetailedTimeAnalysisData(timeAnalysisDprList = []) {
  const [timeAnalysisRows, setTimeAnalysisRows] = useState([]);

  useEffect(() => {
    if (timeAnalysisDprList?.length > 0) {
      const detailTimeAnalysisRows = [];
      let totalReportedTimeRow = null;

      timeAnalysisDprList?.forEach((element) => {
        const key = Object.keys(element)[0];
        const value = Object.values(element)[0];

        if (key === "totalReportedTime") {
          totalReportedTimeRow = {
            activity: (
              <Author
                name="Total Reported Time"
                style={{ fontWeight: "bold" }}
                cellColor="#f6f7ff"
              />
            ),
            previous: (
              <Author
                name={value?.totalPrevious}
                style={{ fontWeight: "bold" }}
                cellColor="#f6f7ff"
              />
            ),
            today: (
              <Author name={value?.totalToday} style={{ fontWeight: "bold" }} cellColor="#f6f7ff" />
            ),
            cumulative: (
              <Author
                name={value?.totalCumulative}
                style={{ fontWeight: "bold" }}
                cellColor="#f6f7ff"
              />
            ),
          };
          return;
        }

        if (Array.isArray(value)) {
          detailTimeAnalysisRows.push({
            activity: (
              <Author
                name={key}
                style={{ fontWeight: "bold", textTransform: "capitalize" }}
                cellColor="#f6f7ff"
              />
            ),
            previous: <Author name="Hours" style={{ fontWeight: "bold" }} cellColor="#f6f7ff" />,
            today: <Author name="Hours" style={{ fontWeight: "bold" }} cellColor="#f6f7ff" />,
            cumulative: <Author name="Hours" style={{ fontWeight: "bold" }} cellColor="#f6f7ff" />,
          });

          value.forEach((activity) => {
            detailTimeAnalysisRows.push({
              activity: <Author name={activity.name} style={{ textTransform: "capitalize" }} />,
              previous: <Author name={activity.previous} />,
              today: <Author name={activity.today} />,
              cumulative: <Author name={activity.cumulative} />,
            });
          });
        }
      });

      if (totalReportedTimeRow) {
        detailTimeAnalysisRows.push(totalReportedTimeRow);
      }

      setTimeAnalysisRows([...detailTimeAnalysisRows]);
    }
  }, [timeAnalysisDprList]);

  return {
    timeAnalysisColumns: [
      { Header: "Activity", accessor: "activity", width: "25%" },
      { Header: "Previous", accessor: "previous", align: "left", width: "20%" },
      { Header: "Today", accessor: "today", align: "left", width: "15%" },
      { Header: "Cumulative", accessor: "cumulative", align: "left", width: "15%" },
    ],
    timeAnalysisRows,
  };
}

export default DetailedTimeAnalysisData;

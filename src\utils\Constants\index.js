// Lib
import countries from "countries-list";

// Constants
import Messages from "utils/Constants/Messages.constants";
import ButtonTitlesConstants from "utils/Constants/ButtonTitles.constants";
import CardTitlesConstants from "utils/Constants/CardTitles.constants";
import FormFieldsConstants from "utils/Constants/ConfigurationFields.constants";
import defaultDataConstants from "utils/Constants/DefaultData.constants";
import FeatureTagsConstants from "utils/Constants/FeatureTags.constants";
import LicensePermissionConstants from "utils/Constants/LicensePermission.constants";
import ModelContentConstants from "utils/Constants/ModelContent.constants";
import PageTitleConstants from "utils/Constants/PageTitles.constants";
import ColorsConstants from "utils/Constants/Colors.constants";
import IconsConstants from "utils/Constants/Icons.constants";
import CommonConstants from "utils/Constants/Common.constants";
import FiltersModuleNameConstants from "utils/Constants/FilterConstants.constants";
import DropdownOptionsConstant from "utils/Constants/OptionsArr.constant";
import BackendFrontendConstant from "utils/Constants/CommonBeFe.constant";
import PrecisionUtilsConstants from "utils/Constants/PrecisionUtils.constants";

export default Messages;
export const ButtonTitles = ButtonTitlesConstants;
export const CardTitles = CardTitlesConstants;
export const FormFields = FormFieldsConstants;
export const defaultData = defaultDataConstants;
export const FeatureTags = FeatureTagsConstants;
export const LicensePermission = LicensePermissionConstants;
export const ModalContent = ModelContentConstants;
export const PageTitles = PageTitleConstants;
export const Colors = ColorsConstants;
export const Icons = IconsConstants;
export const Common = CommonConstants;
export const countryList = Object.values(countries.countries).map((country) => country.name);
export const FiltersModuleName = FiltersModuleNameConstants;
export const DropdownOptions = DropdownOptionsConstant;
export const BackendFrontend = BackendFrontendConstant;
export const PrecisionUtils = PrecisionUtilsConstants;

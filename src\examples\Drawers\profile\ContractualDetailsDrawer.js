import React, { useState } from "react";
import PropTypes from "prop-types";
import moment from "moment";

// @mui material components
import { Grid, Box, IconButton, Drawer } from "@mui/material";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import FTextField from "components/Form/FTextField";
import ConfigDropdown from "components/Dropdown/ConfigDropdown";
import ImageUpload from "components/ImageUpload/imageUpload";

// Icons and Constants
import Constants, {
  Common,
  BackendFrontend,
  Icons,
  ButtonTitles,
  defaultData,
} from "utils/Constants";
import pattern from "utils/Patterns";

import MDTypography from "components/MDTypography";

// Redux
import {
  updateProfileContractualThunk,
  createProfileContractualThunk,
} from "redux/Thunks/SuperAdmin";
import { useDispatch } from "react-redux";
import { openSnackbar } from "redux/Slice/Notification";

// Personal COnfig
import { contractualDetailsFields } from "examples/Drawers/profile/PersonalConfig";
import { useParams } from "react-router-dom";

function ContractualDetailsDrawer({ open, onClose, data = {}, contractualDetailId, userId }) {
  // Format initial data to separate primary and Secondpassports
  const formatInitialData = (initialData) => {
    // Extract primary and secondary passport data
    const primaryPassportData = initialData?.identityProof?.filter((doc) => doc.isPrimary) || [];
    const secondaryPassportData =
      initialData?.identityProof?.filter((doc) => doc.isSecondary) || [];

    // Format driving license data
    const drivingLicenseData = initialData.drivingLicence?.[0] || {};

    // Liability insurance data
    const liabilityInsuranceData = initialData?.liabilityInsurance?.[0] || {};

    // Health insurance data
    const healthInsuranceData = initialData?.healthInsurance?.[0] || {};

    // Find the matching employment type option
    const mapEmploymentTypeFromAPI = (apiValue) => {
      if (!apiValue) return "";

      if (apiValue === Common.EXTERNAL_EMPLOYEE_API) {
        return Common.EXTERNAL_EMPLOYEE;
      }
      if (apiValue === Common.INTERNAL_EMPLOYEE_API) {
        return Common.INTERNAL_EMPLOYEE;
      }

      const employmentTypeOptions =
        contractualDetailsFields.find((field) => field.id === "employmentType")?.options || [];
      const matchingOption = employmentTypeOptions.find((opt) => opt.id === apiValue);

      return matchingOption ? apiValue : "";
    };

    // Format seamans book data
    const formattedSeamansBook =
      initialData.seamansBook?.map((book) => ({
        name: book.name || "",
        url: book.url || "",
        size: book.size || 0,
        fromDate: book.fromDate
          ? moment(book.fromDate).format(defaultData.DATABSE_DATE_FORMAT)
          : "",
        toDate: book.toDate ? moment(book.toDate).format(defaultData.DATABSE_DATE_FORMAT) : "",
        status: book.status || defaultData.PENDING_STATUS,
        comment: book.comment || "",
      })) || [];

    return {
      ...initialData,
      birthDate: initialData.birthDate,
      primaryPassport: initialData.passport || "",
      primaryPassportIssueDate: primaryPassportData[0]?.fromDate
        ? moment(primaryPassportData[0].fromDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      primaryPassportExpiryDate: primaryPassportData[0]?.toDate
        ? moment(primaryPassportData[0].toDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      primaryPassportDoc: primaryPassportData.map((doc) => ({
        ...doc,
        name: doc.name,
        url: doc.url,
        size: doc.size,
        fromDate: doc.fromDate ? moment(doc.fromDate).format(defaultData.DATABSE_DATE_FORMAT) : "",
        toDate: doc.toDate ? moment(doc.toDate).format(defaultData.DATABSE_DATE_FORMAT) : "",
        status: doc.status,
        comment: doc.comment,
        isPrimary: true,
        isSecondary: false,
      })),
      secondaryPassport: initialData.secondaryPassport || "",
      secondaryPassportIssueDate: secondaryPassportData[0]?.fromDate
        ? moment(secondaryPassportData[0].fromDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      secondaryPassportExpiryDate: secondaryPassportData[0]?.toDate
        ? moment(secondaryPassportData[0].toDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      secondaryPassportDoc: secondaryPassportData.map((doc) => ({
        ...doc,
        name: doc.name,
        url: doc.url,
        size: doc.size,
        fromDate: doc.fromDate ? moment(doc.fromDate).format(defaultData.DATABSE_DATE_FORMAT) : "",
        toDate: doc.toDate ? moment(doc.toDate).format(defaultData.DATABSE_DATE_FORMAT) : "",
        status: doc.status,
        comment: doc.comment,
        isPrimary: false,
        isSecondary: true,
      })),
      seamansBook: formattedSeamansBook,
      seamansBookIssueDate: initialData.seamansBook?.[0]?.fromDate
        ? moment(initialData.seamansBook[0].fromDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      seamansBookExpiryDate: initialData.seamansBook?.[0]?.toDate
        ? moment(initialData.seamansBook[0].toDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      employmentType: mapEmploymentTypeFromAPI(initialData.employmentType),
      drivingLicenseNumber: drivingLicenseData.licenseNumber || "",
      drivingLicenseIssueDate: drivingLicenseData.issueDate
        ? moment(drivingLicenseData.issueDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      drivingLicenseExpiryDate: drivingLicenseData.expiryDate
        ? moment(drivingLicenseData.expiryDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      liabilityIssueDate: liabilityInsuranceData.fromDate
        ? moment(liabilityInsuranceData.fromDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      liabilityExpiryDate: liabilityInsuranceData.toDate
        ? moment(liabilityInsuranceData.toDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      healthIssueDate: healthInsuranceData.fromDate
        ? moment(healthInsuranceData.fromDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
      healthExpiryDate: healthInsuranceData.toDate
        ? moment(healthInsuranceData.toDate).format(defaultData.DATABSE_DATE_FORMAT)
        : "",
    };
  };

  const [formData, setFormData] = useState(formatInitialData(data));
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const [errors, setErrors] = useState({});
  const handleDrawerClose = () => {
    setErrors({});
    setFormData(formatInitialData(data));
    onClose();
  };

  const handleChange = (name, value) => {
    // For employment type, extract the id if it's an object
    const newValue = name === "employmentType" && typeof value === "object" ? value.id : value;

    setFormData((prev) => ({
      ...prev,
      [name]: newValue,
    }));
  };

  const handleImageChange = (name, imageValues) => {
    const imageInfoArray = imageValues.map((item) => {
      const fromDate = item.fromDate || "";
      const toDate = item.toDate || "";

      const baseInfo = {
        url: item.url,
        size: item.size,
        name: item.name,
        fromDate,
        toDate,
        status: defaultData.PENDING_STATUS,
        comment: "",
      };

      if (name === "primaryPassportDoc") {
        return {
          ...baseInfo,
          isPrimary: true,
          isSecondary: false,
        };
      }

      if (name === "secondaryPassportDoc") {
        return {
          ...baseInfo,
          isPrimary: false,
          isSecondary: true,
        };
      }

      return baseInfo;
    });

    setFormData((prev) => ({
      ...prev,
      [name]: imageInfoArray,
    }));
  };

  const handleImageCancel = (fieldName, updatedImageUrl) => {
    const newImageUrlArray = updatedImageUrl || formData[fieldName];
    const filteredImageUrlArray = newImageUrlArray.filter((img) => img.url !== Common.FRAME);

    setFormData((prev) => ({
      ...prev,
      [fieldName]: filteredImageUrlArray,
    }));
  };

  const handleSubmit = async () => {
    // Validate form
    const newErrors = {};
    contractualDetailsFields.forEach((field) => {
      if (field.condition && !field.condition(formData)) {
        return;
      }

      // Check if the field is required and empty
      if (field.IsRequired && (!formData[field.id] || formData[field.id].length === 0)) {
        newErrors[field.id] = Constants.FIELD_REQUIRED_ERROR;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    const {
      primaryPassportDoc,
      primaryPassportExpiryDate,
      primaryPassportIssueDate,
      secondaryPassportDoc,
      secondaryPassportExpiryDate,
      secondaryPassportIssueDate,
      seamansBookIssueDate,
      seamansBookExpiryDate,
      drivingLicenseExpiryDate,
      drivingLicenseIssueDate,
      liabilityExpiryDate,
      liabilityIssueDate,
      healthExpiryDate,
      healthIssueDate,
      drivingLicenseNumber,
      primaryPassport,
      employmentType,
      ...rest
    } = formData;
    // Format data back to API structure
    const formattedData = {
      ...rest,
      ...(employmentType
        ? (() => {
            let type = employmentType;
            if (employmentType === Common.EXTERNAL_EMPLOYEE) {
              type = Common.EXTERNAL_EMPLOYEE_API;
            }
            if (employmentType === Common.INTERNAL_EMPLOYEE) {
              type = Common.INTERNAL_EMPLOYEE_API;
            }
            return { employmentType: type };
          })()
        : {}),
      birthDate: formData.birthDate,
      passport: formData.primaryPassport,
      drivingLicence: formData.drivingLicenseNumber
        ? [
            {
              licenseNumber: formData.drivingLicenseNumber,
              issueDate: formData.drivingLicenseIssueDate
                ? moment(formData.drivingLicenseIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
                : "",
              expiryDate: formData.drivingLicenseExpiryDate
                ? moment(formData.drivingLicenseExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
                : "",
            },
          ]
        : [],
      identityProof: [
        ...(formData.primaryPassportDoc?.map((doc) => ({
          name: doc.name,
          size: doc.size,
          url: doc.url,
          status: doc.status || defaultData.PENDING_STATUS,
          comment: doc.comment || "",
          fromDate: formData.primaryPassportIssueDate
            ? moment(formData.primaryPassportIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          toDate: formData.primaryPassportExpiryDate
            ? moment(formData.primaryPassportExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          passport: formData.primaryPassport,
          isPrimary: true,
          isSecondary: false,
        })) || []),
        ...(formData.secondaryPassportDoc?.map((doc) => ({
          name: doc.name,
          size: doc.size,
          url: doc.url,
          status: doc.status || defaultData.PENDING_STATUS,
          comment: doc.comment || "",
          fromDate: formData.secondaryPassportIssueDate
            ? moment(formData.secondaryPassportIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          toDate: formData.secondaryPassportExpiryDate
            ? moment(formData.secondaryPassportExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          passport: formData.secondaryPassport,
          isPrimary: false,
          isSecondary: true,
        })) || []),
      ],
      liabilityInsurance:
        formData.liabilityInsurance?.map((doc) => ({
          name: doc.name,
          size: doc.size,
          url: doc.url,
          status: doc.status || defaultData.PENDING_STATUS,
          comment: doc.comment || "",
          fromDate: formData.liabilityIssueDate
            ? moment(formData.liabilityIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          toDate: formData.liabilityExpiryDate
            ? moment(formData.liabilityExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
        })) || [],
      healthInsurance:
        formData.healthInsurance?.map((doc) => ({
          name: doc.name,
          size: doc.size,
          url: doc.url,
          status: doc.status || defaultData.PENDING_STATUS,
          comment: doc.comment || "",
          fromDate: formData.healthIssueDate
            ? moment(formData.healthIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          toDate: formData.healthExpiryDate
            ? moment(formData.healthExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
        })) || [],
      seamansBook:
        formData.seamansBook?.map((doc) => ({
          name: doc.name,
          size: doc.size,
          url: doc.url,
          status: doc.status || defaultData.PENDING_STATUS,
          comment: doc.comment || "",
          fromDate: formData.seamansBookIssueDate
            ? moment(formData.seamansBookIssueDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
          toDate: formData.seamansBookExpiryDate
            ? moment(formData.seamansBookExpiryDate).format(defaultData.DATABSE_DATE_FORMAT)
            : "",
        })) || [],
      personnelUserId: id || userId,
    };
    const changedFields = {};
    Object.keys(formattedData).forEach((key) => {
      if (JSON.stringify(formattedData[key]) !== JSON.stringify(data[key])) {
        changedFields[key] = formattedData[key];
      }
    });

    const dataBody = {
      body: changedFields,
      id: contractualDetailId,
    };
    const apiToCall = contractualDetailId
      ? updateProfileContractualThunk
      : createProfileContractualThunk;
    setLoading(true);
    const res = await dispatch(apiToCall(dataBody));
    setLoading(false);
    if (res.payload.status === Common.API_STATUS_200) {
      dispatch(
        openSnackbar({
          message: Constants.PROFILE_UPDATED_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      onClose();
    } else if (res.payload.status === Common.API_STATUS_422) {
      const errorData = res.payload.data?.data?.error || [];
      let errorMessage = Constants.SOMETHING_WENT_WRONG;

      if (errorData.length > 0) {
        // Check if the error object contains any keys
        const errorKeys = Object.keys(errorData[0]);
        if (errorKeys.length > 0) {
          // Use the first key's value as the error message
          errorMessage = errorData[0][errorKeys[0]] || Constants.SOMETHING_WENT_WRONG;
        }
      }

      dispatch(
        openSnackbar({
          message: errorMessage,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    const keysToDelete = [
      "primaryPassport",
      "primaryPassportDoc",
      "primaryPassportIssueDate",
      "primaryPassportExpiryDate",
      "secondaryPassport",
      "secondaryPassportDoc",
      "secondaryPassportIssueDate",
      "secondaryPassportExpiryDate",
      "seamansBookIssueDate",
      "seamansBookExpiryDate",
      "drivingLicenseNumber",
      "drivingLicenseIssueDate",
      "drivingLicenseExpiryDate",
      "liabilityIssueDate",
      "liabilityExpiryDate",
      "healthIssueDate",
      "healthExpiryDate",
    ];
    keysToDelete.forEach((key) => delete formattedData[key]);
  };

  const formatFieldValueToArray = (value) => {
    if (!value || value === "") return [];
    return Array.isArray(value) ? value : [value];
  };

  const renderField = (field) => {
    // Skip rendering if condition is not met
    if (field.condition && !field.condition(formData)) {
      return null;
    }

    const fieldValue = formData[field.id] || "";

    switch (field.type) {
      case BackendFrontend.OPTIONS:
        if (field.id === "employmentType") {
          const selectedOption = field.options.find((opt) => opt.id === fieldValue);
          return (
            <ConfigDropdown
              label={field.IsRequired ? `${field.label}*` : field.label}
              name={field.id}
              id={field.id}
              menu={field.options}
              value={selectedOption?.id || ""}
              error={errors[field.id]}
              helperText={errors[field.id]}
              handleChange={(e, value) => handleChange(field.id, value?.id || "")}
            />
          );
        }
        return (
          <ConfigDropdown
            label={field.IsRequired ? `${field.label}*` : field.label}
            name={field.id}
            id={field.id}
            menu={field.options}
            value={fieldValue}
            error={errors[field.id]}
            helperText={errors[field.id]}
            handleChange={(e, value) => handleChange(field.id, value)}
          />
        );

      case BackendFrontend.IMAGES:
        return (
          <Box component="div" sx={{ width: "100%" }}>
            <ImageUpload
              label={field.IsRequired ? `${field.label}*` : field.label}
              name={field.id}
              onImageUpload={(imageValues) => {
                handleImageChange(field.id, imageValues);
              }}
              onImageCancel={(updatedImageUrl) => {
                handleImageCancel(field.id, updatedImageUrl);
              }}
              type="profile"
              formats={pattern.IMAGE_TYPES}
              acceptType="image/*, application/pdf"
              maxImageCount={
                field.id === "primaryPassportDoc" || field.id === "secondaryPassportDoc" ? 2 : 1
              }
              error={Boolean(errors[field.id])}
              helperText={errors[field.id]}
              data={formatFieldValueToArray(fieldValue)}
              showDateFields={
                field.id === "seamansBook" ||
                field.id === "primaryPassportDoc" ||
                field.id === "secondaryPassportDoc" ||
                field.id === "liabilityInsurance" ||
                field.id === "healthInsurance"
              }
              isDocument={field.id === "seamansBook" || field.id.includes("PassportDoc")}
            />
          </Box>
        );

      case BackendFrontend.DATE: {
        // Helper function to get date constraints
        const getDateConstraints = (fieldId) => {
          const constraints = {};

          // Birth date cannot be in the future
          if (fieldId === "birthDate") {
            constraints.max = moment().format(defaultData.DATABSE_DATE_FORMAT);
          }

          // Issue dates max should be set to corresponding expiry dates
          if (fieldId === "primaryPassportIssueDate" && formData.primaryPassportExpiryDate) {
            constraints.max = formData.primaryPassportExpiryDate;
          }

          if (fieldId === "secondaryPassportIssueDate" && formData.secondaryPassportExpiryDate) {
            constraints.max = formData.secondaryPassportExpiryDate;
          }

          if (fieldId === "seamansBookIssueDate" && formData.seamansBookExpiryDate) {
            constraints.max = formData.seamansBookExpiryDate;
          }

          if (fieldId === "drivingLicenseIssueDate" && formData.drivingLicenseExpiryDate) {
            constraints.max = formData.drivingLicenseExpiryDate;
          }

          if (fieldId === "liabilityIssueDate" && formData.liabilityExpiryDate) {
            constraints.max = formData.liabilityExpiryDate;
          }

          if (fieldId === "healthIssueDate" && formData.healthExpiryDate) {
            constraints.max = formData.healthExpiryDate;
          }

          // Expiry dates min should be set to corresponding issue dates
          if (fieldId === "primaryPassportExpiryDate" && formData.primaryPassportIssueDate) {
            constraints.min = formData.primaryPassportIssueDate;
          }

          if (fieldId === "secondaryPassportExpiryDate" && formData.secondaryPassportIssueDate) {
            constraints.min = formData.secondaryPassportIssueDate;
          }

          if (fieldId === "seamansBookExpiryDate" && formData.seamansBookIssueDate) {
            constraints.min = formData.seamansBookIssueDate;
          }

          if (fieldId === "drivingLicenseExpiryDate" && formData.drivingLicenseIssueDate) {
            constraints.min = formData.drivingLicenseIssueDate;
          }

          if (fieldId === "liabilityExpiryDate" && formData.liabilityIssueDate) {
            constraints.min = formData.liabilityIssueDate;
          }

          if (fieldId === "healthExpiryDate" && formData.healthIssueDate) {
            constraints.min = formData.healthIssueDate;
          }

          return constraints;
        };

        const dateConstraints = getDateConstraints(field.id);

        return (
          <FTextField
            label={field.IsRequired ? `${field.label}*` : field.label}
            placeholder={field.hint}
            name={field.id}
            id={field.id}
            type="date"
            value={fieldValue ? moment(fieldValue).format(defaultData.DATABSE_DATE_FORMAT) : ""}
            error={Boolean(errors[field.id])}
            helperText={errors[field.id]}
            handleChange={(e) => handleChange(field.id, e.target.value)}
            InputProps={{
              inputProps: {
                ...dateConstraints,
                style: { textTransform: "uppercase" },
              },
            }}
          />
        );
      }

      default:
        return (
          <FTextField
            label={field.IsRequired ? `${field.label}*` : field.label}
            placeholder={field.hint}
            name={field.id}
            id={field.id}
            type="text"
            value={fieldValue}
            error={Boolean(errors[field.id])}
            helperText={errors[field.id]}
            handleChange={(e) => handleChange(field.id, e.target.value)}
          />
        );
    }
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={handleDrawerClose}
      PaperProps={{
        sx: { width: "90%" },
      }}
    >
      <MDBox
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Header */}
        <MDBox
          px={3}
          py={2}
          display="flex"
          justifyContent="start"
          alignItems="center"
          borderBottom="1px solid #E0E6F5"
        >
          <IconButton onClick={handleDrawerClose} size="small">
            {Icons.CLOSE}
          </IconButton>
          <MDTypography variant="h5" marginLeft={2}>
            Edit Contractual Details
          </MDTypography>
        </MDBox>

        {/* Form */}
        <Box
          component="form"
          noValidate
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          sx={{
            flexGrow: 1,
            overflow: "auto",
            p: 3,
          }}
        >
          <Grid container spacing={2}>
            {contractualDetailsFields.map((field) => {
              // Check if the field has a condition and if it does not match, skip rendering
              if (field.condition && !field.condition(formData)) {
                return null;
              }

              return (
                <Grid item {...field.gridProps} key={field.id}>
                  {renderField(field)}
                </Grid>
              );
            })}
          </Grid>
        </Box>

        {/* Footer */}
        <MDBox
          px={3}
          py={2}
          display="flex"
          justifyContent="flex-end"
          gap={2}
          borderTop="1px solid #E0E6F5"
        >
          <MDButton variant="outlined" color="secondary" onClick={handleDrawerClose}>
            Cancel
          </MDButton>
          <MDButton
            variant="contained"
            color="info"
            style={{ boxShadow: "none", textTransform: "none" }}
            disabled={loading}
            onClick={handleSubmit}
          >
            {loading ? ButtonTitles.UPDATE_LOADING : "Save Changes"}
          </MDButton>
        </MDBox>
      </MDBox>
    </Drawer>
  );
}

ContractualDetailsDrawer.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.shape({
    passport: PropTypes.string,
    identityProof: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        url: PropTypes.string,
        size: PropTypes.number,
        fromDate: PropTypes.string,
        toDate: PropTypes.string,
        isPrimary: PropTypes.bool,
        isSecondary: PropTypes.bool,
      })
    ),
    nationalIdentificationNumber: PropTypes.string,
    drivingLicence: PropTypes.arrayOf(
      PropTypes.shape({
        licenseNumber: PropTypes.string,
        issueDate: PropTypes.string,
        expiryDate: PropTypes.string,
      })
    ),
    seamansBook: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        url: PropTypes.string,
        size: PropTypes.number,
        fromDate: PropTypes.string,
        toDate: PropTypes.string,
      })
    ),
    birthDate: PropTypes.string,
    birthPlace: PropTypes.string,
    employmentType: PropTypes.string,
    liabilityInsurance: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        url: PropTypes.string,
        size: PropTypes.number,
        fromDate: PropTypes.string,
        toDate: PropTypes.string,
      })
    ),
    healthInsurance: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        url: PropTypes.string,
        size: PropTypes.number,
        fromDate: PropTypes.string,
        toDate: PropTypes.string,
      })
    ),
    companyName: PropTypes.string,
    companyRegistrationNumber: PropTypes.string,
    companyVATNumber: PropTypes.string,
    companyAddress: PropTypes.string,
    bankName: PropTypes.string,
    accountHolderName: PropTypes.string,
    bankAccount: PropTypes.string,
    bicSwift: PropTypes.string,
  }),
  contractualDetailId: PropTypes.string,
  userId: PropTypes.string,
};

ContractualDetailsDrawer.defaultProps = {
  data: {},
  contractualDetailId: "",
  userId: "",
};

export default ContractualDetailsDrawer;

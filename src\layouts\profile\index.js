import { useEffect, useState } from "react";

// @mui material components
import Grid from "@mui/material/Grid";
import { Tab, Tabs, CircularProgress, Icon, Card, Tooltip, IconButton } from "@mui/material";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDAvatar from "components/MDAvatar";
import MDTypography from "components/MDTypography";
import FontComponent from "components/Responsive/fonts";

// Material Dashboard 2 React example components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import pxToRem from "assets/theme/functions/pxToRem";
import Personaldetails from "examples/modal/UpdateProfile/PersonalDetails";

// Images imports from assets
import bgImage from "assets/images/Profilebg.png";

// third party libraries
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";

// Redux Components imports
import ProfileThunk from "redux/Thunks/Profile";
import { UserListbyIdThunk } from "redux/Thunks/UserManagement";
import uploadImageThunk from "redux/Thunks/ImageUpload";
import getCertificateTypeOptions from "redux/Thunks/DropdownOptions";
import { updateAdminProfileThunk } from "redux/Thunks/SuperAdmin";
import { openSnackbar } from "redux/Slice/Notification";
import RoleListThunk from "redux/Thunks/Role";
import getProfileFunctions from "redux/Thunks/ProfileFunctions";

// Utils Components imports
import Constants, { Icons, defaultData, Common } from "utils/Constants";

// Small Component related to this page
import UserRatings from "./userRatings";
import PersonalDetailsTab from "./profileTabs/PersonalDetailsTab";
import CertificateTab from "./profileTabs/CertificateTab";
import ContractualDetailsTab from "./profileTabs/ContractualDetailsTab";
import MedicalTab from "./profileTabs/MedicalTab";
import GdprTab from "./profileTabs/GdprTab";
import OrganizationProfileTab from "./profileTabs/OrganizationProfileTab";
import Logbook from "./profileTabs/Logbook";

function Overview({ isUserShow, userId }) {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const mongooseID = "_id";
  const fontSize = FontComponent({ sizes: {} });
  const ConfigData = useSelector((state) => state.config);
  const permission = ConfigData?.screens[11]?.screensInfo?.agreement;

  //  Personal Basic License future use case
  // const personnelBasicPermission = ConfigData?.screens?.find(
  //   (screen) => screen.id === defaultData.PERSONNEL_BASIC_SCREEN_ID
  // )?.screensInfo?.agreement;
  // State variables
  const [profileData, setProfileData] = useState([]);
  const [roleOptions, setRoleOptions] = useState([]);
  const [profileFunctionOptions, setProfileFunctionOptions] = useState([]);
  const [update, setUpdate] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [value, setValue] = useState(0);

  const isUserWebAccess = profileData[0]?.role?.accessType === defaultData.WEB_ACCESSTYPE;

  // Common user tab styles.
  const commonUserTabStyles = {
    display: "flex",
    justifyContent: "flex-start",
    paddingLeft: "12px",
    fontSize: pxToRem(14),
    borderRadius: 0,
    height: "34px!important",
    textTransform: "capitalize",
  };

  // Function to handle dynamic styles for user view tabs.
  const dynamicUserTabStyles = (tabIndex) => ({
    fontWeight: value === tabIndex ? "600" : null,
    backgroundColor: value === tabIndex ? "#F6F7FF" : "#ffffff",
    borderLeft: `2px solid ${value === tabIndex ? "#191A51" : "#ffffff"}`,
  });

  // API Calls to fetch Data for the profile page.
  const displayErrorMessage = () => {
    dispatch(openSnackbar({ message: Constants.SOMETHING_WENT_WRONG, notificationType: "error" }));
  };

  const fetchAdditionalDataFunc = async () => {
    try {
      const [roleRes, profileFunctionRes] = await Promise.all([
        dispatch(RoleListThunk()),
        dispatch(getProfileFunctions()),
      ]);

      const filteredRoles = roleRes?.payload?.data?.data?.rolesData?.filter(
        (role) => ![defaultData.ADMIN_ROLE, defaultData.SUPER_ADMIN_ROLE].includes(role?.title)
      );

      const activeFunctions = profileFunctionRes?.payload?.data?.data?.filter(
        (func) => func.isActive === true
      );

      setRoleOptions(filteredRoles);
      setProfileFunctionOptions(activeFunctions);
    } catch (error) {
      displayErrorMessage();
    }
  };

  const getuserListByIdFunc = async () => {
    try {
      const res = await dispatch(UserListbyIdThunk(id || userId));

      if (res.payload?.status === 200) {
        const profile = res?.payload?.data?.data;
        setProfileData([profile]);

        const roleTitle = profile?.role?.title?.toLowerCase();
        if (![defaultData.ADMIN_ROLE, defaultData.SUPER_ADMIN_ROLE].includes(roleTitle)) {
          await fetchAdditionalDataFunc();
        }
      } else {
        displayErrorMessage();
        navigate("/client/personnel");
      }
    } catch (error) {
      displayErrorMessage();
      navigate("/client/personnel");
    }
  };

  const fetchAdminDataFunc = async () => {
    try {
      const res = await dispatch(ProfileThunk());
      setProfileData([res?.payload?.data]);
      setRoleOptions([]);
      setProfileFunctionOptions([]);
    } catch (error) {
      displayErrorMessage();
    }
  };

  const getAllCertificateTypeFunc = async () => {
    try {
      const res = await dispatch(getCertificateTypeOptions());
      if (res?.payload?.status !== Common.API_STATUS_200) {
        dispatch(
          openSnackbar({
            message: Constants.ERROR_FETCHING_USER_CERTIFICATE,
            notificationType: "error",
          })
        );
      }
    } catch (error) {
      displayErrorMessage();
    }
  };
  // API Calls to fetch Data for the profile page ends here.

  const handleProfileImageChange = async (event) => {
    const file = event.target.files[0];
    const type = "Profile_Photos";

    const res = await dispatch(uploadImageThunk({ file, type }));
    if (res.error === undefined) {
      const b = {
        body: { profileImage: res.payload.data.iconUrl },
        id: profileData[0]?.[mongooseID],
      };

      const res1 = await dispatch(updateAdminProfileThunk(b));
      if (res1.error === undefined) {
        dispatch(
          openSnackbar({ message: Constants.PROFILE_IMAGE_UPDATE, notificationType: "success" })
        );
        setUpdate((prev) => !prev);
      }
    } else {
      displayErrorMessage();
    }
  };

  const renderUserView = () => (
    <MDBox>
      {profileData?.length > 0 ? (
        <MDBox position="relative">
          {isUserShow && (
            <MDBox
              display="flex"
              alignItems="center"
              position="relative"
              minHeight="14.75rem"
              ml={-4}
              mr={-3.5}
              sx={{
                backgroundImage: ({ functions: { rgba, linearGradient } }) =>
                  `${linearGradient(rgba("#FFC9B0", 0.6), rgba("#FFC9B0", 0.6))}, url(${bgImage})`,
                backgroundSize: "cover",
                backgroundPosition: "50%",
                overflow: "hidden",
              }}
            />
          )}

          <Card
            sx={{
              position: "relative",
              mt: isUserShow ? -24 : 0,
              py: 6,
              height: "100%",
              boxShadow: isUserShow ? 1 : 0,
              mb: "20px",
            }}
          >
            <Grid
              container
              spacing={3}
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              alignContent={{ xs: "center", md: "flex-start", lg: "flex-start" }}
            >
              <Grid item xs={12} sm={12} md={6} lg={3}>
                {profileData[0]?.profileImage ? (
                  <MDAvatar
                    src={profileData[0]?.profileImage}
                    size="xxl"
                    shadow="sm"
                    sx={{ marginLeft: "35px" }}
                  />
                ) : (
                  <MDAvatar
                    size="xxl"
                    shadow="sm"
                    sx={{ marginLeft: "35px", backgroundColor: "#191A51" }}
                  >
                    <MDTypography color="light" sx={{ fontSize: pxToRem(50), fontWeight: "700" }}>
                      {profileData[0]?.callingName
                        ? profileData[0]?.callingName.charAt(0)?.toUpperCase()
                        : profileData[0]?.firstName.charAt(0)?.toUpperCase()}
                    </MDTypography>
                  </MDAvatar>
                )}

                {profileData[0]?.role?.title.toLowerCase() === defaultData.ADMIN_ROLE ? (
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item>
                      <Tooltip title="Change profile image">
                        <IconButton
                          aria-label="change-profile-image"
                          component="label"
                          htmlFor="profile-image-input"
                        >
                          <Icon
                            fontSize="medium"
                            display="flex"
                            sx={{
                              ml: 14,
                              mt: -7,
                              cursor: "pointer",
                              backgroundColor: "#f5f5f5",
                              borderRadius: "50%",
                            }}
                          >
                            {Icons.EDIT}
                          </Icon>
                          <input
                            type="file"
                            id="profile-image-input"
                            accept="image/*"
                            hidden
                            onChange={handleProfileImageChange}
                          />
                        </IconButton>
                      </Tooltip>
                    </Grid>
                  </Grid>
                ) : null}
              </Grid>
              <Grid item xs={12} sm={12} md={6} lg={3}>
                {profileData?.map((element) => {
                  const fullName = `${element?.callingName || element?.firstName} ${
                    element?.lastName
                  }`;
                  const profileFunction = element?.profileFunction?.name || element?.role?.title;
                  const email = element?.email;

                  return (
                    <MDBox
                      key={element?.[Constants.mongooseID]}
                      height="100%"
                      mt={0.5}
                      lineHeight={1}
                    >
                      {/* Name with tooltip if needed */}
                      <MDBox display="flex" justifyContent="center" alignItems="center">
                        {fullName?.length > defaultData.MEDIUM_CONTENT_LENGTH ? (
                          <Tooltip title={fullName}>
                            <MDTypography
                              sx={{
                                fontSize,
                                fontWeight: "700",
                                color: "#191D31",
                                textTransform: "capitalize",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                maxWidth: "90%",
                              }}
                            >
                              {fullName}
                            </MDTypography>
                          </Tooltip>
                        ) : (
                          <MDTypography
                            sx={{
                              fontSize,
                              fontWeight: "700",
                              color: "#191D31",
                              textTransform: "capitalize",
                            }}
                          >
                            {fullName}
                          </MDTypography>
                        )}
                      </MDBox>

                      {/* Profile Function with tooltip if needed */}
                      <MDBox display="flex" justifyContent="center" alignItems="center">
                        {profileFunction?.length > defaultData.MEDIUM_CONTENT_LENGTH ? (
                          <Tooltip title={profileFunction}>
                            <MDTypography
                              sx={{
                                fontSize,
                                fontWeight: "500",
                                color: "#191D31",
                                textTransform: "capitalize",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                maxWidth: "90%",
                              }}
                            >
                              {profileFunction}
                            </MDTypography>
                          </Tooltip>
                        ) : (
                          <MDTypography
                            sx={{
                              fontSize,
                              fontWeight: "500",
                              color: "#191D31",
                              textTransform: "capitalize",
                            }}
                          >
                            {profileFunction}
                          </MDTypography>
                        )}
                      </MDBox>

                      {/* Email with tooltip always (already truncated) */}
                      <MDBox display="flex" justifyContent="center" alignItems="center">
                        <Tooltip title={email}>
                          <MDTypography
                            sx={{
                              fontSize,
                              fontWeight: "600",
                              marginLeft: "25px",
                              color: "#191D31",
                              wordBreak: "break-all",
                            }}
                          >
                            {email?.length > defaultData.MEDIUM_CONTENT_LENGTH
                              ? `${email.slice(0, defaultData.MEDIUM_CONTENT_LENGTH)}...`
                              : email}
                          </MDTypography>
                        </Tooltip>

                        <Icon
                          fontSize="medium"
                          onClick={() => setOpenEdit(true)}
                          sx={{ ml: 2, cursor: "pointer", display: !permission?.update && "none" }}
                        >
                          {Icons.EDIT}
                        </Icon>
                      </MDBox>
                    </MDBox>
                  );
                })}
              </Grid>
            </Grid>

            <MDBox mt={5} mb={3}>
              <Grid
                container
                rowSpacing={1}
                columnSpacing={{ xs: 1, sm: 1, md: 3 }}
                display="flex"
                flexWrap="wrap"
              >
                <Grid
                  item
                  xs={12}
                  md={6}
                  xl={3}
                  sx={{
                    display: "flex",
                    height:
                      profileData[0]?.role?.accessType === defaultData.MOBILE_ACCESSTYPE ||
                      profileData[0]?.role?.accessType === defaultData.BOTH_ACCESSTYPE
                        ? "380px"
                        : "120px",
                  }}
                >
                  <Tabs
                    value={value}
                    onChange={(e, val) => setValue(val)}
                    orientation="vertical"
                    sx={{
                      backgroundColor: "#ffffff",
                      width: "100%!important",
                      justifyContent: "flex-start",
                    }}
                  >
                    <Tab
                      label="Personal Details"
                      sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(0) }}
                    />

                    {isUserWebAccess ? null : (
                      <Tab
                        label="Certificates"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(1) }}
                      />
                    )}

                    {isUserWebAccess ? null : (
                      <Tab
                        label="Contractual Details"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(2) }}
                      />
                    )}

                    {isUserWebAccess ? null : (
                      <Tab
                        label="Medical"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(3) }}
                      />
                    )}

                    {isUserWebAccess ? null : (
                      <Tab
                        label="GDPR"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(4) }}
                      />
                    )}

                    {isUserWebAccess ? null : (
                      <Tab
                        label="Ratings"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(5) }}
                      />
                    )}
                    {isUserWebAccess ? null : (
                      <Tab
                        label="Logbook"
                        sx={{ ...commonUserTabStyles, ...dynamicUserTabStyles(6) }}
                      />
                    )}

                    <Tab
                      label="Organization Profile"
                      sx={{
                        ...commonUserTabStyles,
                        ...dynamicUserTabStyles(
                          profileData[0]?.role?.title.toLowerCase() === defaultData.ADMIN_ROLE
                            ? 1
                            : 7
                        ),
                      }}
                    />
                  </Tabs>
                </Grid>

                <Grid
                  xs
                  mt={{ xs: 0, sm: 0, md: -30, lg: -30 }}
                  sx={{ borderLeft: "1px solid #E0E6F5" }}
                >
                  {value ===
                    (profileData[0]?.role?.title.toLowerCase() === defaultData.ADMIN_ROLE
                      ? 1
                      : 7) && (
                    <OrganizationProfileTab tabValue={value} profile={profileData?.[0]} />
                  )}

                  {value === 0 && (
                    <PersonalDetailsTab
                      tabValue={value}
                      profile={profileData?.[0]}
                      onUpdate={() => setUpdate((prev) => !prev)}
                    />
                  )}

                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 1 && (
                        <CertificateTab
                          userId={id || userId}
                          permission={permission}
                          profile={profileData?.[0]}
                          setUpdate={setUpdate}
                        />
                      )}

                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 2 && (
                        <ContractualDetailsTab
                          profile={profileData?.[0]}
                          tabValue={value}
                          onUpdate={() => setUpdate((prev) => !prev)}
                        />
                      )}

                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 3 && <MedicalTab profile={profileData?.[0]} />}

                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 4 && <GdprTab tabValue={value} profile={profileData?.[0]} />}

                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 5 && <UserRatings userId={userId || id} permission={permission} />}
                  {profileData[0]?.role.accessType === defaultData.WEB_ACCESSTYPE
                    ? null
                    : value === 6 && <Logbook userId={userId || id} permission={permission} />}
                </Grid>
              </Grid>
            </MDBox>
          </Card>
        </MDBox>
      ) : (
        <MDBox py={5} mt={30} display="flex" justifyContent="center" alignItems="center">
          <CircularProgress color="info" />
        </MDBox>
      )}
      <Personaldetails
        openPersonal={openEdit}
        setOpenPersonal={setOpenEdit}
        title="Update Profile"
        pdata={profileData}
        setUpdate={setUpdate}
        roleOptions={roleOptions}
        profileFunctionOptions={profileFunctionOptions}
      />
    </MDBox>
  );

  useEffect(() => {
    if (id || userId) {
      getuserListByIdFunc();
    } else {
      fetchAdminDataFunc();
    }
    getAllCertificateTypeFunc();
  }, [id, userId, update]);

  return isUserShow ? (
    <DashboardLayout>
      <DashboardNavbar />
      {renderUserView()}
    </DashboardLayout>
  ) : (
    renderUserView()
  );
}

Overview.defaultProps = {
  isUserShow: true,
  userId: "",
};
Overview.propTypes = {
  isUserShow: PropTypes.bool,
  userId: PropTypes.string,
};
export default Overview;

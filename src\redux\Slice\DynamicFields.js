import { createSlice } from "@reduxjs/toolkit";
import { resetStateThunk } from "redux/Thunks/Authentication";
import { getAllFieldsThunk } from "redux/Thunks/Configuration";
import Constants from "utils/Constants";

const initialState = {
  loading: Constants.IDLE,
  list: {
    dyamicField: [],
    staticField: [],
  },
};

export const dynamicFieldSlice = createSlice({
  name: "dynamicfields",
  initialState,
  reducers: {
    loadDynamicField: (state, action) => {
      state.list.dyamicField.push(...action.payload);
    },
  },

  extraReducers: {
    [getAllFieldsThunk.pending]: (state) => {
      state.loading = Constants.PENDING;
    },
    [getAllFieldsThunk.fulfilled]: (state, action) => {
      state.loading = Constants.FULFILLED;
      const dynamicListData = action.payload.data.dyamicField;
      state.list.dyamicField = dynamicListData.reverse();
      state.list.staticField = action.payload.data.staticField;
    },
    [getAllFieldsThunk.rejected]: (state) => {
      state.loading = Constants.REJECTED;
    },
    [resetStateThunk.fulfilled]: (state) => {
      state.loading = Constants.IDLE;
      state.list.dyamicField = [];
      state.list.staticField = [];
    },
  },
});

export const { loadDynamicField } = dynamicFieldSlice.actions;

export default dynamicFieldSlice.reducer;

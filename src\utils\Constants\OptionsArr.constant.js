const mongooseID = "_id";

const DropdownOptions = {
  // Project Status
  PROJECT_STATUS: [
    { label: "Open", value: "open" },
    { label: "Completed", value: "completed" },
    { label: "Closed", value: "closed" },
  ],

  // Project Docuemnt type options at setup project
  PROJECT_DOCUMENT_TYPES_AT_SETUP_PROJECT: [
    { title: "Procedure", [mongooseID]: "procedure" },
    { title: "Management of Change", [mongooseID]: "management_of_change" },
    { title: "Safety Update", [mongooseID]: "safety_update" },
    { title: "Quality Update", [mongooseID]: "quality_update" },
    { title: "Safety Notification", [mongooseID]: "safety_notification" },
  ],

  PROJECT_STATUS_OPTIONS: [
    { [mongooseID]: "open", title: "Open" },
    { [mongooseID]: "closed", title: "Closed" },
    { [mongooseID]: "completed", title: "Completed" },
  ],
  DEFAULT_SELECTED_PROJECT_STATUS_LIST: ["open", "closed", "completed"],

  ALL_OPTION_OBJ: { label: "All", value: "all" },

  // User Certificate Tab Options
  USER_CERTIFICATE_TAB: ["Approved", "Expired", "Pending", "Rejected", "In Active", "All"],

  // Role Management Access Type options
  ROLE_MANAGEMENT_ACCESS_TYPE: [
    { label: "Both", value: "both" },
    { label: "Web", value: "web" },
    { label: "Mobile", value: "mobile" },
  ],
  ROLE_MANAGEMENT_ASSIGNED_PROJECTS: [
    { label: "Own Projects", value: "own" },
    { label: "All Projects", value: "all" },
  ],
  ROLE_MANAGEMENT_FILTER_ACCESS_TYPE_OPTIONS: [
    { [mongooseID]: "All", title: "All" },
    { [mongooseID]: "both", title: "Both" },
    { [mongooseID]: "web", title: "Web" },
    { [mongooseID]: "mobile", title: "Mobile" },
  ],

  // User Certificate Tab Options
  DPR_TABS: [
    "Progress",
    "QHSE",
    "Applicable Documents",
    "Time Analysis",
    "Personnel List",
    "Equipment",
    "Comments and Signatures",
  ],
};

export default DropdownOptions;

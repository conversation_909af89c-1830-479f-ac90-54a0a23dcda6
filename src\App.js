import { useState, useEffect } from "react";

// react-router components
import { Routes, Route, Navigate, useLocation } from "react-router-dom";

// @mui material components
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";

// Material Dashboard 2 React example components
import Sidenav from "examples/Sidenav";
import Configurator from "examples/Configurator";

// Material Dashboard 2 React themes
import theme from "assets/theme";

// Material Dashboard 2 React Dark Mode themes
import themeDark from "assets/theme-dark";

// Material Dashboard 2 React routes
import adminRoutes, { superAdminRoute, authenticationRoute } from "routes";

// Material Dashboard 2 React contexts
import { useMaterialUIController } from "context";

// Images
import brandWhite from "assets/images/logo-ct.png";
import brandDark from "assets/images/logo-ct-dark.png";

// Sessions
import Session from "utils/Sessions";

// JWT decode
import jwtDecode from "jwt-decode";
import { useDispatch, useSelector } from "react-redux";

// Constants
import { defaultData } from "utils/Constants";
import { updateRole } from "redux/Slice/Authentication";

export default function App() {
  const [controller] = useMaterialUIController();
  const { layout, sidenavColor, transparentSidenav, whiteSidenav, darkMode } = controller;
  const [role, setRole] = useState("");
  const { pathname } = useLocation();
  const licensePermissions = useSelector((state) => state.License);
  const configState = useSelector((state) => state.config);
  const [isSuperAdminViewingAdminPanel, setIsSuperAdminViewingAdminPanel] = useState(false);
  const dispatch = useDispatch();

  // updating role based on token  and Setting page scroll to 0 when changing the route
  useEffect(() => {
    if (pathname.includes("authentication/reset-password")) {
      Session.setClear();
      setRole(defaultData.UNAUTHORIZED_ROLE);
    } else if (Session.userToken) {
      const token = jwtDecode(Session.userToken);
      setRole(token.role);
      Session.setUserRole(token.role);
      setIsSuperAdminViewingAdminPanel(Session.isSuperAdminViewingAdminPanel);
      dispatch(updateRole(token.role));
    } else {
      setRole(defaultData.UNAUTHORIZED_ROLE);
    }
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
  }, [pathname, Session.userToken, Session.isSuperAdminViewingAdminPanel]);

  // First, create a function to get filtered routes without the Route components
  const getFilteredRoutes = (allRoutes) => {
    if (
      (role !== defaultData.SUPER_ADMIN_ROLE ||
        (role === defaultData.SUPER_ADMIN_ROLE && isSuperAdminViewingAdminPanel)) &&
      (licensePermissions.permissions.length > 0 ||
        licensePermissions.ownerLicenseLoading === "fullfilled")
    ) {
      return allRoutes.filter((route) => {
        // Check if the route has a corresponding screen in config
        const screenConfig = configState.screens?.find((screen) => {
          // Check for direct match with route key
          if (screen.id === route.roleId) {
            return true;
          }
          // Check for name match with route name
          if (route.name && screen.name && screen.name.toLowerCase() === route.name.toLowerCase()) {
            return true;
          }
          return false;
        });

        // If screen exists in config, check for read access
        if (screenConfig?.screensInfo?.agreement) {
          const hasReadAccess = screenConfig.screensInfo.agreement.read;
          if (!hasReadAccess) {
            return false; // Hide route if no read access
          }
        }

        // For parent routes, check parentRoleId read access
        if (route.parentRoleId && Array.isArray(route.parentRoleId)) {
          // Check if any of the parentRoleIds have read access
          const hasParentReadAccess = route.parentRoleId.some((parentId) => {
            const parentScreenConfig = configState.screens?.find(
              (screen) => screen.id === parentId
            );
            if (parentScreenConfig?.screensInfo?.agreement) {
              return parentScreenConfig.screensInfo.agreement.read;
            }
            return false;
          });

          if (!hasParentReadAccess) {
            return false; // Hide parent route if none of the parentRoleIds have read access
          }
        }

        // Special handling for QHSE Management
        if (route.name === "QHSE Management") {
          // Check if user has QHSE Management license
          const hasQHSELicense = licensePermissions.permissions.some((permission) =>
            route.license.some(
              (license) => license.toLowerCase() === permission.licence.name.toLowerCase()
            )
          );

          if (!hasQHSELicense) {
            return false;
          }
        }

        // Special handling for QHSE Cards submenu
        if (route.name === "QHSE Cards") {
          // Check if any of the card types have read access
          const hasCardReadAccess = route.roleId.some((cardId) => {
            const cardScreenConfig = configState.screens?.find((screen) => screen.id === cardId);
            return cardScreenConfig?.screensInfo?.agreement?.read;
          });

          if (!hasCardReadAccess) {
            return false;
          }
        }

        // Continue with existing license checks
        if (route.license.length > 0 && !route.extraRoute) {
          return licensePermissions.permissions.some((permission) =>
            route.license.some(
              (license) => license.toLowerCase() === permission.licence.name.toLowerCase()
            )
          );
        }

        if (route.license.length === 0 && route.permissions.length > 0) {
          return licensePermissions.permissions.some((permission) =>
            route.permissions.some(
              (permissionName) =>
                permissionName.toLowerCase() === permission.permission.name.toLowerCase()
            )
          );
        }

        if (route.requiredRole.length > 0 && route.requiredRole.includes(role)) {
          return true;
        }

        if (
          route.license.length === 0 &&
          route.permissions.length === 0 &&
          (route?.requiredRole ? route?.requiredRole?.length === 0 : true)
        ) {
          return true;
        }

        return false;
      });
    }
    return allRoutes;
  };

  // Modify the existing getRoutes function to use getFilteredRoutes
  const getRoutes = (allRoutes) => {
    const filteredRoutes = getFilteredRoutes(allRoutes);
    return filteredRoutes.map((route) => {
      if (route.collapse) {
        return getRoutes(route.collapse);
      }

      if (route.route) {
        return <Route exact path={route.route} element={route.component} key={route.roleId} />;
      }

      return null;
    });
  };

  if (role === "") {
    return null;
  }

  return (
    <ThemeProvider theme={darkMode ? themeDark : theme}>
      <CssBaseline />
      {layout === "dashboard" && role !== "" && role !== defaultData.UNAUTHORIZED_ROLE && (
        <>
          <Sidenav
            color={sidenavColor}
            brand={(transparentSidenav && !darkMode) || whiteSidenav ? brandDark : brandWhite}
            brandName="Reynard"
            routes={getFilteredRoutes(
              (role !== defaultData.SUPER_ADMIN_ROLE && adminRoutes) ||
                (role === defaultData.SUPER_ADMIN_ROLE &&
                  !isSuperAdminViewingAdminPanel &&
                  superAdminRoute) ||
                (role === defaultData.SUPER_ADMIN_ROLE &&
                  isSuperAdminViewingAdminPanel &&
                  adminRoutes)
            )}
            role={role}
          />
          <Configurator />
        </>
      )}
      {layout === "vr" && <Configurator />}
      <Routes>
        {getRoutes(
          (role !== defaultData.SUPER_ADMIN_ROLE &&
            role !== defaultData.UNAUTHORIZED_ROLE &&
            adminRoutes) ||
            (role === defaultData.SUPER_ADMIN_ROLE &&
              !isSuperAdminViewingAdminPanel &&
              superAdminRoute) ||
            (role === defaultData.SUPER_ADMIN_ROLE &&
              isSuperAdminViewingAdminPanel &&
              adminRoutes) ||
            (role === defaultData.UNAUTHORIZED_ROLE && authenticationRoute)
        )}
        <Route
          path="*"
          element={
            <Navigate
              to={
                pathname.includes("qhse")
                  ? "client/qhse-cards"
                  : (role !== defaultData.SUPER_ADMIN_ROLE &&
                      role !== defaultData.UNAUTHORIZED_ROLE &&
                      "client/setting") ||
                    (role === defaultData.SUPER_ADMIN_ROLE &&
                      !isSuperAdminViewingAdminPanel &&
                      "admin/home") ||
                    (role === defaultData.SUPER_ADMIN_ROLE &&
                      isSuperAdminViewingAdminPanel &&
                      "client/setting") ||
                    (role === defaultData.UNAUTHORIZED_ROLE && "/authentication/sign-in")
              }
            />
          }
        />
      </Routes>
    </ThemeProvider>
  );
}

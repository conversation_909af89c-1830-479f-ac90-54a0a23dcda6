import React, { useMemo } from "react";

// 3rd Party libraries
import { IconButton } from "@mui/material";

// Components
import Author from "components/Table/Author";
import MDBox from "components/MDBox";
import CustomImage from "components/Table/GroupImage";
import Status from "components/Table/Status";

// Utils
import Constants, { Icons, Colors, Common } from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";

export default function ProjectOrderHistoryDetailsData({
  dataList = [],
  handleViewDetailsClick,
  openWarehouseCommentModal,
  openRejectCommentModal,
}) {
  const projectHistoryByIdRows = useMemo(() => {
    if (dataList.length === 0) return [];

    return dataList.map((item, index) => ({
      srNo: <Author name={index + 1} />,
      equipmentType: (
        <MDBox display="flex" justifyContent="start" alignItems="center">
          <CustomImage item={item?.equipmentTypeImage?.url} width={30} height={30} />
          <MDBox
            style={{
              flex: 1,
              width: "100%",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            <Author name={item?.typeName} />
          </MDBox>
          {item?.isTemporary && (
            <MDBox>
              <IconButton aria-label="fingerprint">{Icons.CLOCK}</IconButton>
            </MDBox>
          )}
        </MDBox>
      ),
      approvedRequestedQuantity: <Author name={item?.pmRequestedQuantity} />,
      warehouseApprovedQuantity: <Author name={item.wmApprovedQuantity} />,
      status: (
        <MDBox display="flex" flexDirection="row" justifyContent="flex-start" alignItems="center">
          {item?.status === Constants.STATUS_REJECTED &&
            item?.remark?.length > 0 &&
            item?.remark[0]?.comments !== null &&
            item?.remark[0]?.comments !== "" && (
              <MDBox
                bgColor={Colors.LIGHT_RED}
                // mb={1}
                borderRadius={pxToRem(16)}
                sx={{ width: pxToRem(30), height: pxToRem(30) }}
                display="flex"
                justifyContent="center"
                align="center"
              >
                <IconButton
                  aria-label="fingerprint"
                  onClick={() => openRejectCommentModal(Common.USER_TEXT, item?.remark[0])}
                >
                  {Icons.COMMENT_RED}
                </IconButton>
              </MDBox>
            )}
          <MDBox sx={{ marginLeft: pxToRem(10) }}>
            <Status title={item?.status ? `${item?.status?.replace("-", " ")}` : ""} />
          </MDBox>
        </MDBox>
      ),
      action: (
        <MDBox display="flex">
          <IconButton
            aria-label="comment"
            color="error"
            disabled={
              item?.comments?.pmComments?.length === 0 && item?.comments?.wmComments?.length === 0
            }
            onClick={() => openWarehouseCommentModal(Common.USER_TEXT, item?.comments)}
          >
            {item?.comments?.pmComments?.length === 0 && item?.comments?.wmComments?.length === 0
              ? Icons.COMMENT_DISABLE
              : Icons.COMMENT}
          </IconButton>

          <IconButton
            aria-label="view"
            color="error"
            onClick={() => handleViewDetailsClick(item?.linkedEquipment || [])}
          >
            {Icons.VIEW}
          </IconButton>
        </MDBox>
      ),
    }));
  }, [dataList]);

  const projectHistoryByIdColumns = [
    { Header: "No.", accessor: "srNo", width: "3%" },
    { Header: "Equipment Type", accessor: "equipmentType" },
    { Header: "Approved Req Qty", accessor: "approvedRequestedQuantity" },
    { Header: "Warehouse Approved Qty", accessor: "warehouseApprovedQuantity" },
    { Header: "Status", accessor: "status" },
    { Header: "Action", accessor: "action" },
  ];

  const tableData = {
    projectHistoryByIdColumns,
    projectHistoryByIdRows,
  };

  return tableData;
}

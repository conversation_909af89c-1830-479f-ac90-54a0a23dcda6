import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

// MUI Components
import { styled, Switch, FormControlLabel } from "@mui/material";

// Components
import Author from "components/Table/Author";

// Store
import { setDprPersonnelList, updatePersonnelList } from "redux/Slice/Dpr";

const AntSwitch = styled(Switch)(({ theme }) => ({
  width: 28,
  height: 16,
  padding: 0,
  display: "flex",
  "&:active": {
    "& .MuiSwitch-thumb": {
      width: 15,
    },
    "& .MuiSwitch-switchBase.Mui-checked": {
      transform: "translateX(9px)",
    },
  },
  "& .MuiSwitch-switchBase": {
    padding: 2,
    "&.Mui-checked": {
      transform: "translateX(12px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        opacity: 1,
        backgroundColor: theme.palette.mode === "dark" ? "#177ddc" : "#1890ff",
      },
    },
  },
  "& .<PERSON><PERSON><PERSON>witch-thumb": {
    boxShadow: "0 2px 4px 0 rgb(0 35 11 / 20%)",
    width: 12,
    height: 12,
    borderRadius: 6,
    transition: theme.transitions.create(["width"], {
      duration: 200,
    }),
  },
  "& .MuiSwitch-track": {
    borderRadius: 16 / 2,
    opacity: 1,
    backgroundColor: theme.palette.mode === "dark" ? "rgba(255,255,255,.35)" : "rgba(0,0,0,.25)",
    boxSizing: "border-box",
  },
}));

function PersonnelListData(personnelDprList) {
  const dispatch = useDispatch();
  const [personnelRows, setPersonnelRows] = useState([]);

  const handleStatusChange = (updatedItemObj, currIndex) => {
    const updateDprPersonalList = personnelDprList.map((item, index) =>
      currIndex === index ? updatedItemObj : item
    );
    // Sending updated array to the setDprPersonnelList action
    dispatch(setDprPersonnelList(updateDprPersonalList));

    // Sending object to the updatePersonnelList action
    dispatch(updatePersonnelList({ ...updatedItemObj, indexNum: currIndex }));
  };

  useEffect(() => {
    if (personnelDprList) {
      const list = personnelDprList.map((item, index) => ({
        srNo: <Author name={index + 1} />,
        name: <Author name={item.name} />,
        job: <Author name={item.jobTitle} />,
        team: <Author name={item.team} />,
        workingTravelDay: (
          <FormControlLabel
            sx={{ display: "flex" }}
            control={
              <AntSwitch
                checked={item?.isWorking || false}
                onChange={(e, checked) =>
                  handleStatusChange({ ...item, isWorking: checked }, index)
                }
              />
            }
            label={item?.isWorking ? "Working" : "Travel"}
          />
        ),
        status: (
          <FormControlLabel
            sx={{ display: "flex" }}
            control={
              <AntSwitch
                checked={item?.status || false}
                onChange={(e, checked) => handleStatusChange({ ...item, status: checked }, index)}
              />
            }
            label={item?.status ? "On" : "Off"}
          />
        ),
      }));
      setPersonnelRows(list);
    }
  }, [personnelDprList]);

  return {
    personnelColumns: [
      { Header: "No.", accessor: "srNo", width: "5%" },
      { Header: "Name", accessor: "name", width: "30%" },
      { Header: "Job", accessor: "job", align: "left", width: "20%" },
      { Header: "Team", accessor: "team", align: "left", width: "20%" },
      { Header: "Travel/Working", accessor: "workingTravelDay", align: "left", width: "10%" },
      { Header: "Status Off/On", accessor: "status", width: "10%", align: "left" },
    ],
    personnelRows,
  };
}

export default PersonnelListData;

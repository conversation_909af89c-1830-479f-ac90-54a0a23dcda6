import React from "react";
import PropTypes from "prop-types";

// Material UI components
import { Card } from "@mui/material";

// Custom components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";
import DataTable from "examples/Tables/DataTable";
import Author from "components/Table/Author";
import ProjectTrackerTable from "examples/Tables/DataTable/projectTrackerTable";
import DetailProgressTable from "examples/Tables/DataTable/DetailTimeAnalysisTable";

// Layouts
import applicableDocumentsTabData from "layouts/dpr/data/applicableDocumentsTabData";
import QhseDPRData from "layouts/dpr/data/qhseData";
import DetailedTimeAnalysisData from "layouts/dpr/data/detailedTimeAnalysisData";
import EquipmentDprData from "layouts/dpr/data/equipmentData";
import PersonnelDprData from "layouts/dpr/data/personnelListData";
import ProgressSummaryData from "layouts/dpr/data/progressSummaryData";
import ContactDetails from "layouts/dpr/data/contactDetailsData";
import DailyActivityLogsData from "layouts/dpr/data/DailyActivityLogsData";

// Constants
import Constants, { defaultData, Colors } from "utils/Constants";

// Detailed Progress Data Table Columns
const showDetailProgressColumns = [
  { Header: "Location", accessor: "rowspanlocation", isRowspan: true, width: "10%" },
  { Header: "Report", accessor: "rowspanreport", align: "left", width: "30%" },
  { Header: "Completed Tasks", accessor: "completedTasks", align: "left", width: "20%" },
];

// Detailed Progress Data Table Rows creation function
const processApiDataWithGrouping = (data) => {
  const rowsData = [];

  data?.forEach((entry) => {
    const locationName = entry.title;

    rowsData.push({
      rowspanlocation: (
        <Author
          cellColor={Colors.TABLE_ROW_COLOR}
          isRowSpan
          style={{ fontWeight: "bold" }}
          name={locationName}
        />
      ),
      rowspanreport: <Author cellColor={Colors.TABLE_ROW_COLOR} isRowSpan name="" />,
      completedTasks: <Author cellColor={Colors.TABLE_ROW_COLOR} name="" />,
      isGroup: true,
    });

    entry.locationReports?.forEach((report) => {
      const relatedQuestions = report.reportQuestions || [];

      if (relatedQuestions.length) {
        rowsData.push({
          rowspanlocation: "",
          rowspanreport: <Author name={report.title} />,
          completedTasks: <Author name={relatedQuestions[0].title} />,
        });

        relatedQuestions.slice(1).forEach((q) => {
          rowsData.push({
            rowspanlocation: "",
            rowspanreport: "",
            completedTasks: <Author name={q.title} />,
          });
        });
      }
    });
    entry.assets?.forEach((asset) => {
      const assetLocation = `${asset.assetLocations.fromLocation} → ${asset.assetLocations.toLocation}`;
      let isFirstReport = true;

      asset.assetsReports.forEach((report) => {
        const relatedQuestions =
          asset.reportQuestions?.filter((q) => q.report === report[Constants.MONGOOSE_ID]) || [];

        if (isFirstReport) {
          rowsData.push({
            rowspanlocation: <Author name={assetLocation} />,
            rowspanreport: <Author name={report.title} />,
            completedTasks: relatedQuestions.length ? (
              <Author name={relatedQuestions[0].title} />
            ) : (
              "No questions available"
            ),
          });
          isFirstReport = false;
        } else {
          rowsData.push({
            rowspanlocation: "",
            rowspanreport: <Author name={report.title} />,
            completedTasks: relatedQuestions.length ? (
              <Author name={relatedQuestions[0].title} />
            ) : (
              "No questions available"
            ),
          });
        }

        relatedQuestions.slice(1).forEach((q) => {
          rowsData.push({
            rowspanlocation: "",
            rowspanreport: "",
            completedTasks: <Author name={q.title} />,
          });
        });
      });
    });
  });

  return rowsData;
};

// Common Func to create sections
function SectionCard({ title, children }) {
  return (
    <Card sx={{ mt: 1, px: 1 }}>
      <MDBox p={2} display="flex" justifyContent="space-between" alignItems="center">
        <MDTypography variant="h5" fontWeight="medium">
          {title}
        </MDTypography>
      </MDBox>
      {children}
    </Card>
  );
}

SectionCard.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

function Remarks({ text }) {
  return (
    <MDBox p={2}>
      <MDTypography variant="h6" fontWeight="medium">
        Remarks
      </MDTypography>
      <MDTypography variant="body2">{text || "-"}</MDTypography>
    </MDBox>
  );
}

Remarks.propTypes = {
  text: PropTypes.string,
};

Remarks.defaultProps = {
  text: "",
};

function Signature({ title, url }) {
  return (
    <MDBox mt={2}>
      <MDTypography variant="subtitle2" fontWeight="medium">
        {title}
      </MDTypography>
      <MDBox mt={1}>
        {url ? <img src={url} alt={title} style={{ maxWidth: "200px" }} /> : "-"}
      </MDBox>
    </MDBox>
  );
}

Signature.propTypes = {
  title: PropTypes.string.isRequired,
  url: PropTypes.string,
};

Signature.defaultProps = {
  url: "",
};

function DprView({ dprData }) {
  const { qhseColumns, qhseRows } = QhseDPRData(dprData?.qhse?.qhseSummary);

  const { applicableDocColumns, applicableDocsRows } = applicableDocumentsTabData(
    dprData?.applicableDocuments?.applicableDocumentsSummary?.data ||
      dprData?.applicableDocuments?.applicableDocumentsSummary ||
      []
  );

  const { timeAnalysisColumns, timeAnalysisRows } = DetailedTimeAnalysisData(
    dprData?.timeAnalysis?.detailedTimeAnalysis
  );

  const { dailyActivityLogColumns, dailyActivityRows } = DailyActivityLogsData(
    dprData?.timeAnalysis?.dailyActivityLog
  );

  const { equipmentColumns, equipmentRows } = EquipmentDprData(
    dprData?.equipmentList?.equipmentList
  );

  const { personnelColumns, personnelRows } = PersonnelDprData(
    dprData?.personnelList?.personnelList
  );
  const { contactColumns, contactRows } = ContactDetails(dprData?.progressData?.dprMembers || []);

  const sections = [
    {
      title: "QHSE",
      data: { columns: qhseColumns, rows: qhseRows },
      remarks: dprData?.qhse?.remarks,
    },
    {
      title: "Applicable Documents",
      data: { columns: applicableDocColumns, rows: applicableDocsRows },
      remarks: dprData?.applicableDocuments?.remarks,
    },
    {
      title: "Time Analysis",
      data: { columns: timeAnalysisColumns, rows: timeAnalysisRows },
      remarks: dprData?.timeAnalysis?.remarks,
    },
    {
      title: "Daily Activity Logs",
      data: { columns: dailyActivityLogColumns, rows: dailyActivityRows },
      remarks: "NA",
    },
    {
      title: "Personnel",
      data: { columns: personnelColumns, rows: personnelRows },
      remarks: dprData?.personnelList?.remarks,
    },
  ];

  if (dprData?.equipmentList?.equipmentList?.length > 0) {
    const equipmentSection = {
      title: "Equipment",
      data: { columns: equipmentColumns, rows: equipmentRows },
      remarks: dprData?.equipmentList?.remarks,
    };

    sections.splice(4, 0, equipmentSection);
  }

  // Table Column and Row Data for Progress Summary
  const {
    columns: progressColumns,
    rows: progressRows,
    extraHeadersList,
    footerList,
  } = ProgressSummaryData(
    dprData?.progressData?.progressSummary?.scopeData,
    dprData?.progressData?.progressSummary?.projectTrackerData,
    dprData?.progressData?.progressSummary?.totalCompletions || 0
  );

  // Table Column and Row Data for Detailed Progress Data
  const showDetailProgressRows = processApiDataWithGrouping(dprData?.progressData?.detailProgress);

  const converToFirstLetterSmall = (str) => {
    if (!str) return "";
    return str.charAt(0).toLowerCase() + str.slice(1);
  };

  return (
    <MDBox width="100%">
      {/* Progress Summary Section */}
      <SectionCard key="contactDetails" title="Contact Details Site">
        <DataTable
          table={{ columns: contactColumns, rows: contactRows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
          showTotalEntries={false}
          noEndBorder
          loading={Constants.FULFILLED}
          licenseRequired
        />
        <MDBox p={2}>
          <MDTypography variant="h6" fontWeight="medium">
            Last 24 Hours
          </MDTypography>
          <MDTypography variant="body2">{dprData?.progressData?.last24Hours || "-"}</MDTypography>
        </MDBox>

        <MDBox p={2}>
          <MDTypography variant="h6" fontWeight="medium">
            Next 24 Hours
          </MDTypography>
          <MDTypography variant="body2">{dprData?.progressData?.next24Hours || "-"}</MDTypography>
        </MDBox>
      </SectionCard>

      <SectionCard key="progressSummary" title="Progress Summary">
        <ProjectTrackerTable
          table={{ columns: progressColumns, rows: progressRows }}
          isSorted={false}
          loading={Constants.FULFILLED}
          extraHeaders={extraHeadersList}
          footerList={footerList}
        />
      </SectionCard>

      <SectionCard key="detailedProgressData" title="Detailed Progress Data">
        <DetailProgressTable
          table={{ columns: showDetailProgressColumns, rows: showDetailProgressRows }}
          isSorted={false}
          entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
          showTotalEntries={false}
          noEndBorder
          licenseRequired
          loading={Constants.FULFILLED}
        />
        <Remarks text={dprData?.progressData?.remarks} />
      </SectionCard>

      {sections.map(({ title, data, remarks }) => (
        <SectionCard key={title} title={title}>
          <DataTable
            table={data}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.MAX_PER_PAGE }}
            showTotalEntries={false}
            noEndBorder
            loading={Constants.FULFILLED}
            licenseRequired
          />
          {remarks !== "NA" && <Remarks text={remarks} />}
        </SectionCard>
      ))}
      <SectionCard title="Comments and Signatures">
        <MDBox p={2}>
          <MDTypography variant="h6" fontWeight="medium">
            Comments / Remarks
          </MDTypography>
          {["EndClientRepresentative", "ClientRepresentative", "ReynardRepresentative"].map(
            (key) => (
              <MDBox key={key} mt={2}>
                <MDTypography variant="subtitle2" fontWeight="medium">
                  {key.replace(/([A-Z])/g, " $1").trim()}
                </MDTypography>
                <MDTypography variant="body2">
                  {dprData?.commentsAndSignatures?.commentOrRemarks?.[key] ||
                    dprData?.commentsAndSignatures?.commentOrRemarks?.[
                      converToFirstLetterSmall(key)
                    ] ||
                    "-"}
                </MDTypography>
              </MDBox>
            )
          )}
        </MDBox>
        <MDBox p={2}>
          <MDTypography variant="h6" fontWeight="medium">
            Names and Signatures
          </MDTypography>
          {["EndClientRepresentative", "ClientRepresentative", "ReynardRepresentative"].map(
            (key) => (
              <Signature
                key={key}
                title={key
                  .replace(/([A-Z])/g, " $1")
                  .trim()
                  .replace(/^\w/, (c) => c.toUpperCase())}
                url={
                  dprData?.commentsAndSignatures?.namesAndSignature?.[key]?.url ||
                  dprData?.commentsAndSignatures?.namesAndSignature?.[converToFirstLetterSmall(key)]
                    ?.url
                }
              />
            )
          )}
        </MDBox>
      </SectionCard>
    </MDBox>
  );
}

DprView.propTypes = {
  dprData: PropTypes.shape({
    qhse: PropTypes.shape({
      qhseSummary: PropTypes.arrayOf(PropTypes.any),
      remarks: PropTypes.string,
    }),
    applicableDocuments: PropTypes.shape({
      applicableDocumentsSummary: PropTypes.arrayOf(PropTypes.any),
      remarks: PropTypes.string,
    }),
    timeAnalysis: PropTypes.shape({
      detailedTimeAnalysis: PropTypes.arrayOf(PropTypes.any),
      dailyActivityLog: PropTypes.arrayOf(PropTypes.any),
      remarks: PropTypes.string,
    }),
    equipmentList: PropTypes.shape({
      equipmentList: PropTypes.arrayOf(PropTypes.any),
      remarks: PropTypes.string,
    }),
    personnelList: PropTypes.shape({
      personnelList: PropTypes.arrayOf(PropTypes.any),
      remarks: PropTypes.string,
    }),
    progressData: PropTypes.shape({
      dprMembers: PropTypes.arrayOf(PropTypes.any),
      progressSummary: PropTypes.shape({
        projectTrackerData: PropTypes.arrayOf(PropTypes.any),
        scopeData: PropTypes.arrayOf(PropTypes.any),
        totalCompletions: PropTypes.number,
      }),
      detailProgress: PropTypes.arrayOf(PropTypes.any),
      last24Hours: PropTypes.string,
      next24Hours: PropTypes.string,
      remarks: PropTypes.string,
    }),
    commentsAndSignatures: PropTypes.shape({
      commentOrRemarks: PropTypes.shape({
        endClientRepresentative: PropTypes.string,
        clientRepresentative: PropTypes.string,
        reynardRepresentative: PropTypes.string,
      }),
      namesAndSignature: PropTypes.shape({
        endClientRepresentative: PropTypes.shape({
          url: PropTypes.string,
        }),
        clientRepresentative: PropTypes.shape({
          url: PropTypes.string,
        }),
        reynardRepresentative: PropTypes.shape({
          url: PropTypes.string,
        }),
      }),
    }),
  }),
};

DprView.defaultProps = {
  dprData: {
    qhse: {
      qhseSummary: [],
      remarks: "",
    },
    applicableDocuments: {
      applicableDocumentsSummary: [],
      remarks: "",
    },
    timeAnalysis: {
      detailedTimeAnalysis: [],
      dailyActivityLog: [],
      remarks: "",
    },
    equipmentList: {
      equipmentList: [],
      remarks: "",
    },
    personnelList: {
      personnelList: [],
      remarks: "",
    },
    progressData: {
      dprMembers: [],
      progressSummary: {
        projectTrackerData: [],
        scopeData: [],
        totalCompletions: 0,
      },
      detailProgress: [],
      last24Hours: "",
      next24Hours: "",
      remarks: "",
    },
    commentsAndSignatures: {
      commentOrRemarks: {
        endClientRepresentative: "",
        clientRepresentative: "",
        reynardRepresentative: "",
      },
      namesAndSignature: {
        endClientRepresentative: { url: "" },
        clientRepresentative: { url: "" },
        reynardRepresentative: { url: "" },
      },
    },
  },
};

export default DprView;

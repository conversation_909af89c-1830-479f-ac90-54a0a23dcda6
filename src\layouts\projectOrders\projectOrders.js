import React, { useEffect, useState } from "react";

// Common component
import MDBox from "components/MDBox";
import ResetFilterButton from "components/Buttons/ResetButton";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import SearchBar from "components/Search/SearchInTable";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";

// Examples
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import PageTitle from "examples/NewDesign/PageTitle";
import BasicModal from "examples/modal/BasicModal/BasicModal2";

// Layouts
import PMOrderRequest from "layouts/projectOrders/projectOrderRequest";
import ProjectOrderHistory from "layouts/projectOrders/ProjectOrderHistory";
import AddRequestEquipment from "layouts/projectOrders/ProjectEquipment/AddRequestEquipment";
import AddShoppingCartItems from "layouts/projectOrders/ProjectEquipment/AddShoppingCartItems";

// Mui Components
import { Divider } from "@mui/material";

// Redux
import { useDispatch } from "react-redux";

// Assests
import pxToRem from "assets/theme/functions/pxToRem";

import {
  reloadData,
  storeCurrentStatus,
  reloadShoppingData,
  OrderReloadData,
  ReturnOrderReloadData,
} from "redux/Slice/EquipmentRequest";

import projectOrderRequestListing, {
  engineerRequestedEquipmentThunk,
  newShoppingListCreateThunk,
  projectOrderShoppingListing,
  getProjectOrderHistory,
  getPMReturnOrders,
} from "redux/Thunks/EquipmentRequest";
import { projectListThunk } from "redux/Thunks/Filter";
import { openSnackbar } from "redux/Slice/Notification";

// Utils
import { paramCreater, formatKeyName } from "utils/methods/methods";
import Constants, {
  PageTitles,
  Icons,
  Colors,
  ButtonTitles,
  Common,
  ModalContent,
  FiltersModuleName,
  defaultData,
} from "utils/Constants";

const initialProjectOrderFilters = [
  {
    inputLabel: FiltersModuleName.SEARCH,
    list: [FiltersModuleName.PROJECT_ORDERS_FILTERS_TITLE_OBJ],
    selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
    isLoading: false,
  },
  {
    inputLabel: FiltersModuleName.PROJECT,
    list: [FiltersModuleName.PROJECT_ORDERS_FILTERS_TITLE_OBJ],
    selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
  },
  {
    inputLabel: FiltersModuleName.STATUS,
    list: [
      { [Constants.MONGOOSE_ID]: Constants.STATUS_PENDING, title: "Requests" },
      { [Constants.MONGOOSE_ID]: Constants.STATUS_REJECTED, title: "Rejected" },
      { [Constants.MONGOOSE_ID]: Constants.QUEUE, title: "Shopping Cart" },
      { [Constants.MONGOOSE_ID]: Constants.ORDERS, title: "Orders" },
      { [Constants.MONGOOSE_ID]: Constants.RETURN_ORDERS, title: "Return Orders" },
    ],
    selectedValue: Constants.STATUS_PENDING,
  },
];

const requiredKeysForEmptyShoppingList = [
  Common.PROJECT_ORDER_SHOPPING_CART_TITLE_NAME, // title
  Common.PROJECT_ORDER_SHOPPING_CART_PROJECT_NAME, // project
  Common.PROJECT_ORDER_SHOPPING_CART_FROM_DATE_NAME, // fromDate
  Common.PROJECT_ORDER_SHOPPING_CART_TO_DATE_NAME, // toDate
];

const initialHeadersItemObj = {
  title: "",
  project: "",
  fromDate: "",
  toDate: "",
};

export default function ProjectOrders() {
  const dispatch = useDispatch();
  let debounceTimeout;

  const [filters, setFilters] = useState(initialProjectOrderFilters);
  const [shouldUpdateState, setShouldUpdateState] = useState(false);

  // Add Request Equipment Modal State
  const [openAddEquipmentModal, setOpenAddEquipmentModal] = useState(false);
  const [addRequestedEquipmentObj, setAddRequestedEquipmentObj] = useState({
    equipmentOrders: [],
    tempOrders: [],
  });
  const [addRequestedEquipmentLoading, setAddRequestedEquipmentLoading] = useState(false);

  // Add Shopping Cart Modal State
  const [openAddShoppingCartModal, setOpenAddShoppingCartModal] = useState(false);
  const [addShoppingCartObjList, setAddShoppingCartObjList] = useState([]);
  const [addShoppingCartLoading, setAddShoppingCartLoading] = useState(false);
  const [headerItemsObj, setHeaderItemsObj] = useState(initialHeadersItemObj);
  const [shoppingCartItemsErrors, setShoppingCartItemsErrors] = useState({});

  const createParamObj = () => {
    const paramData = {
      search: filters[0].selectedValue,
      project: filters[1].selectedValue,
      status: filters[2].selectedValue,
    };

    Object.keys(paramData).forEach((key) => {
      if (
        paramData[key] === "" ||
        paramData[key] === "all" ||
        paramData[key] === null ||
        paramData[key] === undefined
      ) {
        delete paramData[key];
      }
    });

    return paramData;
  };

  const getRequestListing = async () => {
    const params = createParamObj();

    await dispatch(reloadData());
    await dispatch(storeCurrentStatus(params.status));
    await dispatch(projectOrderRequestListing(paramCreater(params)));
  };

  const somethingWentWrongErrorFunc = () => {
    dispatch(
      openSnackbar({
        message: Constants.SOMETHING_WENT_WRONG,
        notificationType: Constants.NOTIFICATION_ERROR,
      })
    );
  };

  const handleCloseAddEquipmentModal = () => {
    setAddRequestedEquipmentObj({
      equipmentOrders: [],
      tempOrders: [],
    });
    setOpenAddEquipmentModal(false);
  };

  const handleCloseAddShoppingCartModal = () => {
    setOpenAddShoppingCartModal(false);
    setHeaderItemsObj(initialHeadersItemObj);
    setAddShoppingCartObjList([]);
    setShoppingCartItemsErrors({});
  };

  // Rendering Conditional Btn via selected status
  const getConditionalBtnByStatus = (selectedStatus) => {
    switch (selectedStatus) {
      case Constants.STATUS_PENDING:
        return (
          <BasicButton
            title={ButtonTitles.NEW_REQUEST}
            icon={Icons.ADD}
            background={Colors.WHITE}
            color={Colors.PRIMARY}
            border
            action={() => setOpenAddEquipmentModal(true)}
          />
        );

      case Constants.QUEUE:
        return (
          <BasicButton
            title={ButtonTitles.ADD_SHOPPING_CART}
            icon={Icons.ADD}
            background={Colors.WHITE}
            color={Colors.PRIMARY}
            border
            action={() => setOpenAddShoppingCartModal(true)}
          />
        );

      default:
        return null;
    }
  };

  // Submit Equipment Request  Function
  const handleAddEquipmentFunc = async () => {
    if (
      addRequestedEquipmentObj.equipmentOrders.length === 0 &&
      addRequestedEquipmentObj.tempOrders.length === 0
    ) {
      dispatch(
        openSnackbar({
          message: Constants.ENGINEER_REQUESTED_ITEM_CREATE_ERROR,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
      return;
    }
    setAddRequestedEquipmentLoading(true);

    try {
      const res = await dispatch(engineerRequestedEquipmentThunk(addRequestedEquipmentObj));

      if (res?.payload?.status === Common.API_STATUS_200) {
        setAddRequestedEquipmentLoading(false);
        setOpenAddEquipmentModal(false);

        setAddRequestedEquipmentObj({
          equipmentOrders: [],
          tempOrders: [],
        });

        dispatch(
          openSnackbar({
            message: Constants.ENGINEER_REQUESTED_ITEM_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
        // Get Request Listing
        await getRequestListing();
      } else if (res?.payload?.status === Common.API_STATUS_400) {
        setAddRequestedEquipmentLoading(false);
        dispatch(
          openSnackbar({
            message: res?.payload?.data?.data?.[0]?.error || Constants.SOMETHING_WENT_WRONG,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
      } else {
        setAddRequestedEquipmentLoading(false);
        somethingWentWrongErrorFunc();
      }
    } catch (error) {
      setAddRequestedEquipmentLoading(false);
      somethingWentWrongErrorFunc();
    }
    setAddRequestedEquipmentLoading(false);
  };

  const validateShoppingListForm = (formData = {}) => {
    const errors = {};

    requiredKeysForEmptyShoppingList.forEach((key) => {
      const value = formData[key];

      // If the value is a string, trim and check if it's empty
      if (typeof value === "string" && value.trim() === "") {
        errors[key] = `${formatKeyName(key)} is required`;
      }
      // If the value is null or undefined (but allow numbers including 0)
      else if (value === null || value === undefined) {
        errors[key] = `${formatKeyName(key)} is required`;
      }
    });

    setShoppingCartItemsErrors((prev) => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  // making post api call to create new shopping list
  const postShoppingCartListFunc = async (payload) => {
    setAddShoppingCartLoading(true);

    try {
      const res = await dispatch(newShoppingListCreateThunk(payload));

      if (res?.payload?.status === Common.API_STATUS_200) {
        setAddShoppingCartLoading(false);
        setOpenAddShoppingCartModal(false);
        setAddShoppingCartObjList([]);
        setHeaderItemsObj(initialHeadersItemObj);
        setShoppingCartItemsErrors({});

        dispatch(
          openSnackbar({
            message: Constants.SHOPPING_CART_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );

        // Get Shopping Listing
        const params = createParamObj();
        await dispatch(reloadShoppingData());
        await dispatch(projectOrderShoppingListing(paramCreater(params)));
      } else {
        setAddShoppingCartLoading(false);
        somethingWentWrongErrorFunc();
      }
    } catch (error) {
      setAddShoppingCartLoading(false);
      somethingWentWrongErrorFunc();
    }
    setAddShoppingCartLoading(false);
  };

  // Submit Shopping Cart Function
  const handleAddShoppingCartFunc = async () => {
    if (addShoppingCartObjList.length === 0) {
      const validatedData = validateShoppingListForm(headerItemsObj);

      if (!validatedData) {
        dispatch(
          openSnackbar({
            message: Constants.FILL_ALL_REQUIRED_FIELDS,
            notificationType: Constants.NOTIFICATION_ERROR,
          })
        );
        return;
      }

      await postShoppingCartListFunc({ ...headerItemsObj, equipmentOrders: [] });
      return;
    }

    // Create payload with the new structure
    const payload = {
      ...headerItemsObj,
      equipmentOrders: addShoppingCartObjList,
      shoppingCart: null,
    };

    await postShoppingCartListFunc(payload);
  };

  // Handle Filter change function
  const handleFilterChangeFunc = async (e) => {
    const { value, name } = e.target;

    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === name) {
          return {
            ...filter,
            selectedValue: value,
          };
        }
        return filter;
      });
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  // Search Func
  const handleSearch = async (searchValue) => {
    if (searchValue === "" || !searchValue) return;

    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === FiltersModuleName.SEARCH) {
          return { ...filter, selectedValue: searchValue, isLoading: true };
        }
        return filter;
      });
      return updatedFilters;
    });

    setShouldUpdateState((prev) => !prev);
  };

  // For Search
  const debouncedHandleSearch = (e) => {
    clearTimeout(debounceTimeout);
    const { value } = e.target;

    const trimmedValue = value?.trim();
    debounceTimeout = setTimeout(async () => handleSearch(trimmedValue), 400);
  };

  // For Select Dropdown
  const handleSearchChange = (e) => {
    const { name, value } = e.target;
    if (value === "" || !value) return;

    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => {
        if (filter.inputLabel === name) {
          const selectedValue = filter.list.find((item) => item.title === value)?.[
            Constants.MONGOOSE_ID
          ];
          return { ...filter, selectedValue };
        }
        return filter;
      });
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  const handleReset = async () => {
    setFilters((prev) => {
      const updatedFilters = prev.map((filter) => ({
        ...filter,
        selectedValue:
          filter.inputLabel === FiltersModuleName.STATUS ? Constants.STATUS_PENDING : "all",
      }));
      return updatedFilters;
    });
    setShouldUpdateState((prev) => !prev);
  };

  const handleReload = async () => {
    if (filters[2].selectedValue === Constants.QUEUE) {
      await dispatch(reloadShoppingData());
    } else {
      await dispatch(reloadData());
    }

    const params = createParamObj();
    if (filters[2].selectedValue === Constants.QUEUE) {
      await dispatch(projectOrderShoppingListing(paramCreater(params)));
    } else {
      await dispatch(projectOrderRequestListing(paramCreater(params)));
    }
  };

  const handleOrderReload = async () => {
    const params = createParamObj();

    await dispatch(OrderReloadData());
    await dispatch(
      getProjectOrderHistory(
        paramCreater({ search: params.search || "", project: params.project || "" })
      )
    );
  };

  const handleProjectReload = async () => {
    const params = createParamObj();

    if (filters[2].selectedValue === Constants.ORDERS) {
      await handleOrderReload();
    } else if (filters[2].selectedValue === Constants.RETURN_ORDERS) {
      await dispatch(ReturnOrderReloadData());
      await dispatch(
        getPMReturnOrders(
          paramCreater({ search: params.search || "", project: params.project || "" })
        )
      );
    } else {
      await handleReload();
    }
  };

  // Display Modules By Order Status
  const displayModulesByOrderStatus = () => {
    const status = filters[2].selectedValue;
    switch (status) {
      case "":
      case Constants.CHECK_IN_2:
      case Constants.PRE_CHECK_OUT:
      case Constants.CHECK_OUT_2:
        return (
          <PMOrderRequest
            filters={filters}
            setFilters={setFilters}
            shouldUpdateState={shouldUpdateState}
          />
        );
      case Constants.ORDERS:
        return (
          <ProjectOrderHistory
            filters={filters}
            setFilters={setFilters}
            shouldUpdateState={shouldUpdateState}
          />
        );
      case Constants.RETURN_ORDERS:
        return (
          <ProjectOrderHistory
            filters={filters}
            setFilters={setFilters}
            shouldUpdateState={shouldUpdateState}
          />
        );
      default:
        return (
          <PMOrderRequest
            filters={filters}
            shouldUpdateState={shouldUpdateState}
            setFilters={setFilters}
          />
        );
    }
  };

  // Get Project List Func for dropdown.
  const getAllProjectsForFilters = async () => {
    try {
      const res = await dispatch(projectListThunk());
      if (res?.payload?.status) {
        setFilters((prev) => {
          const updatedFilters = prev.map((filter) => {
            if (filter.inputLabel === FiltersModuleName.PROJECT) {
              return {
                ...filter,
                selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
                list: [FiltersModuleName.PROJECT_ORDERS_FILTERS_TITLE_OBJ, ...res.payload.data],
              };
            }
            return filter;
          });
          return updatedFilters;
        });
      }
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  useEffect(() => {
    getAllProjectsForFilters();
  }, []);

  useEffect(() => {
    dispatch(storeCurrentStatus(filters?.[2]?.selectedValue));
  }, [filters?.[2]?.selectedValue]);

  return (
    <DashboardLayout module={defaultData.APPROVER_SCREEN_ID}>
      <DashboardNavbar />
      <MDBox display="flex" justifyContent="space-between">
        <PageTitle title={PageTitles.PROJECT_ORDERS} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleProjectReload}
          />
        </MDBox>
      </MDBox>
      <Divider sx={{ marginTop: 2 }} />

      <MDBox display="flex" justifyContent="space-between" gap={2}>
        <MDBox display="flex" alignItems="flex-end" flexWrap="wrap" justifyContent="flex-start">
          <MDBox>
            <SearchBar
              freeSolos
              width={pxToRem(200)}
              // key={item.inputLabel.replace(" ", "")}
              options={filters[0]?.list.map((val) => val.title) || []}
              filters={filters}
              label="Search"
              value={
                filters[0].list.find(
                  (item) => item[Constants.MONGOOSE_ID] === filters[0].selectedValue
                )?.title ||
                filters[0].selectedValue ||
                "All"
              }
              placeholder="Search"
              handleFilterChange={(e, value) => {
                handleSearchChange({
                  target: {
                    name: filters[0].inputLabel,
                    value,
                  },
                });
              }}
              debouncedHandleSearch={debouncedHandleSearch}
              isLoading={filters[0]?.isLoading}
            />
          </MDBox>

          <MDBox minWidth={160} mr={2}>
            <CustomAutoComplete
              label={filters[1].inputLabel}
              name={filters[1].inputLabel}
              id={filters[1].inputLabel}
              hint={Common.PROJECT_ORDER_PROJECT_LABEL}
              getOptionLabel={(option) => option.title || ""}
              menu={filters[1].list}
              value={{
                title:
                  filters[1].list.find(
                    (item) => item[Constants.MONGOOSE_ID] === filters[1].selectedValue
                  )?.title || "",
              }}
              handleChange={handleFilterChangeFunc}
              labelStyle={{ fontWeight: 600 }}
              valueStyle={{
                height: "40px",
                minWidth: 160,
                fontWeight: 600,
                backgroundColor: "#fff",
              }}
            />
          </MDBox>

          {filters
            ?.filter(
              (val) =>
                val.inputLabel !== FiltersModuleName.PROJECT &&
                val.inputLabel !== FiltersModuleName.SEARCH
            )
            ?.map((val) => (
              <FilterDropdown
                key={val.inputLabel}
                label={val.inputLabel}
                name={val.inputLabel}
                defaultValue={val?.selectedValue}
                value={val?.selectedValue}
                handleChange={handleFilterChangeFunc}
                menu={val.list}
                style={{ marginLeft: pxToRem(1) }}
              />
            ))}
          <MDBox>
            <ResetFilterButton handleReset={handleReset} style={{ marginLeft: "1rem" }} />
          </MDBox>
        </MDBox>

        <MDBox display="flex" alignItems="flex-end" justifyContent="flex-start">
          {getConditionalBtnByStatus(filters?.[2]?.selectedValue)}
        </MDBox>
      </MDBox>

      {/* Add Request Equipment Modal */}
      <BasicModal
        open={openAddEquipmentModal}
        handleClose={handleCloseAddEquipmentModal}
        title={ModalContent.REQUEST_ITEMS_MODAL_TITLE}
        actionButton={ButtonTitles.SUBMIT}
        btnLoading={addRequestedEquipmentLoading}
        btnLoadingText={ButtonTitles.SUBMIT_LOADING}
        disabled={addRequestedEquipmentLoading}
        handleAction={handleAddEquipmentFunc}
        width="90%"
        isAdditionalBtnRequired
        additionalBtnTitle={ButtonTitles.CANCEL}
        additionalBtnAction={handleCloseAddEquipmentModal}
      >
        <AddRequestEquipment
          addRequestedEquipmentObj={addRequestedEquipmentObj}
          setAddRequestedEquipmentObj={setAddRequestedEquipmentObj}
        />
      </BasicModal>

      {/* Add Shopping Cart Modal */}
      <BasicModal
        open={openAddShoppingCartModal}
        handleClose={handleCloseAddShoppingCartModal}
        title={ModalContent.SHOPPING_CART_MODAL_TITLE}
        actionButton={ButtonTitles.SUBMIT}
        btnLoading={addShoppingCartLoading}
        btnLoadingText={ButtonTitles.SUBMIT_LOADING}
        disabled={addShoppingCartLoading}
        handleAction={handleAddShoppingCartFunc}
        width="90%"
        isAdditionalBtnRequired
        additionalBtnTitle={ButtonTitles.CANCEL}
        additionalBtnAction={handleCloseAddShoppingCartModal}
      >
        <AddShoppingCartItems
          addShoppingCartObjList={addShoppingCartObjList}
          setAddShoppingCartObjList={setAddShoppingCartObjList}
          headerItemsObj={headerItemsObj}
          setHeaderItemsObj={setHeaderItemsObj}
          shoppingCartItemsErrors={shoppingCartItemsErrors}
          setShoppingCartItemsErrors={setShoppingCartItemsErrors}
        />
      </BasicModal>

      <MDBox mt={3} mb={5}>
        {displayModulesByOrderStatus()}
      </MDBox>
    </DashboardLayout>
  );
}

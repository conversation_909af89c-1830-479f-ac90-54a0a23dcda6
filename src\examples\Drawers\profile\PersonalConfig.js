// Data
import {
  clothesSizesDropdown,
  shoeSizesDropdown,
  airportDropdown,
} from "utils/Data/PersonnelFieldsData";
import Constants, { BackendFrontend, Common, countryList } from "utils/Constants";
import { generateTimeIntervals } from "utils/methods/methods";

export const personalDetailsFields = [
  {
    id: "street",
    label: Common.STREET_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter your street",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "area",
    label: Common.AREA_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter your area",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "zipCode",
    label: Common.ZIP_CODE_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter your zip code",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "city",
    label: Common.CITY_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter your city",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "state",
    label: Common.STATE_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter your state",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "country",
    label: Common.COUNTRY_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    hint: "Select your country",
    options: countryList.map((country) => ({
      [Constants.MONGOOSE_ID]: country,
      title: country,
    })),
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "nationality",
    label: Common.NATIONALITY_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    hint: "Select your nationality",
    options: [],
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "motherLanguage",
    label: Common.MAIN_LANGUAGE_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: [
      { id: "English", title: "English" },
      { id: "Dutch", title: "Dutch" },
    ],
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "prefAirportDeprt",
    label: Common.PREF_AIRPORT_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    hint: "Select your preferred airport",
    options: airportDropdown,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "otherAirport",
    label: Common.OTHER_PREF_AIRPORT_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter other preferred airport of departure",
    condition: (formData) => formData.prefAirportDeprt === "Other Airport",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "travelTimeToAirport",
    label: Common.TRAVEL_TIME_TO_AIRPORT_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: generateTimeIntervals(),
    hint: "Enter travel time in hours",
    gridProps: { xs: 12, md: 4 },
  },

  {
    id: "secondaryPrefAirportDeprt",
    label: Common.SECOND_PREF_AIRPORT_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    hint: "Select your second preferred airport",
    options: [
      { id: "LCY", title: "London City Airport (LCY)" },
      { id: "STN", title: "London Stansted Airport (STN)" },
    ],
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "otherSecondaryAirport",
    label: Common.OTHER_SECOND_PREF_AIRPORT_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter other second preferred airport of departure",
    condition: (formData) => formData.secondaryPrefAirportDeprt === "Other Airport",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "travelTimeToSecondAirport",
    label: Common.TRAVEL_TIME_TO_SECOND_AIRPORT_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: generateTimeIntervals(),
    hint: "Enter travel time in hours",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "clothesSize",
    label: Common.CLOTHES_SIZE_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: clothesSizesDropdown,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "shoeSize",
    label: Common.SHOE_SIZE_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: shoeSizesDropdown,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "windaId",
    label: Common.WINDA_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter WINDA ID",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "curriculumVitae",
    label: Common.CV_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
];

export const nextOfKinFields = [
  {
    id: "kinName",
    label: "Name",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter kin name",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "relationship",
    label: "Relationship",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter relationship",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinStreet",
    label: "Street/Building",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter street/building",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinArea",
    label: "Area",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter area",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinZip",
    label: "Zip Code",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter zip code",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinCity",
    label: "City",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter city",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinState",
    label: "State",
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter state",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "kinCountry",
    label: "Country",
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    hint: "Select country",
    options: countryList.map((country) => ({
      [Constants.MONGOOSE_ID]: country,
      title: country,
    })),
    gridProps: { xs: 12, md: 4 },
  },

  {
    id: "kinContactNumber",
    label: "Contact Number",
    type: BackendFrontend.PHONE,
    IsRequired: false,
    hint: "Enter contact number",
    gridProps: { xs: 12, md: 4 },
  },
];

export const contractualDetailsFields = [
  {
    id: "primaryPassport",
    label: Common.PASSPORT_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter passport number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "primaryPassportDoc",
    label: Common.PASSPORT_ID_DOCUMENT_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "primaryPassportIssueDate",
    label: Common.PASSPORT_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "primaryPassportExpiryDate",
    label: Common.PASSPORT_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "secondaryPassport",
    label: Common.SECOND_PASSPORT_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter Second passport number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "secondaryPassportDoc",
    label: Common.SECOND_PASSPORT_ID_DOCUMENT_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "secondaryPassportIssueDate",
    label: Common.SECOND_PASSPORT_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "secondaryPassportExpiryDate",
    label: Common.SECOND_PASSPORT_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "nationalIdentificationNumber",
    label: Common.NATIONAL_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter identification number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "drivingLicenseNumber",
    label: Common.DRIVING_LICENSE_NUMBER_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter driving license number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "drivingLicenseIssueDate",
    label: Common.DRIVING_LICENSE_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "drivingLicenseExpiryDate",
    label: Common.DRIVING_LICENSE_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "seamansBook",
    label: Common.SEAMANS_BOOKLET_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "seamansBookIssueDate",
    label: Common.SEAMANS_BOOKLET_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "seamansBookExpiryDate",
    label: Common.SEAMANS_BOOKLET_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "birthDate",
    label: Common.DATE_OF_BIRTH_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "birthPlace",
    label: Common.PLACE_OF_BIRTH_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter place of birth",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "employmentType",
    label: Common.EMPLOYMENT_TYPE_LABEL,
    type: BackendFrontend.OPTIONS,
    IsRequired: false,
    options: [
      { id: Common.INTERNAL_EMPLOYEE, title: "Employee" },
      { id: Common.SELF_EMPLOYED, title: "Self-employed" },
      { id: Common.EXTERNAL_EMPLOYEE, title: "External/Umbrella Company" },
    ],
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "healthInsurance",
    label: Common.HEALTH_INSURANCE_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "healthInsuranceId",
    label: Common.HEALTH_INSURANCE_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter Health Insurance ID",
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "healthIssueDate",
    label: Common.HEALTH_INSURANCE_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "healthExpiryDate",
    label: Common.HEALTH_INSURANCE_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "liabilityInsurance",
    label: Common.LIABILITY_INSURANCE_LABEL,
    type: BackendFrontend.IMAGES,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "liabilityInsuranceId",
    label: Common.LIABILITY_INSURANCE_ID_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    hint: "Enter Liability Insurance ID",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "liabilityIssueDate",
    label: Common.LIABILITY_INSURANCE_ISSUE_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "liabilityExpiryDate",
    label: Common.LIABILITY_INSURANCE_EXPIRY_DATE_LABEL,
    type: BackendFrontend.DATE,
    IsRequired: false,
    condition: (formData) => formData.employmentType === Common.SELF_EMPLOYED,
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "companyName",
    label: Common.COMPANY_NAME_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    condition: (formData) => formData.employmentType !== Common.INTERNAL_EMPLOYEE,
    hint: "Enter company name",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "companyRegistrationNumber",
    label: Common.COMPANY_REGISTRATION_NR_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    condition: (formData) => formData.employmentType !== Common.INTERNAL_EMPLOYEE,
    hint: "Enter company registration number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "companyVATNumber",
    label: Common.COMPANY_VAT_NR_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    condition: (formData) => formData.employmentType !== Common.INTERNAL_EMPLOYEE,
    hint: "Enter company VAT number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "companyAddress",
    label: Common.COMPANY_ADDRESS_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    condition: (formData) => formData.employmentType !== Common.INTERNAL_EMPLOYEE,
    hint: "Enter company address",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "bankName",
    label: Common.BANK_NAME_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter bank name",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "accountHolderName",
    label: Common.ACCOUNT_HOLDER_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter account holder name",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "bankAccount",
    label: Common.BANK_ACCOUNT_NR_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter IBAN number",
    gridProps: { xs: 12, md: 4 },
  },
  {
    id: "bicSwift",
    label: Common.BIC_SWIFT_LABEL,
    type: BackendFrontend.TEXT,
    IsRequired: false,
    hint: "Enter BIC/SWIFT code",
    gridProps: { xs: 12, md: 4 },
  },
];

const projectOrderDataList = [
  {
    project: "PowerLink Horizon",
    equipmentType: [
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
    ],
    items: 8,
    quantity: 12, // 3+5+4
  },
  {
    project: "Drone Industry",
    equipmentType: [
      {
        equipmentImage:
          "https://assets.spe.org/dims4/default/f6f92b9/2147483647/strip/true/crop/1024x768+0+0/resize/800x600!/quality/90/?url=http%3A%2F%2Fspe-brightspot.s3.us-east-2.amazonaws.com%2Fa8%2F1e%2Ff72d5b8325ea1409de4ef1df7557%2Fogf-static-equipment-part2-illustr-1-hero.jpg",
        requestedQuantity: 6,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQpQ3B-IXmzbTxtGeSSF5zy0WgA7zLByX49HJUeokDKstZknwkcbJJamEIxYD03GB4CbWg&usqp=CAU",
        requestedQuantity: 3,
      },
    ],
    items: 3,
    quantity: 9, // 6+3
  },
  {
    project: "Turbine Excellence Program",
    equipmentType: [
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
    ],
    items: 7,
    quantity: 27, // 2+7+5+4+6+3
  },
  {
    project: "Electric Flow System",
    equipmentType: [
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
    ],
    items: 5,
    quantity: 16, // 4+5+7
  },
  {
    project: "Wind Energy Sector Analysis",
    equipmentType: [
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
      {
        equipmentImage:
          "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTiP_I9g7Weha14UKJkwSPX3WTDDL_MomX-Pw&s",
        requestedQuantity: 3,
      },
      {
        equipmentImage:
          "https://www.gymleco.com/wp-content/uploads/2022/05/seated-leg-curl-341-selectorized-gym-machine-sittande-larcurl-viktmagasinsmaskin.jpg",
        requestedQuantity: 5,
      },
      {
        equipmentImage:
          "https://www.jrautomation.com/_next/image?url=https%3A%2F%2Fcdn.sanity.io%2Fimages%2F41hcjk5q%2Fproduction%2Fa2dee33cd4acefa71f99788a06d7b8ba5fe0741e-1000x667.jpg%3Fq%3D60%26auto%3Dformat&w=3840&q=75",
        requestedQuantity: 4,
      },
    ],
    items: 5,
    quantity: 27, // 6+3+5+2+4+7
  },
];

export default projectOrderDataList;

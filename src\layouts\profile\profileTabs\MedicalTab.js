import React from "react";
import PropTypes from "prop-types";

// @mui material components
import { Box } from "@mui/material";

// Matrial Dashboard React components
import MDBox from "components/MDBox";
import MDTypography from "components/MDTypography";

// Function imports from assets
import pxToRem from "assets/theme/functions/pxToRem";

function MedicalTab({ profile = {} }) {
  const { medical = [] } = profile;

  return (
    <Box>
      <MDTypography
        mt={2}
        ml={2}
        mb={2}
        variant="h5"
        fontWeight="medium"
        color="text"
        textTransform="capitalize"
      >
        Medical
      </MDTypography>
      {medical.length > 0 ? (
        medical.map((item) => (
          <MDBox key={item?.questionId} display="flex" alignContent="center" flexDirection="column">
            <MDBox
              ml={2}
              display="flex"
              alignContent="center"
              py={2}
              pr={2}
              sx={{ borderBottom: "1px solid #E0E6F5" }}
            >
              <MDBox display="flex" flexDirection="column" width="65%">
                <MDTypography
                  textTransform="capitalize"
                  sx={{
                    fontSize: pxToRem(16),
                    fontWeight: "500",
                    color: "#667085",
                    whiteSpace: "pre-line",
                    textAlign: "justify",
                  }}
                >
                  {item.title}
                </MDTypography>
                <MDTypography
                  textTransform="capitalize"
                  sx={{
                    fontSize: pxToRem(14),
                    fontWeight: "400",
                    color: "#667085",
                    whiteSpace: "pre-line",
                    textAlign: "justify",
                  }}
                >
                  {item.description}
                </MDTypography>
              </MDBox>
              <MDTypography
                sx={{
                  fontSize: pxToRem(16),
                  fontWeight: "600",
                  color: "#191D31",
                  ml: 5,
                }}
              >
                {item.answer ? "Yes" : "No"}
              </MDTypography>
            </MDBox>
          </MDBox>
        ))
      ) : (
        <MDTypography
          mt={2}
          ml={2}
          variant="h6"
          fontWeight="medium"
          color="text"
          textTransform="capitalize"
        >
          No Medical data available
        </MDTypography>
      )}
    </Box>
  );
}

MedicalTab.propTypes = {
  profile: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default MedicalTab;

import React, { useEffect, useState } from "react";

// Material ui
import { <PERSON><PERSON>, Divider, Menu, MenuItem, FormControl } from "@mui/material";
import MDBox from "components/MDBox";

// Custom Components
import TrainingMatrixData from "layouts/personnelManagement/TrainingMatrix/data/TrainingMatrixData";
import ViewDrawer from "examples/Drawers/ViewTableDrawer/index";
import DataTable from "examples/Tables/DataTable/GroupedRowTable";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import PageTitle from "examples/NewDesign/PageTitle";
import FilterDropdown from "components/Dropdown/FilterDropdown";
import ResetFilterButton from "components/Buttons/ResetButton";
import UserExcelModal from "examples/modal/TrainingMatrix/UserExcel";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import ExportHOC from "examples/HigherOrderComponents/ExportHOC";
import CustomAutoComeplete from "components/Dropdown/CustomAutoComeplete";
import MultiSelectDropdown from "components/Dropdown/MultiSelectDropdown";
import UserProfile from "layouts/profile/index";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { memberThunk } from "redux/Thunks/FieldsData";
import { projectListThunk } from "redux/Thunks/Filter";
import getAllCertificateType from "redux/Thunks/certificateQuestion";
import getTrainingMatrixData, {
  exportTrainingMatrixCertificate,
} from "redux/Thunks/TrainingMatrix";
import { setStoreFilters, resetFilters } from "redux/Slice/Filter";
// utils and constants
import Constants, {
  defaultData,
  PageTitles,
  Colors,
  Icons,
  FeatureTags,
  ButtonTitles,
  FiltersModuleName,
} from "utils/Constants";
import { paramCreater } from "utils/methods/methods";

// 3rd party lib
import { Feature } from "flagged";
import PropTypes from "prop-types";
import pxToRem from "assets/theme/functions/pxToRem";

function TrainingMatrix({ handleExportZipFile, exportLoading }) {
  const [loadingTrainingMatrixStatus, setLoadingTrainingMatrixStatus] = useState(Constants.PENDING);
  const [traingMatrixData, setTraingMatrixData] = useState([]);
  const [certificateTypeList, setCertificateTypeList] = useState([]);
  const [openUserProfile, setOpenUserProfile] = useState(false);
  const [openUserDetailModal, setOpenUserDetailModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [filters, setFilters] = useState([
    {
      inputLabel: "Project",
      list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }],
      selectedValue: "all",
    },
    {
      inputLabel: "Certificate Status",
      list: [
        { [Constants.MONGOOSE_ID]: "all", title: "All" },
        { [Constants.MONGOOSE_ID]: "certificate_expired", title: "Certificate Expired" },
        {
          [Constants.MONGOOSE_ID]: "certificate_expire_in_30",
          title: "Certificate Expiring in 30 days",
        },
        {
          [Constants.MONGOOSE_ID]: "certificate_expire_in_60",
          title: "Certificate Expiring in 60 days",
        },
      ],
      selectedValue: "all",
    },
    {
      inputLabel: "Name",
      list: [],
      selectedValue: "",
    },
    {
      inputLabel: "Function",
      list: [],
      selectedValue: [],
    },
  ]);

  const dispatch = useDispatch();
  const matrixStoredFilters = useSelector((state) => state.filtersSlice.trainingMatrix);
  const exportFilters = {
    project: filters[0].selectedValue,
    expire: filters[1].selectedValue,
    ...(filters[0].selectedValue !== "all" && {
      user: filters[2].selectedValue === "" ? "all" : filters[2].selectedValue,
      functions:
        filters[3].selectedValue &&
        (filters[3].selectedValue.length === filters[3].list.length ||
          filters[3].selectedValue.length === 0)
          ? "all"
          : filters[3].selectedValue.join(","),
    }),
  };
  const fetchTrainingMatrixData = async (tempFilters = filters) => {
    const paramData = {
      project: tempFilters[0].selectedValue,
      expire: tempFilters[1].selectedValue,
      ...(tempFilters[0].selectedValue !== "all" && {
        user: tempFilters[2].selectedValue === "" ? "all" : tempFilters[2].selectedValue,
        functions:
          tempFilters[3].selectedValue &&
          (tempFilters[3].selectedValue.length === tempFilters[3].list.length ||
            tempFilters[3].selectedValue.length === 0)
            ? "all"
            : tempFilters[3].selectedValue.join(","),
      }),
    };
    const TrainingMatrixRes = await dispatch(getTrainingMatrixData(paramCreater(paramData)));
    setTraingMatrixData(TrainingMatrixRes?.payload?.data?.data || []);
    setLoadingTrainingMatrixStatus(Constants.FULFILLED);
  };

  const getNameFunctions = async (id) => {
    if (id !== "all") {
      const usersSet = new Set();
      const functionsSet = new Set();

      const memberFunctionRes = await dispatch(
        memberThunk({
          id,
        })
      );

      memberFunctionRes.payload?.data.forEach((item) => {
        const userTitle = `${
          item?.user?.callingName ? item?.user?.callingName : item?.user?.firstName
        } ${item?.user?.lastName}`;
        const user = {
          [Constants.MONGOOSE_ID]: item?.user?.[Constants.MONGOOSE_ID],
          title: userTitle,
        };
        usersSet.add(JSON.stringify(user));

        if (item?.function?.functionName) {
          const func = {
            [Constants.MONGOOSE_ID]: item?.function?.[Constants.MONGOOSE_ID],
            title: item.function.functionName,
          };
          functionsSet.add(JSON.stringify(func));
        }
      });

      const uniqueUsers = Array.from(usersSet).map((user) => JSON.parse(user));
      const uniqueFunctions = Array.from(functionsSet).map((func) => JSON.parse(func));

      return {
        userList: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }, ...uniqueUsers],
        functionList: uniqueFunctions,
        userValue: "all",
        functionValue: uniqueFunctions.map((func) => func[Constants.MONGOOSE_ID]),
      };
    }
    return { userList: [], functionList: [], userValue: "", functionValue: [] };
  };

  useEffect(() => {
    (async () => {
      const paramData = {
        sort: "asc",
      };
      const [projectRes, certificateTypeRes] = await Promise.all([
        dispatch(projectListThunk()),
        dispatch(getAllCertificateType(paramCreater(paramData))),
      ]);
      const temp = [...filters];
      temp[0].list = [{ [Constants.MONGOOSE_ID]: "all", title: "All" }, ...projectRes.payload.data];

      if (matrixStoredFilters?.length > 0) {
        const { userList, functionList } = await getNameFunctions(
          matrixStoredFilters[0]?.selectedValue
        );
        const isProject = projectRes?.payload?.data?.some(
          (project) => project[Constants.MONGOOSE_ID] === matrixStoredFilters[0]?.selectedValue
        );
        const updatedFilters = temp.map((filter, index) => {
          if (index === 0) {
            return {
              ...filter,
              selectedValue: isProject
                ? matrixStoredFilters[0]?.selectedValue
                : FiltersModuleName.ALL_IN_SMALL_CASE,
              list: temp[0].list,
            };
          }
          if (index === 1) {
            return {
              ...filter,
              selectedValue: isProject
                ? matrixStoredFilters[1]?.selectedValue
                : FiltersModuleName.ALL_IN_SMALL_CASE,
            };
          }
          if (index === 2) {
            return {
              ...filter,
              selectedValue: isProject
                ? matrixStoredFilters[2]?.selectedValue
                : FiltersModuleName.ALL_IN_SMALL_CASE,
              list: userList,
            };
          }
          if (index === 3) {
            return {
              ...filter,
              selectedValue: isProject ? matrixStoredFilters[3]?.selectedValue : [],
              list: functionList,
            };
          }
          return filter;
        });

        setFilters(updatedFilters);
        fetchTrainingMatrixData(updatedFilters); // Use updated filters directly
      } else {
        setFilters(temp);
        fetchTrainingMatrixData(temp); // Use temp filters directly
      }

      setCertificateTypeList(certificateTypeRes?.payload?.data?.data || []);
    })();
  }, [matrixStoredFilters]);

  const handleOpenUserProfileDrawer = (id) => {
    setOpenUserProfile({
      right: true,
      userId: id,
    });
  };
  const { columns, rows } = TrainingMatrixData(
    traingMatrixData,
    certificateTypeList,
    filters[0].selectedValue,
    handleOpenUserProfileDrawer
  );

  const handleReload = async () => {
    let newFilters;
    setLoadingTrainingMatrixStatus(Constants.PENDING);
    const res = await dispatch(projectListThunk());
    if (res?.payload?.status) {
      const isProject = res?.payload?.data?.some(
        (project) => project[Constants.MONGOOSE_ID] === matrixStoredFilters[0]?.selectedValue
      );
      setFilters((prev) => {
        const updatedFilters = prev.map((filter) => {
          if (filter.inputLabel === FiltersModuleName.PROJECT) {
            return {
              ...filter,
              selectedValue: isProject
                ? matrixStoredFilters[0]?.selectedValue
                : FiltersModuleName.ALL_IN_SMALL_CASE,
              list: [{ [Constants.MONGOOSE_ID]: "all", title: "All" }, ...res.payload.data],
            };
          }
          if (filter.inputLabel === FiltersModuleName.NAME) {
            return {
              ...filter,
              selectedValue: isProject
                ? matrixStoredFilters[2]?.selectedValue
                : FiltersModuleName.ALL_IN_SMALL_CASE,
            };
          }
          if (filter.inputLabel === FiltersModuleName.FUNCTION) {
            return {
              ...filter,
              selectedValue: isProject ? matrixStoredFilters[3]?.selectedValue : [],
            };
          }
          return filter;
        });
        // Update the filters in the store
        dispatch(setStoreFilters({ module: "trainingMatrix", filters: updatedFilters }));
        newFilters = updatedFilters;
        return updatedFilters;
      });
    }
    await fetchTrainingMatrixData(newFilters);
  };

  const handleFilterType = async (e) => {
    setLoadingTrainingMatrixStatus(Constants.PENDING);
    const { name, value } = e.target;
    const temp = filters.map((filter) => ({ ...filter }));
    const index = temp.findIndex((val) => val.inputLabel === name);

    temp[index] = {
      ...temp[index],
      selectedValue: value,
    };

    if (name === "Project") {
      const { userList, functionList, userValue, functionValue } = await getNameFunctions(value);
      if (value) {
        temp[2] = {
          ...temp[2],
          list: userList,
          selectedValue: userValue,
        };

        temp[3] = {
          ...temp[3],
          list: functionList,
          selectedValue: functionValue,
        };
      } else {
        temp[0] = {
          ...temp[0],
          selectedValue: "",
        };
      }
    }

    setFilters(temp);
    dispatch(setStoreFilters({ module: "trainingMatrix", filters: temp }));
    fetchTrainingMatrixData(temp);
  };

  const handleResetFilter = () => {
    setLoadingTrainingMatrixStatus(Constants.PENDING);
    const tempFilters = filters.map((filter, index) => {
      switch (index) {
        case 0:
          return { ...filter, selectedValue: "all" };
        case 1:
          return { ...filter, selectedValue: "all" };
        case 2:
          return { ...filter, selectedValue: "" };
        case 3:
          return { ...filter, selectedValue: [] };
        default:
          return { ...filter };
      }
    });
    setFilters(tempFilters);
    dispatch(resetFilters({ module: "trainingMatrix" }));
    fetchTrainingMatrixData(tempFilters);
  };

  const groupedData = traingMatrixData?.trainingMatrix?.reduce((acc, item) => {
    const key = Object.keys(item)[0];
    const value = Object.values(item)[0];
    const updatedAcc = { ...acc };

    if (!updatedAcc[key]) {
      updatedAcc[key] = [];
    }
    updatedAcc[key].push(value);
    return updatedAcc;
  }, {});

  // Functions to open and close export menu
  const handleOpenExportMenu = (e) => {
    setAnchorEl(e.currentTarget);
  };

  const handleCloseExportMenu = () => {
    setAnchorEl(null);
  };

  // Functions to open user detail modal
  const handleOpenUserDetailModal = () => {
    setAnchorEl(null);
    setOpenUserDetailModal(true);
  };

  // UseEffect to close export menu if export is loading
  useEffect(() => {
    if (exportLoading) {
      setAnchorEl(null);
    }
  }, [exportLoading]);

  return (
    <DashboardLayout module={defaultData.TRAINING_MATRIX_SCREEN_ID}>
      <DashboardNavbar />
      <MDBox
        display="flex"
        flexDirection={{ md: "row", sm: "column" }}
        justifyContent={{ md: "space-between" }}
        alignItems={{ lg: "space-between", sm: "center" }}
      >
        <PageTitle title={PageTitles.TRAINING_MATRIX} />
        <BasicButton
          icon={Icons.RELOAD}
          background={Colors.WHITE}
          border
          color={Colors.BLACK}
          action={handleReload}
        />
      </MDBox>
      <Divider sx={{ marginTop: 2 }} />
      <Feature name={FeatureTags.TRAININGMATRIX}>
        <MDBox
          sx={{
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "start",
            alignItems: "flex-end",
            rowGap: pxToRem(16),
          }}
          mx={0}
        >
          {filters?.map((val, index) => {
            if (index < 2) {
              const isProject = val?.inputLabel === "Project";
              return isProject ? (
                <FormControl
                  key={val?.inputLabel}
                  variant="standard"
                  size="medium"
                  style={{ marginTop: "26px", width: 200, marginRight: "15px" }}
                >
                  <CustomAutoComeplete
                    label={val?.inputLabel}
                    name={val?.inputLabel}
                    id={val?.inputLabel}
                    getOptionLabel={(option) => option.title || ""}
                    menu={val?.list}
                    value={
                      val?.list.find(
                        (item) => item[Constants.MONGOOSE_ID] === val?.selectedValue
                      ) || null
                    }
                    handleChange={handleFilterType}
                    valueStyle={{
                      backgroundColor: Colors.WHITE,
                      height: pxToRem(40),
                      verticalMarginTop: pxToRem(4),
                      menuWidth: 400,
                      inputWidth: 250,
                      padding: pxToRem(1),
                    }}
                    labelStyle={{
                      fontSize: pxToRem(14),
                      fontWeight: 600,
                      color: Colors.BLACK,
                    }}
                  />
                </FormControl>
              ) : (
                <FilterDropdown
                  label={val.inputLabel}
                  name={val.inputLabel}
                  value={val.selectedValue}
                  handleChange={handleFilterType}
                  menu={val.list}
                  key={val.inputLabel}
                  maxContent={defaultData.MEDIUM_CONTENT_LENGTH}
                />
              );
            }
            return null;
          })}
          <MDBox
            sx={{
              display: "flex",
              flexDirection: "column",
              width: "fit-content",
              minWidth: pxToRem(160),
              gap: pxToRem(8),
              maxHeight: pxToRem(400),
              marginRight: pxToRem(16),
            }}
          >
            <CustomAutoComeplete
              label={filters[2].inputLabel}
              id={filters[2].inputLabel}
              name={filters[2].inputLabel}
              hint="Enter Name"
              handleChange={(e) => handleFilterType(e)}
              menu={filters[2].list}
              getOptionLabel={(option) => option.title || ""}
              value={
                filters[2].list.find(
                  (val) => val[Constants.MONGOOSE_ID] === filters[2].selectedValue
                ) || null
              }
              labelStyle={{ fontWeight: 600 }}
              valueStyle={{
                backgroundColor: Colors.WHITE,
                height: pxToRem(40),
                color: "#344767",
                textTransform: "capitalize",
                padding: "0px 4px",
              }}
              disabled={filters[0].selectedValue === "all"}
            />
          </MDBox>

          <MDBox
            sx={{
              display: "flex",
              flexDirection: "column",
              width: "fit-content",
              minWidth: pxToRem(160),
              gap: pxToRem(8),
              marginRight: pxToRem(16),
              maxWidth: pxToRem(310),
            }}
          >
            <MultiSelectDropdown
              label={filters[3].inputLabel}
              name={filters[3].inputLabel}
              id={filters[3].inputLabel}
              menu={filters[3].list}
              values={filters[3].selectedValue}
              handleChange={(name, value, id) => handleFilterType({ target: { name, value, id } })}
              labelStyle={{ fontWeight: 600 }}
              valueStyle={{
                backgroundColor: Colors.WHITE,
                height: pxToRem(40),
                verticalMarginTop: pxToRem(4),
                menuWidth: 310,
                inputWidth: 180,
              }}
              showBadge
              hint="Select Function"
              disabled={filters[0].selectedValue === "all"}
            />
          </MDBox>

          <MDBox sx={{ marginRight: pxToRem(8) }}>
            <ResetFilterButton handleReset={handleResetFilter} />
          </MDBox>
          <Button
            sx={{
              m: 2,
              ml: 0,
              mb: 0,
              backgroundColor: Colors.WHITE,
              "&:hover": {
                backgroundColor: Colors.WHITE,
              },
              fontSize: pxToRem(14),
              textTransform: "capitalize",
              width: pxToRem(130),
            }}
            variant="outlined"
            color="info"
            onClick={handleOpenExportMenu}
            style={{ btnMarginLeft: pxToRem(0) }}
            startIcon={Icons.EXPORT}
            disabled={exportLoading}
          >
            {exportLoading ? ButtonTitles.EXPORTING : ButtonTitles.EXPORT}
          </Button>
        </MDBox>
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleCloseExportMenu}
          MenuListProps={{
            "aria-labelledby": "basic-button",
          }}
        >
          <MenuItem onClick={handleOpenUserDetailModal}>CSV / Excel</MenuItem>
          <MenuItem
            onClick={() => handleExportZipFile(exportFilters, exportTrainingMatrixCertificate)}
          >
            Zip
          </MenuItem>
        </Menu>
        <MDBox mt={3} mb={3}>
          <DataTable
            table={{ columns, rows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            noEndBorder
            pagination={{ variant: "gradient", color: "info" }}
            loading={loadingTrainingMatrixStatus}
            groupedData={groupedData}
            keyField="function"
            keyFieldHeader="Function"
          />
        </MDBox>
        {openUserProfile.right && (
          <ViewDrawer
            title="User Profile"
            openDrawer={openUserProfile}
            closeDrawer={() => setOpenUserProfile({ right: false, userId: "" })}
            drawerWidth="90%"
          >
            <UserProfile isUserShow={false} userId={openUserProfile.userId} />
          </ViewDrawer>
        )}

        {/* Modal for export excel for user deatils  */}
        <UserExcelModal
          openUserDetailModal={openUserDetailModal}
          setOpenUserDetailModal={setOpenUserDetailModal}
          filters={filters}
        />
      </Feature>
    </DashboardLayout>
  );
}

TrainingMatrix.propTypes = {
  handleExportZipFile: PropTypes.func.isRequired,
  exportLoading: PropTypes.bool.isRequired,
};

export default ExportHOC(TrainingMatrix);

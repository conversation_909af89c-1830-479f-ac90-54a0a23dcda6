import { Common } from "utils/Constants";

const userDetails = [
  {
    label: "Personal Details",
    name: "personalDetails",
    details: [
      { title: Common.FIRST_NAME_LABEL, value: "callingName" },
      { title: Common.RESOURCE_NUMBER_LABEL, value: "resourceNumber" },
      { title: Common.COUNTRY_LABEL, value: "country" },
      { title: Common.NATIONALITY_LABEL, value: "nationality" },
      { title: Common.CONTACT_NUMBER_LABEL, value: "contactNumber" },
      { title: Common.EMAIL_LABEL, value: "email" },
      { title: Common.ADDRESS_LABEL, value: "address" },
      { title: Common.MAIN_LANGUAGE_LABEL, value: "motherLanguage" },
      { title: Common.PREF_AIRPORT_LABEL, value: "prefAirportDeprt" },
      { title: Common.TRAVEL_TIME_TO_AIRPORT_LABEL, value: "travelTimeToAirport" },
      { title: Common.SECOND_PREF_AIRPORT_LABEL, value: "secondaryPrefAirportDeprt" },
      { title: Common.TRAVEL_TIME_TO_SECOND_AIRPORT_LABEL, value: "travelTimeToSecondAirport" },
      { title: Common.SHOE_SIZE_LABEL, value: "shoeSize" },
      { title: Common.WINDA_ID_LABEL, value: "windaId" },
      { title: Common.CLOTHES_SIZE_LABEL, value: "clothesSize" },
      { title: Common.NEXT_OF_KIN_LABEL, value: "nextofKin" },
    ],
  },
  {
    label: "Contractual Details",
    name: "contractualDetails",
    details: [
      { title: Common.PASSPORT_ID_LABEL, value: "passport" },
      { title: Common.SECOND_PASSPORT_ID_LABEL, value: "secondaryPassport" },
      { title: Common.DRIVERS_LICENSE_LABEL, value: "drivingLicence" },
      { title: Common.SEAMANS_BOOKLET_LABEL, value: "seamansBook" },
      { title: Common.NATIONAL_ID_LABEL, value: "nationalIdentificationNumber" },
      { title: Common.DATE_OF_BIRTH_LABEL, value: "birthDate" },
      { title: Common.PLACE_OF_BIRTH_LABEL, value: "birthPlace" },
      { title: Common.EMPLOYMENT_TYPE_LABEL, value: "employmentType" },
      { title: Common.LIABILITY_INSURANCE_LABEL, value: "liabilityInsurance" },
      { title: Common.HEALTH_INSURANCE_LABEL, value: "healthInsurance" },
      { title: Common.COMPANY_NAME_LABEL, value: "companyName" },
      { title: Common.COMPANY_REGISTRATION_NR_LABEL, value: "companyRegistrationNumber" },
      { title: Common.COMPANY_VAT_NR_LABEL, value: "companyVATNumber" },
      { title: Common.COMPANY_ADDRESS_LABEL, value: "companyAddress" },
      { title: Common.BANK_NAME_LABEL, value: "bankName" },
      { title: Common.ACCOUNT_HOLDER_LABEL, value: "accountHolderName" },
      { title: Common.BANK_ACCOUNT_NR_LABEL, value: "bankAccount" },
      { title: Common.BIC_SWIFT_LABEL, value: "bicSwift" },
    ],
  },
];

export default userDetails;

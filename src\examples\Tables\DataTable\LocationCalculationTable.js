import { useMemo, useEffect, useState } from "react";

// @mui material components
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import Autocomplete from "@mui/material/Autocomplete";
import { Pagination, TableCell } from "@mui/material";
import TableContainer from "@mui/material/TableContainer";

// Common components
import MDBox from "components/MDBox";
import MDInput from "components/MDInput";
import MDTypography from "components/MDTypography";
import pxToRem from "assets/theme/functions/pxToRem";
import DataTableBodyCell from "examples/Tables/DataTable/DataTableBodyCell";
import DataTableHeadCell from "examples/Tables/DataTable/DataTableHeadCell";
import FontComponent from "components/Responsive/fonts";

// Redux
import { useSelector } from "react-redux";

// Constants
import Session from "utils/Sessions";
import Constants, { Icons, defaultData, Colors } from "utils/Constants";

// 3rd party library
import jwtDecode from "jwt-decode";
import PropTypes from "prop-types";
import { useTable, usePagination, useGlobalFilter, useAsyncDebounce, useSortBy } from "react-table";

function DataTable({
  entriesPerPage,
  canSearch,
  showTotalEntries,
  table,
  isSorted,
  noEndBorder,
  loading,
  licenseRequired,
  // currentPage, handleTablePagination, handleCurrentPage are use when need data size is more
  // Use this parameter to retrieve additional data when a user visits the last page
  currentPage,
  handleTablePagination,
  handleCurrentPage,
  backgroundColor, // New prop to specify the background color
  textColor,
  extraContent,
  towerProgress,
}) {
  const defaultValue = entriesPerPage.defaultValue ? entriesPerPage.defaultValue : 10;
  const entries = entriesPerPage.entries
    ? entriesPerPage.entries.map((el) => el.toString())
    : ["25"];
  const [status, setStatus] = useState(Constants.PENDING);
  const ConfigData = useSelector((state) => state.config);
  const columns = useMemo(() => table.columns, [table]);
  const data = useMemo(() => table.rows, [table]);
  const fontSize = FontComponent({ sizes: {} });

  const tableInstance = useTable(
    { columns, data, initialState: { pageIndex: currentPage } },
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    page,
    pageOptions,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    setGlobalFilter,
    state: { pageIndex, pageSize, globalFilter },
  } = tableInstance;

  // Set the default value for the entries per page when component mounts
  useEffect(() => setPageSize(defaultValue || 10), [defaultValue]);

  // update the status on role change, when superadmin login as admin and data changes
  useEffect(() => {
    const token = jwtDecode(Session.userToken);
    const { role } = token;

    const setPending = () => setStatus(Constants.PENDING);
    const setRejected = () => setStatus(Constants.REJECTED);
    const setFulfilled = () => setStatus(Constants.FULFILLED);
    const setNoData = () => setStatus("noData");

    const isSuperAdmin =
      role === defaultData.SUPER_ADMIN_ROLE && !Session.isSuperAdminViewingAdminPanel;
    const isPending = ConfigData.loading === Constants.PENDING || loading === Constants.PENDING;
    const isRejected = ConfigData.loading === Constants.REJECTED || loading === Constants.REJECTED;
    const isFulfilledWithData =
      loading === Constants.FULFILLED && rows.length > 0 && pageOptions.length > 0;
    const isFulfilledNoData =
      loading === Constants.FULFILLED && rows.length === 0 && pageOptions.length === 0;
    const isConfigFulfilled = ConfigData.loading === "fulfilled";

    // for superadmin
    if (isSuperAdmin || !licenseRequired) {
      if (loading === Constants.PENDING) setPending();
      else if (isRejected) setRejected();
      else if (isFulfilledWithData) setFulfilled();
      else if (isFulfilledNoData) setNoData();
    }
    // for admin
    else if (isPending) setPending();
    else if (isRejected) setRejected();
    else if (isConfigFulfilled && isFulfilledWithData) setFulfilled();
    else if (isConfigFulfilled && isFulfilledNoData) setNoData();
  }, [Session.userToken, Session.isSuperAdminViewingAdminPanel, ConfigData.loading, table]);
  // set current page to last page when the current page has no data
  // Works when some data is deleted from the last page
  useEffect(() => {
    if (pageOptions.length > 0 && pageOptions.length <= currentPage) {
      const lastPage = pageOptions.length - 1;
      gotoPage(lastPage);
      handleCurrentPage(lastPage);
    }
  }, [pageOptions]);
  // Set the entries per page value based on the select value
  const setEntriesPerPage = (value) => setPageSize(value);

  // Search input value state
  const [search, setSearch] = useState(globalFilter);

  // Search input state handle
  const onSearchChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 100);

  // A function that sets the sorted value for the table
  const setSortedValue = (column) => {
    let sortedValue;

    if (isSorted && column.isSorted) {
      sortedValue = column.isSortedDesc ? "desc" : "asce";
    } else if (isSorted) {
      sortedValue = "none";
    } else {
      sortedValue = false;
    }

    return sortedValue;
  };

  const handlePageChange = (event, value) => {
    if (pageOptions.length === value) handleTablePagination(value);
    if (event.target.innerText === "Previous") {
      previousPage();
    } else if (event.target.innerText === "Next") {
      nextPage();
    } else {
      gotoPage(value - 1);
      handleCurrentPage(value - 1);
    }
  };

  const handleCellBackground = (value) => {
    const numericValue = parseFloat(value);
    if (numericValue < 25) {
      return Colors.GREY1;
    }
    if (numericValue <= 50) {
      return Colors.PINK;
    }
    if (numericValue <= 75) {
      return Colors.LIGHT_PINK;
    }
    if (numericValue < 100) {
      return Colors.LIGHT_ORANGE1;
    }
    if (numericValue === 100) {
      return Colors.LIGHT_GREEN3;
    }
    return "transparent";
  };

  return (
    <MDBox
      sx={{
        backgroundColor: "White",
        borderRadius: "10px",
        border: "1px solid #E0E6F5",
        width: "100%",
      }}
    >
      <TableContainer sx={{ boxShadow: "none" }}>
        {entriesPerPage.entries || canSearch ? (
          <MDBox
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            p={3}
            backgroundColor={backgroundColor}
            color={textColor}
          >
            {entriesPerPage && (
              <MDBox display="flex" alignItems="center">
                <Autocomplete
                  disableClearable
                  value={pageSize.toString()}
                  options={entries}
                  onChange={(event, newValue) => {
                    setEntriesPerPage(parseInt(newValue, 10));
                  }}
                  size="small"
                  sx={{ width: "5rem" }}
                  renderInput={(params) => <MDInput {...params} />}
                />
                <MDTypography variant="caption" color="secondary">
                  &nbsp;&nbsp;entries per page
                </MDTypography>
              </MDBox>
            )}
            {canSearch && (
              <MDBox width="12rem" ml="auto">
                <MDInput
                  placeholder="Search..."
                  value={search}
                  size="small"
                  fullWidth
                  onChange={({ currentTarget }) => {
                    setSearch(search);
                    onSearchChange(currentTarget.value);
                  }}
                />
              </MDBox>
            )}
          </MDBox>
        ) : null}
        <Table {...getTableProps()}>
          <MDBox component="thead">
            {headerGroups.map((headerGroup) => (
              <TableRow {...headerGroup.getHeaderGroupProps()}>
                {headerGroup.headers.map((column) => (
                  <DataTableHeadCell
                    {...column.getHeaderProps(isSorted && column.getSortByToggleProps())}
                    width={column.width ? column.width : "auto"}
                    align={column.align ? column.align : "left"}
                    sorted={setSortedValue(column)}
                    backgroundColor={backgroundColor}
                    textColor={textColor}
                  >
                    {column.render("Header")}
                  </DataTableHeadCell>
                ))}
              </TableRow>
            ))}
          </MDBox>
          <TableBody {...getTableBodyProps()}>
            {(() => {
              switch (status) {
                case Constants.PENDING:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDBox py={5} display="flex" justifyContent="center" alignItems="center">
                          {Icons.LOADING2}
                        </MDBox>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.FULFILLED:
                  return page.map((row, key) => {
                    const headerList = [
                      "Location based report",
                      "Asset based report",
                      "Multiple asset based reports",
                    ];

                    prepareRow(row);
                    return (
                      <TableRow
                        {...row.getRowProps()}
                        sx={{
                          background: key % 2 !== 0 ? "#f6f7ff" : "transparent",
                        }}
                        style={{ border: "none" }}
                      >
                        {row.cells.map((cell) => (
                          <DataTableBodyCell
                            noBorder={noEndBorder && rows.length - 1 === key}
                            style={{
                              border:
                                (headerList.includes(cell?.value?.props?.name) && "none") ||
                                (cell?.value?.props?.name === "" && "none"),
                              backgroundColor:
                                cell.value &&
                                cell.column.id === "percentage" &&
                                cell.value.props.name !== "" &&
                                handleCellBackground(cell?.value?.props?.name),
                            }}
                            align={cell.column.align ? cell.column.align : "left"}
                            {...cell.getCellProps()}
                          >
                            {cell.render("Cell")}
                          </DataTableBodyCell>
                        ))}
                      </TableRow>
                    );
                  });

                case "noData":
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.NO_DATA_FOUND}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );

                case Constants.REJECTED:
                default:
                  return (
                    <TableRow>
                      <TableCell colSpan={columns.length} align="center">
                        <MDTypography variant="h4" color="secondary">
                          {Constants.SOMETHING_WENT_WRONG}
                        </MDTypography>
                      </TableCell>
                    </TableRow>
                  );
              }
            })()}
          </TableBody>
          <MDBox component="tfoot">
            <TableRow>
              <TableCell
                align="center"
                colSpan={4}
                sx={({ typography: { fontWeightBold } }) => ({
                  fontSize: pxToRem(14),
                  fontWeight: fontWeightBold,
                  backgroundColor: Colors.WHITE,
                  color: Colors.WHITE,
                  border: "1px solid rgba(235, 239, 249, 0.1)",
                  borderTop: "none",
                })}
              />
              <TableCell
                align="center"
                colSpan={2}
                sx={({ typography: { fontWeightBold } }) => ({
                  fontSize,
                  fontWeight: fontWeightBold,
                  backgroundColor: Colors.PRIMARY,
                  color: Colors.WHITE,
                  border: "1px solid rgba(235, 239, 249, 0.1)",
                  borderTop: "none",
                })}
              >
                Overall Completion Percentage
              </TableCell>
              <TableCell
                align="center"
                colSpan={2}
                sx={({ typography: { fontWeightBold } }) => ({
                  fontSize,
                  fontWeight: fontWeightBold,
                  backgroundColor: handleCellBackground(towerProgress),
                  color: Colors.BLACK,
                  border: "1px solid rgba(235, 239, 249, 0.1)",
                  borderTop: "none",
                })}
              >
                {towerProgress}
              </TableCell>
            </TableRow>
          </MDBox>
        </Table>
      </TableContainer>
      {status === Constants.FULFILLED && page.length > 0 && pageOptions.length > 1 && (
        <MDBox
          sx={{ color: "#f6f7ff" }}
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          justifyContent="center"
          alignItems={{ xs: "flex-start", sm: "center" }}
          p={!showTotalEntries && pageOptions.length === 1 ? 0 : 3}
        >
          <Pagination
            count={pageOptions.length}
            page={pageIndex + 1}
            onChange={handlePageChange}
            variant="outlined"
            shape="rounded"
            sx={{
              "& .Mui-selected:hover": {
                backgroundColor: "#f6f7ff",
              },
              "& .Mui-selected": {
                backgroundColor: "#e0e1f5",
              },
              ".MuiPaginationItem-root": {
                borderRadius: "50%",
                border: "none",
              },
            }}
          />
        </MDBox>
      )}
      {extraContent}
    </MDBox>
  );
}

// Setting default values for the props of DataTable
DataTable.defaultProps = {
  entriesPerPage: { defaultValue: 10, entries: [5, 10, 15, 20, 25] },
  canSearch: false,
  showTotalEntries: true,
  pagination: { variant: "gradient", color: "info" },
  isSorted: true,
  noEndBorder: false,
  currentPage: 0,
  loading: Constants.PENDING,
  licenseRequired: false,
  handleTablePagination: () => {},
  handleCurrentPage: () => {},
  backgroundColor: "", // Add a default value for backgroundColor
  textColor: "",
  extraContent: null,
  towerProgress: "",
};

// Typechecking props for the DataTable
DataTable.propTypes = {
  entriesPerPage: PropTypes.oneOfType([
    PropTypes.shape({
      defaultValue: PropTypes.number,
      entries: PropTypes.arrayOf(PropTypes.number),
    }),
    PropTypes.bool,
  ]),
  canSearch: PropTypes.bool,
  showTotalEntries: PropTypes.bool,
  table: PropTypes.objectOf(PropTypes.array).isRequired,
  pagination: PropTypes.shape({
    variant: PropTypes.oneOf(["contained", "gradient"]),
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "dark",
      "light",
    ]),
  }),
  isSorted: PropTypes.bool,
  noEndBorder: PropTypes.bool,
  loading: PropTypes.string,
  licenseRequired: PropTypes.bool,
  handleTablePagination: PropTypes.func,
  currentPage: PropTypes.number,
  handleCurrentPage: PropTypes.func,
  backgroundColor: PropTypes.string,
  textColor: PropTypes.string,
  extraContent: PropTypes.node,
  towerProgress: PropTypes.string,
};

export default DataTable;

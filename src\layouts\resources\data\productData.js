import React, { useMemo } from "react";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import { Icon<PERSON><PERSON>on, Tooltip } from "@mui/material";

// Components
import Author from "components/Table/Author";
import CustomCheckbox from "components/CustomCheckbox/CustomCheckbox";
import pxToRem from "assets/theme/functions/pxToRem";

// Constants
import Constants, { Icons, defaultData, Colors, Common } from "utils/Constants";

// 3rd Party Libraries
import moment from "moment";

export default function ProductData({
  productList = [],
  handleEditDrawerOpen = () => {},
  handleOpenEquipmentDetailDrawer = () => {},
  permissions = {},
  stockShow = false,
  updateStockCheckboxFunc = () => {},
  stockData = [],
  handleOpenEquipmentLocation = () => {},
  handleOpenPermanentType,
}) {
  const filteredList = useMemo(
    () => productList,
    [productList, stockShow] // Dependencies
  );

  function checkCertificateExpiry(expiryDate, forColor) {
    const expiryDateFormatted = moment(expiryDate);
    const currentDate = moment();
    const daysRemaining = expiryDateFormatted.diff(currentDate, "days");
    let color = "";

    switch (true) {
      case daysRemaining <= 0:
        color = forColor === "background" ? Colors.LIGHT_RED : Colors.DARK_RED;
        break;
      case daysRemaining <= 30:
        color = forColor === "background" ? Colors.LIGHT_ORANGE : Colors.DARK_ORANGE;
        break;
      case daysRemaining <= 60:
        color = forColor === "background" ? Colors.LIGHT_YELLOW : Colors.DARK_YELLOW;
        break;
      default:
        break;
    }

    return color;
  }

  const rows = useMemo(() => {
    if (!filteredList) return [];
    return filteredList.map((element, index) => {
      let certificateExpiryDate = null;
      const validCertificates =
        element?.certificateType?.filter((certificate) => certificate.endDate !== null) || [];

      const certificateExpiry =
        validCertificates?.map((certificate) => new Date(certificate.endDate)) || [];

      const currentDate = new Date();

      const filteredDates =
        certificateExpiry?.length > 0
          ? certificateExpiry?.filter(
              (expiryDate) => expiryDate?.getTime() !== currentDate?.getTime()
            )
          : [];

      filteredDates?.sort((a, b) => a - b);

      if (filteredDates?.length > 0) {
        certificateExpiryDate = filteredDates?.[0]?.toISOString();
      }
      return {
        stockCheck: element?.equipmentType?.quantityType?.[0]?.quantityType !== Common.UNIQUE && (
          <CustomCheckbox
            name="check"
            checked={stockData?.some(
              (item) => item?.[Constants.MONGOOSE_ID] === element?.[Constants.MONGOOSE_ID]
            )}
            onChange={(e) => updateStockCheckboxFunc(element, e.target.checked)}
          />
        ),
        srNo: <Author name={index + 1} />,
        equipmentcategory: (
          <Author
            name={
              element?.equipmentType?.equipmentCategory[0]?.name ||
              element?.equipmentType?.equipmentCategory?.name
            }
          />
        ),
        equipmenttype: <Author name={element?.equipmentType?.type} />,
        productNumber: (
          <Author
            name={
              element?.equipmentNumber && element?.equipmentNumber !== null
                ? element?.equipmentNumber
                : Constants.NA
            }
          />
        ),
        equipmentname: (
          <MDBox display="flex" justifyContent="start" alignItems="center">
            <img
              src={
                element?.equipmentImage?.[0]?.url
                  ? element?.equipmentImage?.[0]?.url
                  : process.env.REACT_APP_IMAGE_NOT_FOUND
              }
              alt={element?.equipmentImage}
              key={element?.equipmentImage?.[0]?.name}
              style={{
                width: pxToRem(50),
                height: pxToRem(50),
                marginRight: pxToRem(10),
                borderRadius: "8px",
              }}
            />
            <MDBox
              style={{
                flex: 1,
                width: "100%",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              <Author name={element?.name} />
            </MDBox>
          </MDBox>
        ),
        serialNumber: <Author name={element?.serialNumber ?? Constants.NA} />,
        availableStocks: <Author name={element?.quantity} />,
        certificateExpiry: (
          <Author
            name={
              certificateExpiryDate
                ? moment(certificateExpiryDate).format(defaultData.WEB_DATE_FORMAT)
                : ""
            }
            cellColor={checkCertificateExpiry(certificateExpiryDate, "background")}
            style={{
              color: checkCertificateExpiry(certificateExpiryDate, "color"),
              fontWeight: "bold",
            }}
          />
        ),
        equipmentLocation:
          element?.equipmentType?.quantityType?.[0]?.quantityType === Common.UNIQUE ? (
            <Author name={element?.inventoryLocation} />
          ) : (
            <Tooltip title="View Equipment Location" placement="top">
              <IconButton
                aria-label="fingerprint"
                color="info"
                onClick={() =>
                  handleOpenEquipmentLocation({
                    id: element?.[Constants.MONGOOSE_ID],
                    name: element?.name,
                    quantity: element?.quantity,
                  })
                }
              >
                {Icons.INFO}
              </IconButton>
            </Tooltip>
          ),
        action: (
          <MDBox>
            <IconButton
              aria-label="fingerprint"
              color="info"
              onClick={() => handleOpenEquipmentDetailDrawer(element?.[Constants.MONGOOSE_ID])}
            >
              {Icons.VIEW}
            </IconButton>
            {permissions?.update && (
              <IconButton
                aria-label="fingerprint"
                color="error"
                onClick={() => handleEditDrawerOpen(element?.[Constants.MONGOOSE_ID])}
              >
                {Icons.EDIT}
              </IconButton>
            )}
            {permissions?.update && element?.equipmentType?.isTemporary && (
              <IconButton
                aria-label="fingerprint"
                color="error"
                onClick={() => handleOpenPermanentType(element)}
              >
                {Icons.MOVE}
              </IconButton>
            )}
          </MDBox>
        ),
      };
    });
  }, [filteredList, permissions, stockData]); // Dependencies

  const columns = [
    ...(stockShow ? [{ Header: "", accessor: "stockCheck", align: "left" }] : []),
    { Header: "No.", accessor: "srNo", width: "2%" },
    { Header: "Equipment Category", accessor: "equipmentcategory", align: "left", width: "25%" },
    { Header: "Equipment Type", accessor: "equipmenttype", align: "left" },
    { Header: "Product Number", accessor: "productNumber", align: "left" },
    {
      Header: "Equipment Name",
      accessor: "equipmentname",
      align: "left",
      width: "35%",
    },
    { Header: "Serial Number", accessor: "serialNumber", align: "left" },
    { Header: "Available Stocks", accessor: "availableStocks", align: "center" },
    { Header: "Certificate Expiry", accessor: "certificateExpiry", align: "left" },
    { Header: "Location", accessor: "equipmentLocation", align: "center" },
    { Header: "Action", accessor: "action", align: "center" },
  ];

  const tableData = {
    columns,
    rows,
  };
  return tableData;
}

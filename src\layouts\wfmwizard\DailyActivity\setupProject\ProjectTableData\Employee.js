import MDBox from "components/MDBox";
import { useEffect, useState } from "react";
import Constants, { Icons, defaultData } from "utils/Constants";
import { IconButton } from "@mui/material";
import Author from "components/Table/Author";
import moment from "moment";

export default function EmployeeData(employeeList, handleDelete) {
  const [rows, setRows] = useState([]);

  useEffect(() => {
    if (employeeList) {
      const list = employeeList.map((item) => {
        const temp = {
          name: (
            <Author
              name={`${item?.user?.callingName ? item?.user?.callingName : item?.user?.firstName} ${
                item?.user?.lastName
              }`}
            />
          ),
          from: <Author name={moment(item?.createdAt).format(defaultData.WEB_DATE_FORMAT)} />,
          inactiveFrom: (
            <Author
              name={
                item?.deletedAt !== null && item?.updatedAt
                  ? moment(item?.updatedAt).format(defaultData.WEB_DATE_FORMAT)
                  : ""
              }
            />
          ),
          isProjectManager: <Author name={item?.isProjectManager ? "Yes" : "No"} />,
          Active: <Author name={item?.deletedAt === null ? "Active" : "Inactive"} />,
          action: (
            <MDBox>
              <IconButton
                color="secondary"
                fontSize="medium"
                sx={{ cursor: "pointer" }}
                onClick={() => handleDelete("employee", item[Constants.MONGOOSE_ID])}
                // disabled={!item?.isDeletable || false}
              >
                {Icons.REMOVE_MEMBER}
              </IconButton>
            </MDBox>
          ),
        };
        return temp;
      });
      setRows([...list]);
    }
  }, [employeeList]);

  return {
    employeeColumns: [
      { Header: "Name", accessor: "name", width: "20%", align: "left" },
      { Header: "Active From", accessor: "from", width: "20%", align: "left" },
      { Header: "InActive From", accessor: "inactiveFrom", width: "20%", align: "left" },
      { Header: "Is Project Manager", accessor: "isProjectManager", width: "20%", align: "left" },
      { Header: "Active", accessor: "Active", width: "20%", align: "left" },
      { Header: "Action", accessor: "action", width: "20%", align: "center" },
    ],
    employeeRows: rows,
  };
}

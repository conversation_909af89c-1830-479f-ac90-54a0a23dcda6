import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useDispatch } from "react-redux";

// @mui material components
import { Grid, Box, IconButton, Drawer } from "@mui/material";

// Material Dashboard 2 React components
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import FTextField from "components/Form/FTextField";
import ConfigDropdown from "components/Dropdown/ConfigDropdown";
import ImageUpload from "components/ImageUpload/imageUpload";
import BasicModal from "examples/modal/BasicModal/BasicModal2";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
import CustomAutoComplete2 from "components/Dropdown/CustomAutoComplete2";
import { openSnackbar } from "redux/Slice/Notification";

// Icons and Constants
import Constants, {
  Common,
  BackendFrontend,
  Icons,
  Colors,
  countryList,
  ButtonTitles,
} from "utils/Constants";
import MDTypography from "components/MDTypography";
import { nationalityList } from "redux/Thunks/UserManagement";

// Redux
import { updatePersonalThunk } from "redux/Thunks/SuperAdmin";

// Data
import { clothSizeChart, shoeSizeChart, airportDropdown } from "utils/Data/PersonnelFieldsData";
import pxToRem from "assets/theme/functions/pxToRem";

// Profile Config
import { personalDetailsFields, nextOfKinFields } from "examples/Drawers/profile/PersonalConfig";

function PersonalDetailsDrawer({ open, onClose, data = {} }) {
  const dispatch = useDispatch();
  const [nationalityOptions, setNationalityOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [infoBox, setInfoBox] = useState({ visible: false, content: "", position: null });
  useEffect(() => {
    const getNationalityList = async () => {
      try {
        const res = await dispatch(nationalityList());
        if (res?.payload?.status === Common.API_STATUS_200) {
          const resData = res?.payload?.data?.data || [];
          const nationalityLists = resData.map((item) => ({
            [Constants.MONGOOSE_ID]: item[Constants.MONGOOSE_ID],
            title: item?.nationality,
          }));
          setNationalityOptions(nationalityLists);
        }
      } catch (error) {
        await dispatch(
          openSnackbar({
            message: Constants.REVIEW_CREATE_SUCCESS,
            notificationType: Constants.NOTIFICATION_SUCCESS,
          })
        );
      }
    };
    getNationalityList();
  }, []);
  // Function to format initial data
  const formatInitialData = (initialData) => ({
    ...initialData,
    country: countryList.find((item) => item === initialData.country) || initialData.country,
    nationality:
      nationalityOptions.find(
        (item) =>
          item.title === initialData.nationality ||
          item[Constants.MONGOOSE_ID] === initialData.nationality
      )?.title ||
      initialData.nationality ||
      "",
    nextOfKin: initialData.nextOfKin?.map((kin) => ({
      kinName: kin.kinName || "",
      relationship: kin.relationship || "",
      kinStreet: kin.kinStreet || "",
      kinArea: kin.kinArea || "",
      kinCity: kin.kinCity || "",
      kinState: kin.kinState || "",
      kinZip: kin.kinZip || "",
      kinCountry: kin.kinCountry || "",

      kinContactNumber: kin.kinContactNumber || { in: "", number: "" },
      [Constants.MONGOOSE_ID]: kin[Constants.MONGOOSE_ID],
    })) || [{}],
  });

  const [formData, setFormData] = useState(formatInitialData(data));
  const [errors, setErrors] = useState({});

  const handleDrawerClose = () => {
    setErrors({});
    setFormData(formatInitialData(data));
    onClose();
  };

  const handleChange = (name, value) => {
    if ((name === "prefAirportDeprt" || name === "secondaryPrefAirportDeprt") && value?.iata) {
      setFormData((prev) => ({
        ...prev,
        [name]: `${value.title} (${value.iata})`,
      }));
      return;
    }
    const normalizedValue =
      typeof value === "object" && value !== null
        ? value.title || value.country || value.id
        : value;

    setFormData((prev) => ({
      ...prev,
      [name]: normalizedValue,
    }));
  };

  const handleImageChange = (name, imageValues) => {
    const imageInfoArray = imageValues.map((item) => ({
      url: item.url,
      size: item.size,
      name: item.name,
    }));

    setFormData((prev) => ({
      ...prev,
      [name]: imageInfoArray,
    }));
  };

  const handleImageCancel = (fieldName, updatedImageUrl) => {
    const newImageUrlArray = updatedImageUrl || formData[fieldName];
    const filteredImageUrlArray = newImageUrlArray.filter((img) => img.url !== Common.FRAME);

    setFormData((prev) => ({
      ...prev,
      [fieldName]: filteredImageUrlArray,
    }));
  };

  const handleSubmit = async () => {
    // Validate form
    const newErrors = {};
    personalDetailsFields.forEach((field) => {
      if (field.condition && !field.condition(formData)) {
        return;
      }

      // Check if the field is required and empty
      if (field.IsRequired && (!formData[field.id] || formData[field.id].length === 0)) {
        newErrors[field.id] = Constants.FIELD_REQUIRED_ERROR;
      }
    });

    // Validate Next of Kin fields
    formData.nextOfKin?.forEach((kin, index) => {
      nextOfKinFields.forEach((field) => {
        if (field.IsRequired && !kin[field.id]) {
          newErrors[`${field.id}_${index}`] = Constants.FIELD_REQUIRED_ERROR;
        }
      });
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Transform the data back to the expected format before submission
    const transformedData = {
      ...formData,
      nationality: nationalityOptions.find(
        (item) => item[Constants.MONGOOSE_ID] === formData.nationality
      )?.title,
      nextOfKin: formData.nextOfKin?.map((kin) => ({
        kinName: kin.kinName,
        relationship: kin.relationship,
        kinStreet: kin.kinStreet,
        kinArea: kin.kinArea,
        kinCity: kin.kinCity,
        kinState: kin.kinState,
        kinCountry: kin.kinCountry,
        kinZip: kin.kinZip,
        kinContactNumber: kin.kinContactNumber,
        [Constants.MONGOOSE_ID]: kin[Constants.MONGOOSE_ID],
      })),
    };

    const changedFields = {};
    Object.keys(transformedData).forEach((key) => {
      if (JSON.stringify(transformedData[key]) !== JSON.stringify(data[key])) {
        changedFields[key] = transformedData[key];
      }
    });

    const dataBody = {
      body: changedFields,
      id: formData[Constants.MONGOOSE_ID],
    };
    setLoading(true);
    const res = await dispatch(updatePersonalThunk(dataBody));
    setLoading(false);
    if (res.payload.status === Common.API_STATUS_200) {
      dispatch(
        openSnackbar({
          message: Constants.PROFILE_UPDATED_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      onClose();
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleKinChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedKin = [...(prev.nextOfKin || [])];
      if (field === "kinContactNumber") {
        // Handle phone number object structure
        updatedKin[index] = {
          ...updatedKin[index],
          [field]: typeof value === "object" ? value : { in: "", number: value },
        };
      } else {
        updatedKin[index] = {
          ...updatedKin[index],
          [field]: value,
        };
      }
      return {
        ...prev,
        nextOfKin: updatedKin,
      };
    });
  };

  const addKin = () => {
    setFormData((prev) => ({
      ...prev,
      nextOfKin: [...(prev.nextOfKin || []), {}],
    }));
  };

  const removeKin = (index) => {
    setFormData((prev) => ({
      ...prev,
      nextOfKin: prev.nextOfKin.filter((_, i) => i !== index),
    }));
  };

  const formatFieldValueToArray = (value) => {
    if (!value || value === "") return [];
    return Array.isArray(value) ? value : [value];
  };

  const handleInfoClick = (type) => {
    const content = type === "shoeSize" ? shoeSizeChart : clothSizeChart;

    setInfoBox({
      visible: true,
      content,
    });
  };
  const handleCloseInfoBox = () => {
    setInfoBox((prev) => ({
      ...prev,
      visible: false,
    }));
  };

  const renderField = (field) => {
    if (field.condition && !field.condition(formData)) {
      return null;
    }
    const fieldValue = formData[field.id] || "";

    switch (field.type) {
      case BackendFrontend.OPTIONS:
        if (field.id === "country" || field.id === "nationality") {
          const options = field.id === "nationality" ? nationalityOptions : field.options;
          const selectedOption = options.find(
            (item) => item.title === fieldValue || item[Constants.MONGOOSE_ID] === fieldValue
          ) || { title: fieldValue };
          return (
            <CustomAutoComplete
              label={field.IsRequired ? `${field.label}*` : field.label}
              hint={field.hint}
              name={field.id}
              id={field.id}
              getOptionLabel={(option) => option.title || option.country || ""}
              menu={options}
              value={selectedOption}
              handleChange={(e) => {
                handleChange(field.id, e.target.value || "");
              }}
              valueStyle={{
                backgroundColor: Colors.WHITE,
                height: "50px",
                verticalMarginTop: "4px",
                menuWidth: 400,
                inputWidth: 250,
                padding: "1px",
              }}
              labelStyle={{
                fontSize: "14px",
                color: Colors.BLACK,
              }}
              error={Boolean(errors[field.id])}
              helperText={errors[field.id]}
            />
          );
        }
        if (field.id === "prefAirportDeprt" || field.id === "secondaryPrefAirportDeprt") {
          const options = airportDropdown;
          const selectedOption = options.find(
            (item) => item.title === fieldValue || item[Constants.MONGOOSE_ID] === fieldValue
          ) || { title: fieldValue };
          return (
            <CustomAutoComplete2
              label={field.IsRequired ? `${field.label}*` : field.label}
              hint={field.hint}
              name={field.id}
              id={field.id}
              getOptionLabel={(option) =>
                option.iata ? `${option.title} (${option.iata})` : option.title
              }
              renderOption={(props, option) => (
                <li {...props}>
                  {option.iata ? `${option.title} (${option.iata})` : option.title}
                </li>
              )}
              menu={options}
              value={selectedOption}
              error={errors[field.id]}
              helperText={errors[field.id]}
              handleChange={(e) => {
                handleChange(field.id, e.target.value || "");
              }}
              valueStyle={{
                backgroundColor: Colors.WHITE,
                height: "50px",
                verticalMarginTop: "4px",
                menuWidth: 400,
                inputWidth: 250,
                padding: "1px",
              }}
              labelStyle={{
                fontSize: "14px",
                color: Colors.BLACK,
              }}
            />
          );
        }
        return (
          <MDBox
            sx={{
              position: "relative",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {(field.id === "shoeSize" || field.id === "clothesSize") && (
              <IconButton
                onClick={(e) => handleInfoClick(field.id, e)}
                aria-label="info-icon"
                color="info"
                size="small"
                sx={{
                  position: "absolute",
                  top: 0, // Adjust as needed
                  right: 4,
                  cursor: "pointer",
                  zIndex: 10, // Ensure it's on top
                  backgroundColor: "white", // Optional: helps visually separate
                }}
              >
                {Icons.INFO}
              </IconButton>
            )}

            <ConfigDropdown
              label={field.IsRequired ? `${field.label}*` : field.label}
              name={field.id}
              id={field.id}
              menu={field.options}
              value={fieldValue}
              error={errors[field.id]}
              helperText={errors[field.id]}
              handleChange={(e, value) => handleChange(field.id, value)}
            />
          </MDBox>
        );

      case BackendFrontend.IMAGES:
        return (
          <Box component="div" sx={{ width: "100%" }} onClick={(e) => e.stopPropagation()}>
            <ImageUpload
              label={field.IsRequired ? `${field.label}*` : field.label}
              name={field.id}
              onImageUpload={(imageValues) => handleImageChange(field.id, imageValues)}
              onImageCancel={(updatedImageUrl) => handleImageCancel(field.id, updatedImageUrl)}
              type="profile"
              formats={["image/jpeg", "image/jpg", "image/png", "application/pdf"]}
              acceptType="image/*, application/pdf"
              maxImageCount={1}
              error={Boolean(errors[field.id])}
              helperText={errors[field.id]}
              data={formatFieldValueToArray(fieldValue)}
            />
          </Box>
        );

      case BackendFrontend.NUMBER:
        return (
          <FTextField
            label={field.IsRequired ? `${field.label}*` : field.label}
            placeholder={field.hint}
            name={field.id}
            id={field.id}
            type="number"
            value={fieldValue}
            error={Boolean(errors[field.id])}
            helperText={errors[field.id]}
            handleChange={(e) => handleChange(field.id, e.target.value)}
          />
        );

      default:
        return (
          <FTextField
            label={field.IsRequired ? `${field.label}*` : field.label}
            placeholder={field.hint}
            name={field.id}
            id={field.id}
            type="text"
            value={fieldValue}
            error={Boolean(errors[field.id])}
            helperText={errors[field.id]}
            handleChange={(e) => handleChange(field.id, e.target.value)}
          />
        );
    }
  };

  const renderKinField = (field, kinIndex, kinData) => {
    const fieldValue = kinData[field.id] || "";
    const errorKey = `${field.id}_${kinIndex}`;

    if (field.type === BackendFrontend.PHONE) {
      return (
        <FTextField
          label={field.IsRequired ? `${field.label}*` : field.label}
          placeholder={field.hint}
          name={`${field.id}_${kinIndex}`}
          id={`${field.id}_${kinIndex}`}
          type="number"
          value={fieldValue.number || fieldValue}
          error={Boolean(errors[errorKey])}
          helperText={errors[errorKey]}
          handleChange={(e) => handleKinChange(kinIndex, field.id, e.target.value)}
        />
      );
    }
    if (field.type === BackendFrontend.OPTIONS) {
      const selectedOption = field.options.find(
        (item) => item.title === fieldValue || item[Constants.MONGOOSE_ID] === fieldValue
      ) || { title: fieldValue };
      return (
        <CustomAutoComplete
          label={field.IsRequired ? `${field.label}*` : field.label}
          hint={field.hint}
          name={field.id}
          id={field.id}
          getOptionLabel={(option) => option.title || option.country || ""}
          menu={field.options}
          value={selectedOption}
          handleChange={(e) => {
            handleKinChange(kinIndex, field.id, e.target.value || "");
          }}
          valueStyle={{
            backgroundColor: Colors.WHITE,
            height: "50px",
            menuWidth: 400,
            inputWidth: 250,
          }}
          labelStyle={{
            fontSize: "14px",
            color: Colors.BLACK,
          }}
          error={Boolean(errors[field.id])}
          helperText={errors[field.id]}
        />
      );
    }

    return (
      <FTextField
        label={field.IsRequired ? `${field.label}*` : field.label}
        placeholder={field.hint}
        name={`${field.id}_${kinIndex}`}
        id={`${field.id}_${kinIndex}`}
        type="text"
        value={fieldValue}
        error={Boolean(errors[errorKey])}
        helperText={errors[errorKey]}
        handleChange={(e) => handleKinChange(kinIndex, field.id, e.target.value)}
        multiline={field.id === "kinAddress"}
        rows={field.id === "kinAddress" ? 3 : 1}
      />
    );
  };

  return (
    <>
      <Drawer
        anchor="right"
        open={open}
        onClose={handleDrawerClose}
        PaperProps={{
          sx: { width: "90%" },
        }}
      >
        <MDBox
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {/* Header */}
          <MDBox
            px={3}
            py={2}
            display="flex"
            justifyContent="start"
            alignItems="center"
            borderBottom="1px solid #E0E6F5"
          >
            <IconButton onClick={handleDrawerClose} size="small">
              {Icons.CLOSE}
            </IconButton>
            <MDTypography variant="h5" marginLeft={2}>
              Edit Personal Details
            </MDTypography>
          </MDBox>

          {/* Form */}
          <Box
            component="form"
            noValidate
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            sx={{
              flexGrow: 1,
              overflow: "auto",
              p: 3,
            }}
          >
            <Grid container spacing={2}>
              {personalDetailsFields.map((field) => {
                // Check if the field has a condition and if it does not match, skip rendering
                if (field.condition && !field.condition(formData)) {
                  return null;
                }

                return (
                  <Grid item {...field.gridProps} key={field.id}>
                    {renderField(field)}
                  </Grid>
                );
              })}

              {/* Next of Kin Section */}
              <Grid item xs={12}>
                <MDBox
                  mt={4}
                  mb={2}
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  {formData.nextOfKin?.length > 0 && (
                    <MDTypography variant="h5">Next of Kin</MDTypography>
                  )}
                  <MDButton
                    variant="contained"
                    color="info"
                    onClick={addKin}
                    sx={{ display: "none" }}
                  >
                    Add Next of Kin
                  </MDButton>
                </MDBox>
              </Grid>

              {formData.nextOfKin?.map((kin, kinIndex) => (
                <>
                  <Grid item xs={12}>
                    <MDBox display="flex" justifyContent="space-between" alignItems="center">
                      <MDTypography variant="h6">{`Kin ${kinIndex + 1}`}</MDTypography>
                      {kinIndex > 0 && (
                        <IconButton
                          onClick={() => removeKin(kinIndex)}
                          size="small"
                          color="error"
                          sx={{ display: "none" }}
                        >
                          {Icons.DELETE}
                        </IconButton>
                      )}
                    </MDBox>
                  </Grid>
                  {nextOfKinFields.map((field) => (
                    <Grid item {...field.gridProps}>
                      {renderKinField(field, kinIndex, kin)}
                    </Grid>
                  ))}
                </>
              ))}
            </Grid>
          </Box>

          {/* Footer */}
          <MDBox
            px={3}
            py={2}
            display="flex"
            justifyContent="flex-end"
            gap={2}
            borderTop="1px solid #E0E6F5"
          >
            <MDButton variant="outlined" color="secondary" onClick={handleDrawerClose}>
              Cancel
            </MDButton>
            <MDButton
              variant="contained"
              color="info"
              disabled={loading}
              style={{ boxShadow: "none", textTransform: "none" }}
              onClick={handleSubmit}
            >
              {loading ? ButtonTitles.UPDATE_LOADING : "Save Changes"}
            </MDButton>
          </MDBox>
        </MDBox>
      </Drawer>

      <BasicModal
        title="Size Chart"
        open={infoBox.visible}
        handleClose={handleCloseInfoBox}
        handleAction={handleCloseInfoBox}
        actionButton={ButtonTitles.CANCEL}
        py={0}
        width={pxToRem(500)}
      >
        <MDBox
          sx={{
            overflow: "auto",
            padding: 2,
            "::-webkit-scrollbar": {
              width: "5px",
            },
            "::-webkit-scrollbar-thumb": {
              background: Colors.LIGHT_GRAY,
            },
            scrollbarWidth: "thin",
            scrollbarColor: "gray transparent",
          }}
        >
          {infoBox.visible &&
            infoBox?.content?.map((size) => (
              <MDTypography key={size} variant="body2" mb={1}>
                {size}
              </MDTypography>
            ))}
        </MDBox>
      </BasicModal>
    </>
  );
}

PersonalDetailsDrawer.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.shape({
    country: PropTypes.string,
    address: PropTypes.string,
    mainLanguage: PropTypes.string,
    nationality: PropTypes.string,
    travelTimeToAirport: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    prefAirportDeprt: PropTypes.string,
    travelTimeToSecondAirport: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    secondaryPrefAirportDeprt: PropTypes.string,
    clothesSize: PropTypes.string,
    shoeSize: PropTypes.string,
    windaId: PropTypes.string,
    curriculumVitae: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(
        PropTypes.shape({
          name: PropTypes.string,
          url: PropTypes.string,
          size: PropTypes.number,
        })
      ),
    ]),
    nextOfKin: PropTypes.arrayOf(
      PropTypes.shape({
        kinName: PropTypes.string,
        relationship: PropTypes.string,
        kinStreet: PropTypes.string,
        kinArea: PropTypes.string,
        kinCity: PropTypes.string,
        kinState: PropTypes.string,
        kinCountry: PropTypes.string,
        kinZip: PropTypes.string,
        kinContactNumber: PropTypes.shape({
          in: PropTypes.string,
          number: PropTypes.string,
        }),
      })
    ),
  }),
};

PersonalDetailsDrawer.defaultProps = {
  data: {},
};

export default PersonalDetailsDrawer;

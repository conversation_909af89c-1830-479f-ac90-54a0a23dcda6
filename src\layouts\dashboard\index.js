// Material Dashboard 2 React components
import { <PERSON><PERSON>, <PERSON>, Divider, <PERSON>u, MenuItem, FormControl } from "@mui/material";
import MDBox from "components/MDBox";

// Components
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import DeleteModal from "examples/modal/deleteModal/deleteModal";
import CustomFilterDropdown from "components/Dropdown/CustomFilterDropdown";
import QhseCardDrawer from "examples/Drawers/QHSE/SafetyCard";
import QhseViewCardDrawer from "examples/Drawers/QHSE/ViewSafetyCard";
import QhseUpdateCardDrawer from "examples/Drawers/QHSE/UpdateQhseCard";
import MultiSelectDropdown from "components/Dropdown/MultiSelectDropdown";
import CustomAutoComplete from "components/Dropdown/CustomAutoComeplete";
// Data
import authorsTableData from "layouts/dashboard/data/safetyCardData";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { deleteSafetyCardThunk } from "redux/Thunks/SafetyCard";
import SafetyCardForm from "examples/modal/SafetyCardForm/SafetyCardForm";
import CustomButton from "examples/NewDesign/CustomButton";
import BasicButton from "examples/NewDesign/CustomButton/BasicButton";
import PageTitle from "examples/NewDesign/PageTitle";
import filterThunk, {
  projectListThunk,
  exportSafetyCardThunk,
  exportIndividualSafetyCardThunk,
  categoryThunk,
} from "redux/Thunks/Filter";
import configThunk from "redux/Thunks/Config";
import {
  updateList,
  loadSafetyCardData,
  reloadData,
  removeSafetyCard,
} from "redux/Slice/SafetyCard";
import { qhseStoreFilters, resetFilters } from "redux/Slice/Filter";
import { locationListByIdThunk } from "redux/Thunks/FieldsData";
import Constants, {
  Icons,
  PageTitles,
  ButtonTitles,
  LicensePermission,
  Colors,
  defaultData,
  FiltersModuleName,
  Common,
} from "utils/Constants";
import pxToRem from "assets/theme/functions/pxToRem";
import { openSnackbar } from "redux/Slice/Notification";
import ExportHOC from "examples/HigherOrderComponents/ExportHOC";
import { paramCreater } from "utils/methods/methods";

function Dashboard() {
  const mongooseId = "_id";
  const [exportSafetyCardId, setExportSafetycardId] = useState(null);
  const [openSafeModal, setOpenSafeModal] = useState(false);
  const [safeAnchor, setSafeAnchor] = useState({ right: false });
  const [unSafeAnchor, setUnSafeAnchor] = useState({ right: false });
  const [ncrAnchor, setNcrAnchor] = useState({ right: false });
  const [incidentAnchor, setIncidentAnchor] = useState({ right: false });
  const [viewQhseAnchor, setViewQhseAnchor] = useState({ right: false });
  const [qhseViewId, setQhseViewId] = useState("");
  const [openUnsafeModal, setOpenUnsafeModal] = useState(false);
  const [openNCRModal, setOpenNCRModal] = useState(false);
  const [openIncidentModal, setOpenIncidentModal] = useState(false);
  const [updateQhseAnchor, setUpdateQhseAnchor] = useState({ right: false });
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [updateSafetyCard, setUpdateSafetyCard] = useState({});
  const [updateSafetyCardId, setUpdateSafetyCardId] = useState("");
  const [deleteData, setDeleteData] = useState({
    open: false,
    id: "",
    title: "",
    message: "",
  });
  const [dynamicFiltersdata, setDynamicFiltersdata] = useState([
    {
      inputLabel: "Project",
      list: [{ [mongooseId]: "all", title: "All" }],
      selectedValue: "all",
    },

    {
      inputLabel: "Location",
      list: [{ [mongooseId]: "all", title: "All" }],
      selectedValue: "all",
    },
    {
      inputLabel: "Category",
      list: [{ [mongooseId]: "all", title: "All" }],
      selectedValue: "all",
    },
  ]);
  const [staticFiltersdata, setStaticFiltersdata] = useState([
    {
      inputLabel: "Created",
      list: [
        "All",
        "Today",
        "Yesterday",
        "This Week",
        "This Month",
        "This Year",
        Common.CUSTOM_RANGE,
      ],
      selectedValue: "All",
      fromDate: "",
      toDate: "",
    },
    {
      inputLabel: "Type",
      list: [
        { [mongooseId]: "safe", title: "Safe" },
        { [mongooseId]: "unsafe", title: "Unsafe" },
        { [mongooseId]: "ncr", title: "NCR" },
        { [mongooseId]: "incident", title: "Incident" },
      ],
      selectedValue: ["safe", "unsafe", "ncr", "incident"],
    },

    {
      inputLabel: "Status",
      list: [
        {
          [mongooseId]: "open",
          title: "Open",
        },
        {
          [mongooseId]: "submitted",
          title: "Submitted To Client",
        },
        {
          [mongooseId]: "in_discussion",
          title: "In Discussion",
        },
        {
          [mongooseId]: "closed",
          title: "Closed",
        },
        {
          [mongooseId]: "archived",
          title: "Archived",
        },
      ],
      selectedValue: ["open", "submitted", "in_discussion", "closed", "archived"],
    },
  ]);
  const [tablePagination, setTablePagination] = useState({
    page: 0,
    perPage: defaultData.DATE_ON_SINGLE_API_CALL,
  });
  const [next, setNext] = useState(0);

  const dispatch = useDispatch();
  const safetyCards = useSelector((state) => state.safetCard);
  const permissions = useSelector((state) => state.License.permissions);
  const ConfigData = useSelector((state) => state.config);
  const qhseStoredFilters = useSelector((state) => state.filtersSlice.qhse);
  const screens = ConfigData?.screens;

  // All Drawer Func starts here
  const handleOpenSafeDrawer = async () => {
    setSafeAnchor({ right: true });
  };
  const handleCloseSafeDrawer = async () => {
    setSafeAnchor({ right: false });
  };

  const handleOpenUnsafeDrawer = async () => {
    setUnSafeAnchor({ right: true });
  };
  const handleCloseUnsafeDrawer = async () => {
    setUnSafeAnchor({ right: false });
  };
  const handleOpenNcrDrawer = async () => {
    setNcrAnchor({ right: true });
  };
  const handleCloseNcrDrawer = async () => {
    setNcrAnchor({ right: false });
  };
  const handleOpenIncidentDrawer = async () => {
    setIncidentAnchor({ right: true });
  };
  const handleCloseIncidentDrawer = async () => {
    setIncidentAnchor({ right: false });
  };
  const handleOpenExportMenu = (e) => {
    setAnchorEl(e.currentTarget);
  };

  const handleCloseExportMenu = () => {
    setAnchorEl(null);
  };

  const handleEditOpen = (item) => {
    const id = "_id";
    setUpdateSafetyCardId(item[id]);
    const temp = {
      title: item?.title ? item?.title : "",
      project: (item.project && {
        id: item.project[id] || "",
        title: item?.project?.projectNumber
          ? `${item?.project?.projectNumber} - ${item?.project?.title}`
          : item?.project?.title,
      }) || {
        id: "",
        title: "",
      },
      defaultProject: (item.defaultProject && {
        id: item?.defaultProject[id],
        title: item?.defaultProject.title,
      }) || {
        id: "",
        title: "",
      },
      location: (item.location && {
        id: item.location[id] || "",
        title: item.location?.title || "",
      }) || {
        id: "",
        title: "",
      },
      ...(item.category && {
        category: (item.category && {
          id: item.category[id] || "",
          title: item?.category?.categoryName,
        }) || {
          id: "",
          title: "",
        },
      }),
      severity: item?.severity?.[id],
      likelihood: item?.likelihood?.[id],
      type: item?.type?.[id],
      time: item.time ? item.time : "",
      images: item.images ? item.images : [],
      subject: item.subject ? item.subject : "",
      description: item.description ? item.description : "",
      actionsTaken: item.actionsTaken ? item.actionsTaken : "",
      statusUpdate: item.statusUpdate ? item.statusUpdate : "",
      item: item.item ? item.item : "",
      correctiveAction: item.correctiveAction ? item.correctiveAction : "",
      preventiveAction: item.preventiveAction ? item.preventiveAction : "",
      productQuality: item.productQuality ? item.productQuality : false,
      estimatedDelayCost: item.estimatedDelayCost ? item.estimatedDelayCost : "",
      status: item.status ? item.status : "Open",
      cardType: item?.cardType ? item?.cardType : "",
      dynamicFields: item.dynamicFields ? item.dynamicFields : [],
    };
    setUpdateSafetyCard({ ...temp });
    setUpdateQhseAnchor({ right: true });
  };

  const handleDeleteOpen = (item) => {
    setDeleteData({
      open: true,
      id: item[Constants.MONGOOSE_ID],
      title: `Delete ${
        item.cardType === Constants.NCR_VALUE
          ? "NCR"
          : item.cardType.charAt(0).toUpperCase() + item.cardType.slice(1)
      } Card`,
      message: `Are you sure you want to delete this ${
        item.cardType === Constants.NCR_VALUE ? "NCR" : item.cardType
      } Card?`,
    });
  };

  const handleOpenQhseDetailDrawer = async (id) => {
    setViewQhseAnchor({ right: true });
    setQhseViewId(id);
  };

  const handleExportIndividualPdf = async (id) => {
    setExportSafetycardId(id);
    const currentDate = new Date();
    const filename = `Reynard_safety_card_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.pdf`;

    const res = await dispatch(exportIndividualSafetyCardThunk(id));
    setExportSafetycardId(null);
    const url = window.URL.createObjectURL(res.payload);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
  };

  const { columns, rows } = authorsTableData(
    handleEditOpen,
    handleDeleteOpen,
    handleOpenQhseDetailDrawer,
    handleExportIndividualPdf,
    exportSafetyCardId
  );

  const handleFilter = async (dynamicData = dynamicFiltersdata, staticData = staticFiltersdata) => {
    setTablePagination({ ...tablePagination, page: 0 });
    setNext(0);
    const paramData = {
      page: 0,
      perPage: tablePagination.perPage,
      project: dynamicData[0].selectedValue,
      location: dynamicData[1].selectedValue,
      category: dynamicData[2].selectedValue,
      cardType: staticData[1]?.selectedValue,
      created:
        staticData[0]?.selectedValue === Common.CUSTOM_RANGE
          ? "custom"
          : staticData[0]?.selectedValue.toLowerCase().replace(/ /g, "_"),
      status: staticData[2]?.selectedValue,
    };

    if (staticData[0]?.selectedValue === Common.CUSTOM_RANGE) {
      paramData.fromDate = staticData[0].fromDate;
      paramData.toDate = staticData[0].toDate;

      // Skip API call if Custom Range is selected but either date is missing
      if (!paramData.fromDate || !paramData.toDate) {
        return;
      }
    }

    Object.keys(paramData).forEach((key) => {
      if (paramData[key] === "") {
        delete paramData[key];
      }
    });

    const data = new URLSearchParams(paramData);
    const res = await dispatch(filterThunk(data));
    await dispatch(updateList(res.payload.data.qhseData));
  };

  useEffect(() => {
    let isMounted = true;

    (async () => {
      const tempProjectList = await dispatch(projectListThunk());
      let tempLocationList = {
        payload: {
          data: [],
        },
      };
      if (
        qhseStoredFilters?.dynamicFilters?.length > 0 &&
        qhseStoredFilters?.dynamicFilters[0]?.selectedValue !== "all" &&
        tempProjectList?.payload?.data?.some(
          (project) =>
            project[Constants.MONGOOSE_ID] === qhseStoredFilters?.dynamicFilters[0]?.selectedValue
        )
      ) {
        tempLocationList = await dispatch(
          locationListByIdThunk(qhseStoredFilters?.dynamicFilters[0]?.selectedValue)
        );
      }

      const categoryList = await dispatch(categoryThunk());

      if (!isMounted) return;

      const temp = [
        {
          inputLabel: "Project",
          list: [{ [mongooseId]: "all", title: "All" }, ...tempProjectList.payload.data],
          selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
        },
        {
          inputLabel: "Location",
          list: [
            { [mongooseId]: "all", title: "All" },
            ...tempLocationList.payload.data.filter(
              (item) => item.title !== "" && item.title !== undefined && item.title !== null
            ),
          ],
          selectedValue: "all",
        },
        {
          inputLabel: "Category",
          list: [
            { [mongooseId]: "all", title: "All" },
            ...categoryList.payload.data.map((item) => ({
              ...item,
              title: item.categoryName,
            })),
          ],
          selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
        },
      ];

      // Update filters only if dynamic filters are not already present
      if (qhseStoredFilters?.dynamicFilters?.length > 0) {
        setDynamicFiltersdata((prevFilters) => {
          const updatedFilters = [...prevFilters];
          updatedFilters[0] = {
            ...updatedFilters[0],
            selectedValue: tempProjectList?.payload?.data?.some(
              (project) =>
                project[Constants.MONGOOSE_ID] ===
                qhseStoredFilters?.dynamicFilters[0]?.selectedValue
            )
              ? qhseStoredFilters?.dynamicFilters[0]?.selectedValue
              : FiltersModuleName.ALL_IN_SMALL_CASE,
            list: temp[0].list,
          };

          updatedFilters[1] = {
            ...updatedFilters[1],
            selectedValue: tempLocationList?.payload?.data?.some(
              (location) =>
                location[Constants.MONGOOSE_ID] ===
                qhseStoredFilters?.dynamicFilters[1]?.selectedValue
            )
              ? qhseStoredFilters?.dynamicFilters[1]?.selectedValue
              : FiltersModuleName.ALL_IN_SMALL_CASE,
            list: temp[1].list,
          };
          updatedFilters[2] = {
            ...updatedFilters[2],
            selectedValue: categoryList?.payload?.data?.some(
              (category) =>
                category[Constants.MONGOOSE_ID] ===
                qhseStoredFilters?.dynamicFilters[2]?.selectedValue
            )
              ? qhseStoredFilters?.dynamicFilters[2]?.selectedValue
              : FiltersModuleName.ALL_IN_SMALL_CASE,
            list: temp[2].list,
          };
          return updatedFilters;
        });
      } else {
        setDynamicFiltersdata(temp); // Update dynamic filters with temp only if no stored filters
      }

      // Always update static filters if present
      if (qhseStoredFilters?.staticFilters?.length > 0) {
        setStaticFiltersdata(qhseStoredFilters.staticFilters);
      }
    })();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    const staticFilter =
      qhseStoredFilters?.staticFilters?.length > 0
        ? qhseStoredFilters.staticFilters
        : staticFiltersdata;
    handleFilter(dynamicFiltersdata, staticFilter);
  }, [dynamicFiltersdata, staticFiltersdata]);

  useEffect(() => {
    if (!updateQhseAnchor) {
      setUpdateSafetyCardId("");
      setUpdateSafetyCard({});
    }
  }, [updateQhseAnchor]);

  // Func realted to filter
  const updateFilterList = (locationData = []) => {
    const createListWithDefault = (list = []) => [
      {
        [Constants.MONGOOSE_ID]: FiltersModuleName.ALL_IN_SMALL_CASE,
        title:
          list.length > 0
            ? FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL
            : FiltersModuleName.NO_DATA_FOUND,
      },
      ...list,
    ];

    setDynamicFiltersdata((prevFilters) => {
      const updatedFilters = prevFilters.map((filter) => {
        if (filter.inputLabel === FiltersModuleName.LOCATION) {
          return {
            ...filter,
            selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
            list: createListWithDefault(locationData),
          };
        }
        return filter;
      });

      // Updating the Store also with the latest filters.
      dispatch(
        qhseStoreFilters({ module: "qhse", filters: updatedFilters, filterType: "dynamic" })
      );
      return updatedFilters;
    });
  };

  const fetchLocationList = async (key) => {
    try {
      const [locationList] = await Promise.all([dispatch(locationListByIdThunk(key))]);
      const locationArr = locationList?.payload?.data;
      updateFilterList(locationArr);
    } catch (error) {
      dispatch(
        openSnackbar({
          message: Constants.SOMETHING_WENT_WRONG,
          notificationType: Constants.NOTIFICATION_ERROR,
        })
      );
    }
  };

  const handleDynamicFilter = async (e) => {
    const temp = [...dynamicFiltersdata];
    const index = temp.findIndex((item) => item.inputLabel === e.target.name);
    const locationIndex = temp.findIndex((item) => item.inputLabel === FiltersModuleName.LOCATION);

    temp[index] = {
      ...temp[index],
      selectedValue: e.target.value,
    };

    if (
      e.target.name === FiltersModuleName.PROJECT &&
      e.target.value === FiltersModuleName.ALL_IN_SMALL_CASE
    ) {
      temp[locationIndex] = {
        ...temp[locationIndex],
        selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
        list: [
          {
            [Constants.MONGOOSE_ID]: FiltersModuleName.ALL_IN_SMALL_CASE,
            title: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
          },
        ],
      };
    }

    setDynamicFiltersdata(temp);
    if (
      e.target.name === FiltersModuleName.PROJECT &&
      e.target.value !== FiltersModuleName.ALL_IN_SMALL_CASE
    ) {
      await fetchLocationList(e.target.value);
    }

    dispatch(qhseStoreFilters({ module: "qhse", filters: temp, filterType: "dynamic" }));
    await handleFilter(temp, staticFiltersdata);
  };

  const handleStaticFilter = async (e) => {
    const temp = [...staticFiltersdata];
    const index = temp.findIndex((item) => item.inputLabel === e.target.name);

    temp[index] = {
      ...temp[index],
      selectedValue: e.target.value,
    };

    setStaticFiltersdata(temp);
    dispatch(qhseStoreFilters({ module: "qhse", filters: temp, filterType: "static" }));

    // If selecting Custom Range for date filter, only proceed with filter if both dates are already set
    if (e.target.name === "Created" && e.target.value === Common.CUSTOM_RANGE) {
      const createdFilter = temp.find((filter) => filter.inputLabel === "Created");
      if (!createdFilter.fromDate || !createdFilter.toDate) {
        return; // Skip API call until both dates are selected
      }
    }

    await handleFilter(dynamicFiltersdata, temp);
  };

  const handleCustomDateChange = (field, value) => {
    setStaticFiltersdata((prevFilters) => {
      const updatedFilters = prevFilters.map((filter) => {
        if (filter.inputLabel === "Created") {
          return { ...filter, [field]: value };
        }
        return filter;
      });

      // Get the updated Created filter
      const createdFilter = updatedFilters.find((filter) => filter.inputLabel === "Created");

      // Only trigger filter if both dates are selected and Custom Range is selected
      if (
        createdFilter &&
        createdFilter.selectedValue === Common.CUSTOM_RANGE &&
        createdFilter.fromDate &&
        createdFilter.toDate
      ) {
        dispatch(
          qhseStoreFilters({ module: "qhse", filters: updatedFilters, filterType: "static" })
        );
        handleFilter(dynamicFiltersdata, updatedFilters);
      } else {
        dispatch(
          qhseStoreFilters({ module: "qhse", filters: updatedFilters, filterType: "static" })
        );
      }

      return updatedFilters;
    });
  };
  const handleExport = async (format) => {
    setAnchorEl(null);
    const exportData = {
      project: dynamicFiltersdata[0].selectedValue,
      cardType: staticFiltersdata[1].selectedValue,
      location: dynamicFiltersdata[1].selectedValue,
      category: dynamicFiltersdata[2].selectedValue,
      created:
        staticFiltersdata[0].selectedValue === Common.CUSTOM_RANGE
          ? "custom"
          : staticFiltersdata[0].selectedValue.toLowerCase().replace(/ /g, "_"),
      status: staticFiltersdata[2].selectedValue,
      format: format === "pdf" ? "pdf" : "excel",
    };

    // Add date parameters if Custom Range is selected
    if (staticFiltersdata[0].selectedValue === Common.CUSTOM_RANGE) {
      exportData.fromDate = staticFiltersdata[0].fromDate;
      exportData.toDate = staticFiltersdata[0].toDate;
    }

    const data = new URLSearchParams(exportData);
    const currentDate = new Date();

    const filename = `Reynard_safety_card_${currentDate.getFullYear()}-${
      currentDate.getMonth() + 1
    }-${currentDate.getDate()}_${currentDate.getHours()}-${currentDate.getMinutes()}-${currentDate.getSeconds()}.${format}`;

    const res = await dispatch(exportSafetyCardThunk(data));
    const url = window.URL.createObjectURL(res.payload);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
  };

  const handleReset = async () => {
    const resetStaticFiltersdata = staticFiltersdata.map((filter) => ({
      ...filter,
      selectedValue:
        filter?.inputLabel === "Created"
          ? filter.list[0]
          : filter?.list?.map((item) => item[mongooseId]),
      fromDate: null,
      toDate: null,
    }));
    const resetDynamicFiltersdata = dynamicFiltersdata.map((filter) => {
      let defaultValue = filter.list[0]?.[mongooseId];

      if (filter.inputLabel === "Severity" || filter.inputLabel === "Likelihood") {
        defaultValue = filter.list?.map((item) => item[mongooseId]);
      }

      if (filter.list[0]?.title === FiltersModuleName.NO_DATA_FOUND) {
        return {
          ...filter,
          selectedValue: FiltersModuleName.ALL_IN_SMALL_CASE,
          list: [
            {
              [Constants.MONGOOSE_ID]: FiltersModuleName.ALL_IN_SMALL_CASE,
              title: FiltersModuleName.ALL_WITH_FIRST_LETTER_CAPITAL,
            },
          ],
        };
      }

      return {
        ...filter,
        selectedValue: defaultValue,
      };
    });
    dispatch(resetFilters({ module: "qhse" }));
    setStaticFiltersdata(resetStaticFiltersdata);
    setDynamicFiltersdata(resetDynamicFiltersdata);
    await handleFilter(resetDynamicFiltersdata, resetStaticFiltersdata);
  };

  const handleTablePagination = async () => {
    const data = {
      page: next + 1,
      perPage: tablePagination.perPage,
      project: dynamicFiltersdata[0].selectedValue,
      cardType: staticFiltersdata[1].selectedValue,
      location: dynamicFiltersdata[1].selectedValue,
      category: dynamicFiltersdata[2].selectedValue,
      created:
        staticFiltersdata[0]?.selectedValue === Common.CUSTOM_RANGE
          ? "custom"
          : staticFiltersdata[0]?.selectedValue.toLowerCase().replace(/ /g, "_"),
      status: staticFiltersdata[2].selectedValue,
    };
    if (staticFiltersdata[0]?.selectedValue === Common.CUSTOM_RANGE) {
      data.fromDate = staticFiltersdata[0].fromDate;
      data.toDate = staticFiltersdata[0].toDate;

      // Skip API call if Custom Range is selected but either date is missing
      if (!data.fromDate || !data.toDate) {
        return;
      }
    }
    const res = await dispatch(filterThunk(paramCreater(data)));
    await dispatch(loadSafetyCardData(res.payload.data.qhseData));
    setNext(res.payload.data.qhseData.length > 0 ? next + 1 : next);
  };

  const handleDelete = async () => {
    const res = await dispatch(deleteSafetyCardThunk(deleteData.id));
    if (res.payload.status === 200) {
      dispatch(
        openSnackbar({
          message: Constants.SAFETY_CARD_DELETE_SUCCESS,
          notificationType: Constants.NOTIFICATION_SUCCESS,
        })
      );
      await dispatch(removeSafetyCard(deleteData.id));
    } else {
      dispatch(
        openSnackbar({
          message: Constants.SAFETY_CARD_DELETE_ERROR,
          status: Constants.NOTIFICATION_ERROR,
        })
      );
    }
    setDeleteData({ ...deleteData, open: false });
  };

  // Realod Table Data
  const handleReload = async () => {
    await dispatch(reloadData());
    await dispatch(configThunk());

    handleFilter(dynamicFiltersdata, staticFiltersdata);
  };
  const safetyCardArray = [
    {
      title: ButtonTitles.NEW_SAFE,
      permission: LicensePermission.SAFE_CARD,
      icon: Icons.NEW,
      background: Colors.SUCCESS,
      color: Colors.WHITE,
      openModal: handleOpenSafeDrawer,
    },
    {
      title: ButtonTitles.NEW_UNSAFE,
      permission: LicensePermission.UNSAFE_CARD,
      icon: Icons.NEW,
      background: Colors.ERROR,
      color: Colors.WHITE,
      openModal: handleOpenUnsafeDrawer,
    },
    {
      title: ButtonTitles.NEW_NCR,
      permission: LicensePermission.NCR_CARD,
      icon: Icons.NEW,
      background: Colors.PRIMARY,
      color: Colors.WHITE,
      openModal: handleOpenNcrDrawer,
    },
    {
      title: ButtonTitles.NEW_INCIDENT,
      permission: LicensePermission.INCIDENT_CARD,
      icon: Icons.NEW,
      background: Colors.PRIMARY,
      color: Colors.WHITE,
      openModal: handleOpenIncidentDrawer,
    },
  ];

  const convertStringToArray = (value) => {
    if (typeof value === "string") {
      return [value.toLowerCase()];
    }
    return value;
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox
        display="flex"
        flexDirection={{ md: "row", sm: "column" }}
        justifyContent={{ md: "space-between" }}
        alignItems={{ lg: "space-between", sm: "center" }}
      >
        <PageTitle title={PageTitles.SAFETY_CARD} />
        <MDBox mt={{ lg: 0, sm: 2 }} display="flex" flexWrap="wrap">
          {safetyCardArray.map((item) => {
            const permissionExists = permissions.some(
              (per) => per.permission.name === item.permission
            );
            const screen = screens.find((screenbutton) =>
              screenbutton.name.includes(item.permission)
            );

            const showButton = screen?.screensInfo.agreement?.create && permissionExists;

            if (showButton) {
              return (
                <CustomButton
                  key={item.title}
                  title={item.title}
                  icon={item.icon}
                  background={item.background}
                  color={item.color}
                  openModal={item.openModal}
                />
              );
            }

            return null;
          })}
          <Divider
            orientation="vertical"
            sx={{
              backgroundColor: "var(--gray-300, #D0D5DD)",
              height: "auto",
              marginLeft: pxToRem(16),
              marginRight: 0,
            }}
          />
          <BasicButton
            icon={Icons.RELOAD}
            background={Colors.WHITE}
            border
            color={Colors.BLACK}
            action={handleReload}
          />
        </MDBox>
      </MDBox>
      <Divider sx={{ marginTop: pxToRem(22) }} />
      <MDBox display="flex wrap" justifyContent="space-between" mx={0}>
        {dynamicFiltersdata?.map((val) => {
          const isProjectOrLocation =
            val?.inputLabel === "Project" ||
            val?.inputLabel === "Location" ||
            val?.inputLabel === "Category";
          return isProjectOrLocation ? (
            <FormControl
              key={val?.inputLabel}
              variant="standard"
              size="medium"
              style={{ marginTop: "26px", width: 200, marginRight: "15px" }}
            >
              <CustomAutoComplete
                label={val?.inputLabel}
                name={val?.inputLabel}
                id={val?.inputLabel}
                getOptionLabel={(option) => option.title || ""}
                menu={val?.list}
                value={{
                  title:
                    val?.list.find((item) => item[Constants.MONGOOSE_ID] === val?.selectedValue)
                      ?.title || "",
                }}
                handleChange={handleDynamicFilter}
                valueStyle={{
                  backgroundColor: Colors.WHITE,
                  height: pxToRem(40),
                  verticalMarginTop: pxToRem(4),
                  menuWidth: 400,
                  inputWidth: 250,
                  padding: pxToRem(1),
                }}
                labelStyle={{
                  fontSize: pxToRem(14),
                  fontWeight: 600,
                  color: Colors.BLACK,
                }}
                disabled={
                  (val?.inputLabel === "Location" &&
                    dynamicFiltersdata[0]?.selectedValue === "all") ||
                  (val?.inputLabel === "Location" &&
                    dynamicFiltersdata[1]?.list[0].title === "No data found")
                }
              />
            </FormControl>
          ) : (
            <FormControl
              key={val?.inputLabel}
              variant="standard"
              size="medium"
              style={{ marginTop: "10px", marginRight: "15px" }}
            >
              <MultiSelectDropdown
                id={val.inputLabel}
                label={val.inputLabel}
                name={val.inputLabel}
                defaultValue={val?.selectedValue}
                menu={val.list}
                values={val?.selectedValue}
                labelStyle={{ fontWeight: 600 }}
                handleChange={(name, value) => handleDynamicFilter({ target: { name, value } })}
                showBadge
                valueStyle={{
                  backgroundColor: Colors.WHITE,
                  height: pxToRem(40),
                  verticalMarginTop: pxToRem(4),
                  menuWidth: 310,
                  inputWidth: 180,
                }}
              />
            </FormControl>
          );
        })}
        {(qhseStoredFilters?.staticFilters?.length > 0
          ? qhseStoredFilters?.staticFilters
          : staticFiltersdata
        )?.map((val) => {
          const isCreated = val?.inputLabel === "Created";
          return isCreated ? (
            <FormControl
              key={val?.inputLabel}
              variant="standard"
              size="medium"
              style={{ marginTop: "20px", marginRight: "0px" }}
            >
              <CustomFilterDropdown
                key={val?.inputLabel}
                filterKey={val.inputLabel}
                label={val.inputLabel === "Created" ? "Date" : val?.inputLabel}
                name={val.inputLabel}
                defaultValue={val?.selectedValue}
                value={val?.selectedValue}
                handleChange={handleStaticFilter}
                menu={val.list}
                fromDate={val.fromDate}
                toDate={val.toDate}
                handleCustomDateChange={handleCustomDateChange}
              />
            </FormControl>
          ) : (
            <FormControl
              key={val?.inputLabel}
              variant="standard"
              size="medium"
              style={{ marginTop: "10px", marginRight: "15px" }}
            >
              <MultiSelectDropdown
                id={val.inputLabel}
                label={val.inputLabel}
                name={val.inputLabel}
                defaultValue={convertStringToArray(val?.selectedValue)}
                menu={val.list}
                values={convertStringToArray(val?.selectedValue)}
                labelStyle={{ fontWeight: 600 }}
                handleChange={(name, value) => handleStaticFilter({ target: { name, value } })}
                showBadge
                valueStyle={{
                  backgroundColor: Colors.WHITE,
                  height: pxToRem(40),
                  verticalMarginTop: pxToRem(4),
                  menuWidth: 310,
                  inputWidth: 180,
                }}
              />
            </FormControl>
          );
        })}
        <Button
          sx={{
            mr: 2,
            mt: pxToRem(45),
            ml: 0,
            mb: 0,
            minWidth: 150,
            backgroundColor: "#fff",
            "&:hover": {
              backgroundColor: "#fff",
            },
            fontSize: pxToRem(14),
            textTransform: "capitalize",
            alignSelf: "flex-end",
          }}
          variant="outlined"
          color="info"
          onClick={handleReset}
          startIcon={Icons.RESET_FILTER}
        >
          {ButtonTitles.RESET_FILTER}
        </Button>
        <Button
          sx={{
            m: 2,
            mt: pxToRem(45),
            ml: 0,
            mb: 0,
            backgroundColor: "#fff",
            "&:hover": {
              backgroundColor: "#fff",
            },
            fontSize: pxToRem(14),
            textTransform: "capitalize",
            width: pxToRem(130),
          }}
          variant="outlined"
          color="info"
          onClick={handleOpenExportMenu}
          startIcon={Icons.EXPORT}
        >
          {ButtonTitles.EXPORT}
        </Button>
      </MDBox>

      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseExportMenu}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        <MenuItem onClick={() => handleExport("xlsx")}>CSV</MenuItem>
        <MenuItem onClick={() => handleExport("pdf")}>PDF</MenuItem>
      </Menu>

      <MDBox mt={3} mb={3}>
        <Card sx={{ boxShadow: "none", height: "auto" }}>
          <DataTable
            table={{ columns, rows }}
            isSorted={false}
            entriesPerPage={{ defaultValue: defaultData.PER_PAGE }}
            showTotalEntries={false}
            noEndBorder
            loading={safetyCards.loading}
            licenseRequired
            currentPage={tablePagination.page}
            handleTablePagination={handleTablePagination}
            handleCurrentPage={(page) => setTablePagination({ ...tablePagination, page })}
          />
        </Card>
      </MDBox>

      {/* Safe card Form */}
      {openSafeModal && (
        <SafetyCardForm
          screenIndex={0}
          cardType="Safe"
          openSafetyModal={openSafeModal}
          setOpenSafetyModal={setOpenSafeModal}
          bgColor="#029E3B"
          handleReset={handleReset}
        />
      )}

      {/* Unsafe card Form */}
      {openUnsafeModal && (
        <SafetyCardForm
          screenIndex={1}
          cardType="Unsafe"
          openSafetyModal={openUnsafeModal}
          setOpenSafetyModal={setOpenUnsafeModal}
          bgColor="#9D0202"
          handleReset={handleReset}
        />
      )}

      {/* NCR Card Form */}
      {openNCRModal && (
        <SafetyCardForm
          screenIndex={2}
          cardType="NCR"
          openSafetyModal={openNCRModal}
          setOpenSafetyModal={setOpenNCRModal}
          bgColor="#191A51"
          handleReset={handleReset}
        />
      )}

      {/* Incident Card Form */}
      {openIncidentModal && (
        <SafetyCardForm
          screenIndex={3}
          cardType="Incident"
          openSafetyModal={openIncidentModal}
          setOpenSafetyModal={setOpenIncidentModal}
          bgColor="#191A51"
          handleReset={handleReset}
        />
      )}

      {/* Update Card Modal */}
      {updateQhseAnchor && (
        <QhseUpdateCardDrawer
          selectedCardId={updateSafetyCardId}
          safetyCardData={updateSafetyCard}
          updateQhseAnchor={updateQhseAnchor}
          setUpdateQhseAnchor={setUpdateQhseAnchor}
          // closeDrawer={handleCloseQhseUpdateDrawer}
        />
      )}
      {safeAnchor.right && (
        <QhseCardDrawer
          qhseAnchor={safeAnchor}
          screenIndex={0}
          cardType="Safe"
          bgColor="#029E3B"
          closeDrawer={handleCloseSafeDrawer}
          handleReset={handleReset}
        />
      )}
      {unSafeAnchor.right && (
        <QhseCardDrawer
          qhseAnchor={unSafeAnchor}
          cardType="Unsafe"
          screenIndex={1}
          bgColor="#9D0202"
          closeDrawer={handleCloseUnsafeDrawer}
          handleReset={handleReset}
        />
      )}
      {ncrAnchor.right && (
        <QhseCardDrawer
          qhseAnchor={ncrAnchor}
          cardType="NCR"
          screenIndex={2}
          bgColor="#191A51"
          closeDrawer={handleCloseNcrDrawer}
          handleReset={handleReset}
        />
      )}
      {incidentAnchor.right && (
        <QhseCardDrawer
          qhseAnchor={incidentAnchor}
          cardType="Incident"
          screenIndex={3}
          bgColor="#191A51"
          closeDrawer={handleCloseIncidentDrawer}
          handleReset={handleReset}
        />
      )}
      {viewQhseAnchor.right && (
        <QhseViewCardDrawer
          viewQhseAnchor={viewQhseAnchor}
          qhseViewId={qhseViewId}
          setQhseAnchor={setViewQhseAnchor}
          // closeDrawer={handleCloseQhseDetailDrawer}
        />
      )}
      {/* Delete modal for safetry card */}
      {deleteData.open && (
        <DeleteModal
          open={deleteData.open}
          title={deleteData.title}
          message={deleteData.message}
          handleClose={() => setDeleteData({ ...deleteData, open: false })}
          handleDelete={handleDelete}
        />
      )}
    </DashboardLayout>
  );
}

export default ExportHOC(Dashboard);

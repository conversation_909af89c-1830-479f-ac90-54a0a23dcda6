import { useEffect, useState } from "react";

// Components
import Author from "components/Table/Author";

function EquipmentData(equipmentDprList) {
  const [equipmentRows, setEquipmentRows] = useState([]);

  useEffect(() => {
    if (equipmentDprList) {
      const list = equipmentDprList.map((item, index) => {
        const temp = {
          srNo: <Author name={index + 1} />,
          equipmentType: <Author name={item?.equipmentType} />,
          equipmentName: <Author name={item?.equipmentName} />,
          equipmentNumber: <Author name={item?.equipmentNumber} />,
          serialNumber: <Author name={item?.serialNumber} />,
          orderNo: <Author name={item?.orderNo} />,
        };
        return temp;
      });
      setEquipmentRows([...list]);
    }
  }, [equipmentDprList]);

  return {
    equipmentColumns: [
      { Header: "No.", accessor: "srNo", width: "10%" },
      { Header: "Product Type", accessor: "equipmentType", width: "10%" },
      { Header: "Product Name", accessor: "equipmentName", align: "left", width: "30%" },
      { Header: "Product Number", accessor: "equipmentNumber", align: "left", width: "20%" },
      { Header: "Serial Number", accessor: "serialNumber", align: "left", width: "30%" },
      { Header: "Order Number", accessor: "orderNo", width: "20%", align: "left" },
    ],

    equipmentRows,
  };
}

export default EquipmentData;

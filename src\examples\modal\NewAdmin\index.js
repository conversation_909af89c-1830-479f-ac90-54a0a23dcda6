import React, { useState } from "react";

// MUI components
import {
  CircularProgress,
  FormControl,
  Grid,
  Icon,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  TextField,
  IconButton,
  FormHelperText,
  InputAdornment,
} from "@mui/material";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";

// Custom components
import ImageUpload from "components/ImageUpload/imageUpload";
import MDBox from "components/MDBox";
import MDButton from "components/MDButton";
import MDTypography from "components/MDTypography";
import License from "examples/modal/NewAdmin/License/License";
import ModalTitle from "examples/NewDesign/ModalTitle";

// Functions
import pxToRem from "assets/theme/functions/pxToRem";

// Redux
import { useDispatch, useSelector } from "react-redux";
import { CreateClientThunk } from "redux/Thunks/SuperAdmin";
import { openSnackbar } from "redux/Slice/Notification";

// Constants
import Constants, { Icons, defaultData } from "utils/Constants";
import Validations from "utils/Validations/index";

// 3rd party lib
import PhoneInput from "react-phone-input-2";
import countries from "countries-list";
import PropTypes from "prop-types";

// Styles
import style from "assets/style/Modal";
import "react-dropzone-uploader/dist/styles.css";
import "react-phone-input-2/lib/style.css";

const countryList = Object.values(countries.countries).map((country) => country.name);

function NewAdmin({ open, handleClose, fetchClientList }) {
  const [values, setValues] = useState({
    email: "",
    password: "",
    callingName: "",
    firstName: "",
    lastName: "",
    nationality: "",
    companyName: "",
    companyLogo: "",
    address: "",
    contactNumber: {
      number: "",
      code: defaultData.DEFAULT_PHONE_CODE,
      in: defaultData.DEFAULT_PHONE_IN,
    },
    emergencyContactNumber: {
      number: "",
      code: defaultData.DEFAULT_PHONE_CODE,
      in: defaultData.DEFAULT_PHONE_IN,
    },
  });

  const [errors, setErrors] = useState({});
  const [licenses, setLicenses] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const licenseList = useSelector((state) => state.License.allLicense);
  const [showPassword, setShowPassword] = useState(false);
  const handleShowPasswordClick = () => {
    setShowPassword(!showPassword);
  };
  const dispatch = useDispatch();

  const handlePhoneNumberChange = (num, country, type) => {
    setValues({
      ...values,
      [type]: {
        number: num.substring(country.dialCode.length),
        code: country.countryCode.toUpperCase(),
        in: `+${country.dialCode}`,
      },
    });
  };

  const handleImageChange = (name, imageValues) => {
    setValues({
      ...values,
      [name]: imageValues?.[0]?.url || "",
    });
  };
  const handleImageCancel = (fieldName) => {
    setValues({
      ...values,
      [fieldName]: "",
    });
  };
  const validate = () => {
    const emailValidate = Validations.validate("email", values.email, null, null, false);
    const passwordValidate = Validations.validate("password", values.password, 6, 30, true);
    const imageError = Validations.validate("basic", values.companyLogo);
    const newErrors = {};

    if (!values) {
      return false;
    }

    if (!values.email) {
      newErrors.email = "Email is required";
    } else if (emailValidate) {
      newErrors.email = Constants.EMAIL_NOT_VALID;
    }

    if (!values.callingName) {
      newErrors.callingName = "Usual First Name is required";
    } else if (values.callingName.trim() === "") {
      newErrors.callingName = Constants.INVALID_SPACE;
    }

    if (!values.firstName) {
      newErrors.firstName = "First name is required";
    } else if (values.firstName.trim() === "") {
      newErrors.firstName = Constants.INVALID_SPACE;
    }

    if (!values.lastName) {
      newErrors.lastName = "Last name is required";
    } else if (values.lastName.trim() === "") {
      newErrors.lastName = Constants.INVALID_SPACE;
    }

    if (!values.companyName) {
      newErrors.companyName = "Company name is required";
    } else if (values.companyName.trim() === "") {
      newErrors.companyName = Constants.INVALID_SPACE;
    }

    if (imageError !== "") newErrors.companyLogo = imageError;
    if (!values.password) {
      newErrors.password = Constants.PASSWORD_REQUIRED;
    } else if (passwordValidate) {
      newErrors.password = passwordValidate;
    }

    if (!values.nationality) {
      newErrors.nationality = "Nationality is required";
    }

    if (!licenses.length > 0) {
      newErrors.AccountLicence = "At least 1 license is required";
    }
    if (!values.address) {
      newErrors.address = "Address is required";
    } else if (values.address.trim() === "") {
      newErrors.address = Constants.INVALID_SPACE;
    }

    if (values.contactNumber.number === "" || values.contactNumber.length < 5) {
      newErrors.contactNumber = "Contact number is required";
    }

    setErrors(newErrors);
    return Object.values(newErrors).filter((val) => val !== "").length === 0;
  };
  const handleResetModal = () => {
    setErrors("");
    handleClose();
  };

  const handleChange = (e) => {
    setValues({
      ...values,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const val = validate();
    if (val) {
      setIsSubmitting(true);
      const body = {
        name: values.companyName.trim(),
        callingName: values.callingName.trim(),
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        email: values.email.trim(),
        contactNumber: values.contactNumber,
        ...(values.emergencyContactNumber.number !== "" && {
          emergencyContactNumber: values.emergencyContactNumber,
        }),
        password: values.password,
        logo: values.companyLogo,
        AccountLicence: licenses,
        nationality: values.nationality,
        address: values.address,
      };
      setIsSubmitting(false);
      const res = await dispatch(CreateClientThunk(body));
      if (res.payload.status === 200) {
        handleResetModal();
        dispatch(openSnackbar({ message: res.payload.data.message, notificationType: "success" }));
        fetchClientList();
      } else if (res.payload.status === 422) {
        const newError = res.payload.data.data.error;
        let tempError = {};
        newError.forEach((item) => {
          tempError = { ...tempError, ...item };
        });
        setErrors(tempError);
      } else if (res.payload.status === 401) {
        const temp = {};
        temp.email = Constants.EMAIL_EXIST;
        setErrors(temp);
      } else if (res.payload.status === 400) {
        const temp = {};
        temp.companyName = Constants.Company_EXIST;
        setErrors(temp);
      }
    }
    setLoading(false);
  };
  return (
    <MDBox>
      <Modal
        open={open}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <MDBox sx={style}>
          <MDBox
            bgColor="info"
            p={3}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            borderRadius="lg"
            sx={{ borderBottomRightRadius: 0, borderBottomLeftRadius: 0, height: pxToRem(72) }}
          >
            <ModalTitle title="New Company" color="white" />
            <Icon
              sx={{ cursor: "pointer", color: "beige" }}
              fontSize="medium"
              onClick={handleResetModal}
            >
              {Icons.CROSS}
            </Icon>
          </MDBox>
          {licenseList.length > 0 ? (
            <MDBox
              display="flex"
              flexDirection="column"
              justifyContent="space-between"
              px={3}
              py={2}
              sx={{
                maxHeight: 500,
                overflowY: "scroll",
                "::-webkit-scrollbar": { display: "none" },
                scrollbarWidth: "none",
              }}
            >
              <TextField
                sx={{ marginBottom: 2 }}
                name="callingName"
                label="Usual First Name*"
                value={values.callingName}
                onChange={handleChange}
                error={Boolean(errors.callingName)}
                helperText={errors.callingName}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <TextField
                sx={{ marginBottom: 2 }}
                name="firstName"
                label="First Name (as per Passport)*"
                value={values.firstName}
                onChange={handleChange}
                error={Boolean(errors.firstName)}
                helperText={errors.firstName}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <TextField
                sx={{ marginBottom: 2 }}
                name="lastName"
                label="Last Name (as per Passport)*"
                value={values.lastName}
                onChange={handleChange}
                error={Boolean(errors.lastName)}
                helperText={errors.lastName}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <TextField
                sx={{ marginBottom: 2 }}
                name="email"
                label="Email*"
                value={values.email}
                onChange={handleChange}
                error={Boolean(errors.email)}
                helperText={errors.email}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <TextField
                sx={{ marginBottom: 2 }}
                name="password"
                label="Password*"
                type={showPassword ? "text" : "password"}
                value={values.password}
                onChange={handleChange}
                error={Boolean(errors.password)}
                helperText={errors.password}
                margin="normal"
                fullWidth
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={handleShowPasswordClick}>
                        {showPassword ? <VisibilityOutlinedIcon /> : <VisibilityOffOutlinedIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <FormControl
                variant="outlined"
                fullWidth
                error={Boolean(errors.nationality)}
                sx={{ marginTop: 2 }}
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              >
                <InputLabel id="nationality-label">Country*</InputLabel>
                <Select
                  name="nationality"
                  labelId="nationality"
                  id="nationality-select"
                  value={values.nationality}
                  onChange={handleChange}
                  sx={{
                    color: "black",
                    backgroundColor: "black",
                    paddingY: "0.65rem",
                    maxHeight: 100,
                    marginBottom: 2,
                  }}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        height: 200,
                        width: 635,
                        opacity: 1,
                        transform: "none",
                        top: 183,
                        left: 442,
                      },
                    },
                  }}
                >
                  {countryList.map((item) => (
                    <MenuItem value={item} id={item} key={item}>
                      {item}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText sx={{ marginLeft: 0 }}>{errors.nationality}</FormHelperText>
              </FormControl>
              <TextField
                sx={{ marginBottom: 2 }}
                name="companyName"
                label="Company Name*"
                value={values.companyName}
                onChange={handleChange}
                error={Boolean(errors.companyName)}
                helperText={errors.companyName}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <MDBox>
                <ImageUpload
                  label="Company Logo*"
                  name="companyLogo"
                  onImageUpload={(imageValues) => handleImageChange("companyLogo", imageValues)}
                  onImageCancel={(updatedImageUrl) =>
                    handleImageCancel("companyLogo", updatedImageUrl)
                  }
                  data={[
                    values?.companyLogo && {
                      name: "",
                      url: values?.companyLogo,
                      size: 0,
                    },
                  ]}
                  type="Company_Registration"
                  formats={["image/jpeg", "image/jpg", "image/png", "image/svg+xml"]}
                  acceptType="image/*"
                  maxImageCount={1}
                  error={Boolean(errors?.companyLogo)}
                  helperText={errors?.companyLogo}
                  resetComponent={values?.companyLogo === ""}
                  imageTypeError={Constants.IMAGE_FILE_TYPE_NOT_ALLOWED}
                />
              </MDBox>
              <License licenseList={licenseList} Licenses={licenses} setLicense={setLicenses} />

              <MDTypography variant="caption" color="error">
                {errors?.AccountLicence}
              </MDTypography>

              <TextField
                sx={{ marginBottom: 2 }}
                name="address"
                multiline
                label="Address*"
                rows={3}
                value={values.address}
                onChange={handleChange}
                error={Boolean(errors.address)}
                helperText={errors.address}
                margin="normal"
                fullWidth
                FormHelperTextProps={{
                  sx: { marginLeft: 0 },
                }}
              />
              <MDBox display="flex" flexWrap="wrap" justifyContent="space-between">
                <MDBox>
                  <MDTypography variant="caption" ml={0}>
                    Contact Number*
                  </MDTypography>
                  <PhoneInput
                    country="us"
                    value={values.contactNumber.in + values.contactNumber.number}
                    onChange={(num, country) =>
                      handlePhoneNumberChange(num, country, "contactNumber")
                    }
                    style={{ width: "100%" }}
                  />
                  <MDTypography variant="caption" color="error" sx={{ marginTop: 2 }}>
                    {errors?.contactNumber}
                  </MDTypography>
                </MDBox>
                <MDBox>
                  <MDTypography variant="caption" ml={0}>
                    Emergency Contact number
                  </MDTypography>
                  <PhoneInput
                    country="us"
                    value={values.emergencyContactNumber.in + values.emergencyContactNumber.number}
                    onChange={(num, country) =>
                      handlePhoneNumberChange(num, country, "emergencyContactNumber")
                    }
                    style={{ width: "100%" }}
                  />
                  <MDTypography variant="caption" color="error" sx={{ marginTop: 2 }}>
                    {errors?.emergencyContactNumber}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          ) : (
            <MDBox py={5} display="flex" justifyContent="center" alignItems="center">
              <CircularProgress color="info" />
            </MDBox>
          )}
          <MDBox px={0} mb={2} ml={2}>
            <Grid container direction="row" justifyContent="flex-end" alignItems="center">
              <Grid item xs={2}>
                <MDButton
                  variant="contained"
                  color={isSubmitting ? "secondary" : "info"}
                  disabled={isSubmitting}
                  onClick={handleSubmit}
                  style={{ textTransform: "none", boxShadow: "none" }}
                >
                  {loading ? "Loading..." : "Submit"}
                </MDButton>
              </Grid>
            </Grid>
          </MDBox>
        </MDBox>
      </Modal>
    </MDBox>
  );
}

// Setting default values for the props of NewAdmin
NewAdmin.defaultProps = {
  open: false,
  handleClose: () => {},
};

// Typechecking props for the NewAdmin
NewAdmin.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  fetchClientList: PropTypes.func.isRequired,
};

export default NewAdmin;
